import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitTableRating1750839141391 implements MigrationInterface {
  name = 'InitTableRating1750839141391';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "review_tags" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100) NOT NULL, "display_order" integer NOT NULL DEFAULT '0', "active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_cf85590d0d14748548f63f4c407" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_d85d9cf116ef2899c1527e0161" ON "review_tags" ("active") `);
    await queryRunner.query(
      `CREATE TABLE "mapping_restaurant_review_tags" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "restaurant_review_id" uuid NOT NULL, "review_tag_id" uuid NOT NULL, CONSTRAINT "PK_491eba079fc5e193b6b59e00570" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cff991e11a4ba656fb30f58e50" ON "mapping_restaurant_review_tags" ("restaurant_review_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a8209057d1a2c75464d22765a" ON "mapping_restaurant_review_tags" ("review_tag_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_8ec69ba5e3c0958dff0f6f71cd" ON "mapping_restaurant_review_tags" ("restaurant_review_id", "review_tag_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "restaurant_review_replies" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "restaurant_review_id" uuid NOT NULL, "merchant_staff_id" uuid, "user_id" uuid, "comment" text NOT NULL, CONSTRAINT "PK_625bb135f1523a7b97983187164" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8ceccd31f4e29422b4a07bc527" ON "restaurant_review_replies" ("restaurant_review_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_88781f54067d681e71c89b159c" ON "restaurant_review_replies" ("merchant_staff_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f2651c1fc5cda6c248cf9a9600" ON "restaurant_review_replies" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d57c8458bc9f4d1b1ce0279e08" ON "restaurant_review_replies" ("restaurant_review_id", "created_at") `,
    );
    await queryRunner.query(
      `CREATE TABLE "restaurant_reviews" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, "order_id" uuid NOT NULL, "rating" integer NOT NULL, "comment" text, CONSTRAINT "PK_b8103daf77b1e7dfd2cfa3ba73a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b02e9a1d9c5bb702c399c64532" ON "restaurant_reviews" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_a1f816622fc3b59d1f2e752134" ON "restaurant_reviews" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_ab8ab3f2d51d7fa980975b44cb" ON "restaurant_reviews" ("order_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_1203311ef704aec0de93518b1f" ON "restaurant_reviews" ("rating") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_052c061b71591c68b3db96702d" ON "restaurant_reviews" ("user_id", "order_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_43f343e9f7ae36dcad50b06431" ON "restaurant_reviews" ("user_id", "restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d0218619c7cb8b730fec7b8345" ON "restaurant_reviews" ("restaurant_id", "created_at") `,
    );
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "total_reviews" integer NOT NULL DEFAULT '0'`);
    await queryRunner.query(`CREATE INDEX "IDX_54b296fa045ada3fc08af06bb8" ON "restaurants" ("total_reviews") `);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD CONSTRAINT "FK_cff991e11a4ba656fb30f58e508" FOREIGN KEY ("restaurant_review_id") REFERENCES "restaurant_reviews"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD CONSTRAINT "FK_3a8209057d1a2c75464d22765a6" FOREIGN KEY ("review_tag_id") REFERENCES "review_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurant_review_replies" ADD CONSTRAINT "FK_8ceccd31f4e29422b4a07bc5272" FOREIGN KEY ("restaurant_review_id") REFERENCES "restaurant_reviews"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurant_review_replies" ADD CONSTRAINT "FK_88781f54067d681e71c89b159c7" FOREIGN KEY ("merchant_staff_id") REFERENCES "merchant_staff"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurant_reviews" ADD CONSTRAINT "FK_b02e9a1d9c5bb702c399c645329" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurant_reviews" ADD CONSTRAINT "FK_a1f816622fc3b59d1f2e7521347" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurant_reviews" ADD CONSTRAINT "FK_ab8ab3f2d51d7fa980975b44cb3" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurant_reviews" DROP CONSTRAINT "FK_ab8ab3f2d51d7fa980975b44cb3"`);
    await queryRunner.query(`ALTER TABLE "restaurant_reviews" DROP CONSTRAINT "FK_a1f816622fc3b59d1f2e7521347"`);
    await queryRunner.query(`ALTER TABLE "restaurant_reviews" DROP CONSTRAINT "FK_b02e9a1d9c5bb702c399c645329"`);
    await queryRunner.query(`ALTER TABLE "restaurant_review_replies" DROP CONSTRAINT "FK_88781f54067d681e71c89b159c7"`);
    await queryRunner.query(`ALTER TABLE "restaurant_review_replies" DROP CONSTRAINT "FK_8ceccd31f4e29422b4a07bc5272"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" DROP CONSTRAINT "FK_3a8209057d1a2c75464d22765a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" DROP CONSTRAINT "FK_cff991e11a4ba656fb30f58e508"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_54b296fa045ada3fc08af06bb8"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "total_reviews"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d0218619c7cb8b730fec7b8345"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_43f343e9f7ae36dcad50b06431"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_052c061b71591c68b3db96702d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1203311ef704aec0de93518b1f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ab8ab3f2d51d7fa980975b44cb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a1f816622fc3b59d1f2e752134"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b02e9a1d9c5bb702c399c64532"`);
    await queryRunner.query(`DROP TABLE "restaurant_reviews"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d57c8458bc9f4d1b1ce0279e08"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2651c1fc5cda6c248cf9a9600"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_88781f54067d681e71c89b159c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8ceccd31f4e29422b4a07bc527"`);
    await queryRunner.query(`DROP TABLE "restaurant_review_replies"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8ec69ba5e3c0958dff0f6f71cd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a8209057d1a2c75464d22765a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cff991e11a4ba656fb30f58e50"`);
    await queryRunner.query(`DROP TABLE "mapping_restaurant_review_tags"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d85d9cf116ef2899c1527e0161"`);
    await queryRunner.query(`DROP TABLE "review_tags"`);
  }
}
