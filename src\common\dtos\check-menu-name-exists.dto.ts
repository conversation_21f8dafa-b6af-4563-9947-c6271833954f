import { IsOptional, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CheckMenuNameExistsDto {
  @ApiProperty({ description: 'ID of the restaurant' })
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Name of the menu' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'ID of the menu to exclude' })
  @IsOptional()
  @IsUUID()
  excludeId?: string;
}

export class NameExistsResponseDto {
  exists: boolean;
}
