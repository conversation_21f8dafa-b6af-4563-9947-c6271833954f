import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { OrdersService } from '../orders.service';
import { AllUserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { ApiEventPattern } from '@/common/enums/event-pattern.enum';

@Controller()
export class OrdersMicroserviceController {
  constructor(private readonly ordersService: OrdersService) {}

  @MessagePattern(ApiEventPattern.ORDER_VALIDATE_ACCESS)
  async validateOrderAccess(@Payload() data: { requestId: string; orderId: string; user: AllUserJwtInfo }) {
    const { requestId, orderId, user } = data;
    try {
      await this.ordersService.verifyAccessAndGetOrder(user, orderId);

      return { requestId, hasAccess: true };
    } catch {
      return { requestId, hasAccess: false };
    }
  }
}
