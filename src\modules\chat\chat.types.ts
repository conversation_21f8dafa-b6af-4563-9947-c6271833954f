import { ChatMessage, MessageStatus, SenderType } from './entities/chat-message.entity';

export interface UserConnectedEventPayload {
  userId: string;
  orderId: string;
  senderType: SenderType;
  restaurantId?: string;
}

export interface ChatEventStatusUpdatePayload {
  userId?: string;
  restaurantId?: string;
  senderType: SenderType;
  status: MessageStatus;
  updatedAt: Date;
}

export interface ChatEventNewMessagePayload {
  conversationId: string;
  orderId: string;
  userId: string;
  restaurantId: string;
  message?: ChatMessage;
  senderType: SenderType;
  senderId?: string;
  status?: string;
  updatedAt?: Date;
}
