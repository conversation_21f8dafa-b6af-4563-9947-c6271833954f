import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('(Ops) Restaurants')
@Controller('staff/restaurants')
@Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
export class StaffRestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Get()
  userFindOne(@User() user: UserMerchantStaffJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.staffFindOne(user.restaurantId);
  }
}
