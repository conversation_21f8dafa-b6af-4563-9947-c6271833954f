import { Between, FindOptionsWhere, In, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { OrderCancelReason, OrderStatus } from '@/modules/orders/constants/order.enums';
import { Order } from '@/modules/orders/entities/order.entity';
import { FCMService } from '@/modules/shared/fcm/fcm.service';
import { FcmRestaurantType, FcmUserType } from '@/modules/shared/fcm/fcm.types';
import { MicroserviceClientService } from '@/modules/shared/microservice/microservice-client.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class OrderSchedulesService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly fcmService: FCMService,
  ) {}

  private readonly logger = new Logger(OrderSchedulesService.name);

  private async updateOrderStatus(
    condition: FindOptionsWhere<Order> | FindOptionsWhere<Order>[],
    partialOrder: QueryDeepPartialEntity<Order>,
    order: Order,
    notificationType?: FcmRestaurantType,
  ) {
    const result = await this.orderRepository.update(condition, partialOrder);
    if (result.affected === 0) return false;
    try {
      const orderUpdated = await this.orderRepository.findOne({ where: { id: order.id as string } });
      if (orderUpdated) {
        void this.microserviceClient.publishOrderStatusUpdate(orderUpdated);

        if (orderUpdated?.status === OrderStatus.CANCELLED) {
          void this.fcmService.sendToUser(order, FcmUserType.ORDER_CANCELLED);
          void this.fcmService.sendToStaff(order, FcmUserType.ORDER_CANCELLED);
        }

        // Send specific restaurant notification if provided
        if (notificationType && orderUpdated) {
          void this.fcmService.sendToStaffWithRestaurantType(orderUpdated, notificationType);
        }
      }
    } catch (e) {
      this.logger.error('Failed to publish order status update', e);
    }
    return true;
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleNewOrdersAfter5MinutesToRemind(): Promise<void> {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const sixMinutesAgo = new Date(Date.now() - 6 * 60 * 1000);

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.NEW,
      createdAt: Between(sixMinutesAgo, fiveMinutesAgo), // Orders created between 5-6 minutes ago
    };

    const orders = await this.orderRepository.find({ where: condition });

    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} new orders to remind staff.`);

    await Promise.all(
      orders.map(async (order) => {
        try {
          await this.fcmService.sendToStaffWithRestaurantType(order, FcmRestaurantType.DONT_MISS_ORDER);
          this.logger.log(`Sent reminder for order ${order.id}`);
        } catch (error) {
          this.logger.error(`Failed to send reminder for order ${order.id}:`, error);
        }
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleNewOrdersAfter20MinutesToCancel(): Promise<void> {
    const twentyMinutesAgo = new Date(Date.now() - 20 * 60 * 1000);

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.NEW,
      createdAt: LessThan(twentyMinutesAgo),
    };

    const orders = await this.orderRepository.find({ where: condition });

    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} new orders to cancel.`);

    await Promise.all(
      orders.map(async (order) => {
        const result = await this.updateOrderStatus(
          { id: order.id, ...condition },
          {
            status: OrderStatus.CANCELLED,
            cancelledAt: new Date(),
            cancelReason: OrderCancelReason.OVERTIME_ACCEPT_NEW_ORDER,
          },
          order,
        );
        if (result) this.logger.log(`Cancelled order ${order.id}`);
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleModifiedOrdersAfter10MinutesToCancel(): Promise<void> {
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.MODIFIED,
      modifiedAt: LessThan(tenMinutesAgo),
    };

    const orders = await this.orderRepository.find({ where: condition });

    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} modified orders to cancel.`);

    await Promise.all(
      orders.map(async (order) => {
        const result = await this.updateOrderStatus(
          { id: order.id, ...condition },
          {
            status: OrderStatus.CANCELLED,
            cancelledAt: new Date(),
            cancelReason: OrderCancelReason.OVERTIME_ACCEPT_MODIFIED_ORDER,
          },
          order,
        );
        if (result) this.logger.log(`Cancelled order ${order.id}`);
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleInKitchenOrdersAfterKitchenEtaAtMinutesToInKitchenOverdue(): Promise<void> {
    const now = new Date();

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.IN_KITCHEN,
      kitchenEta: LessThan(now),
    };

    const orders = await this.orderRepository.find({ where: condition });

    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} in kitchen orders to change status to kitchen overdue.`);

    await Promise.all(
      orders.map(async (order) => {
        const result = await this.updateOrderStatus(
          { id: order.id, ...condition },
          { status: OrderStatus.IN_KITCHEN_OVERDUE },
          order,
          FcmRestaurantType.ORDER_READY_FOR_DELIVERY,
        );
        if (result) this.logger.log(`Updated order ${order.id} to kitchen overdue.`);
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleInDeliveryOrdersAfterDeliveryEtaAtMinutesToInDeliveryOverdue(): Promise<void> {
    const now = new Date();

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.IN_DELIVERY,
      deliveryToEta: LessThan(now),
    };

    const orders = await this.orderRepository.find({ where: condition });
    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} in delivery orders to change status to delivery overdue.`);

    await Promise.all(
      orders.map(async (order) => {
        const result = await this.updateOrderStatus(
          { id: order.id, ...condition },
          { status: OrderStatus.IN_DELIVERY_OVERDUE },
          order,
          FcmRestaurantType.ISSUE_WITH_DELIVERY,
        );
        if (result) this.logger.log(`Updated order ${order.id} to delivery overdue.`);
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleInDeliveryOverdueOrdersAfter30MinutesToDelivered(): Promise<void> {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

    const condition: FindOptionsWhere<Order> = {
      archivedAt: IsNull(),
      status: OrderStatus.IN_DELIVERY_OVERDUE,
      inDeliveryAt: LessThan(thirtyMinutesAgo),
    };

    const orders = await this.orderRepository.find({ where: condition });

    if (!orders.length) return;

    this.logger.log(`Found ${orders.length} in delivery overdue orders to delivered.`);

    await Promise.all(
      orders.map(async (order) => {
        const result = await this.updateOrderStatus(
          { id: order.id, ...condition },
          {
            status: OrderStatus.DELIVERED,
            completedAt: new Date(),
          },
          order,
        );
        if (result) this.logger.log(`Delivered order ${order.id}`);
      }),
    );
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async autoArchiveOrders(): Promise<void> {
    const sixtyMinutesAgo = new Date(Date.now() - 60 * 60 * 1000);

    const ordersToArchive = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.archivedAt IS NULL')
      .andWhere(
        `((order.status = :delivered AND order.completedAt < :date) 
        OR (order.status = :cancelled AND order.cancelledAt < :date) 
        OR (order.status = :unfulfilled AND order.unfulfilledAt < :date))`,
        {
          delivered: OrderStatus.DELIVERED,
          cancelled: OrderStatus.CANCELLED,
          unfulfilled: OrderStatus.UNFULFILLED,
          date: sixtyMinutesAgo,
        },
      )
      .getMany();

    if (ordersToArchive.length > 0) {
      this.logger.log(`Found ${ordersToArchive.length} orders to archive.`);
      const orderIds = ordersToArchive.map((order) => order.id);

      await this.orderRepository.update({ id: In(orderIds) }, { archivedAt: new Date() });

      this.logger.log(`Archived ${orderIds.length} orders.`);
    }
  }
}
