import { Job } from 'bullmq';

import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';

import { EmailService } from './email.service';

export interface SendOtpEmailJob {
  to: string;
  code: string;
  otpExpiryMinutes: number;
  locale?: string;
}

@Processor('email')
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);

  constructor(private emailService: EmailService) {
    super();
  }

  async process(job: Job<SendOtpEmailJob>): Promise<void> {
    const { to, code, otpExpiryMinutes, locale = 'en' } = job.data;

    try {
      this.logger.log(`Processing email job ${job.id} for ${to}`);
      await this.emailService.sendOtpEmail(to, code, otpExpiryMinutes, locale);
      this.logger.log(`Email job ${job.id} completed successfully for ${to}`);
    } catch (error) {
      this.logger.error(`Email job ${job.id} failed for ${to}:`, error);
      throw error; // Let BullMQ handle retries
    }
  }
}
