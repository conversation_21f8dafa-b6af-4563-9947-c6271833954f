import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

@Entity('user_payment_cards')
@Index(['userId', 'cardUid'], { unique: true, where: 'deleted_at IS NULL' })
export class PaymentCard extends BaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'token_number', type: 'varchar', length: 16 })
  tokenNumber: string;

  @Column({ name: 'token_expiry', type: 'varchar', length: 10 })
  tokenExpiry: string;

  @Column({ name: 'masked_card_number', type: 'varchar', length: 20, nullable: true })
  maskedCardNumber?: string | null;

  @Column({ name: 'card_bank', type: 'varchar', length: 16, nullable: true })
  cardBank?: string | null;

  @Column({ name: 'card_uid', type: 'varchar', length: 64 })
  cardUid: string;

  @Column({ name: 'is_default', type: 'boolean', default: false })
  isDefault: boolean;

  // Add relation to User entity
  // @ManyToOne(() => User, user => user.paymentCards)
  // @JoinColumn({ name: 'user_id' })
  // user: User;
}
