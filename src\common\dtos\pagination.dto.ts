import { Type } from 'class-transformer';
import { IsInt, <PERSON>Optional, <PERSON>, <PERSON> } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { MaxPaginationSize } from '../validators/max-pagination-size.validator';
import { SortDto } from './sort.dto';

export class PaginationDto {
  @ApiProperty({ default: 1, required: false })
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @MaxPaginationSize(1_000_000, { message: 'page * limit cannot exceed 1,000,000' })
  @IsOptional()
  page: number = 1;

  @ApiProperty({ default: 10, required: false })
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @Max(1_000)
  @IsOptional()
  limit: number = 10;
}

export abstract class PaginationSortDto extends SortDto {
  @ApiProperty({ default: 1, required: false })
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @MaxPaginationSize(1_000_000, { message: 'page * limit cannot exceed 1,000,000' })
  @IsOptional()
  page: number = 1;

  @ApiProperty({ default: 10, required: false })
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @Max(1_000)
  @IsOptional()
  limit: number = 10;
}
