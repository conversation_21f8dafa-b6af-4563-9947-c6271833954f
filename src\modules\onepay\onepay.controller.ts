import { Request, Response } from 'express';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { Controller, Get, Logger, Query, Req, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Public } from '../auth/decorators/public.decorator';
import { UserType } from '../auth/enums/user-type.enum';
import { UserJwtInfo } from '../auth/types/jwt-payload.type';
import { CallbackCreateTokenOnepay } from './dto/callback-create-token-onepay.dto';
import { OnePayService } from './onepay.service';

@ApiTags('Onepay')
@Controller('onepay')
@Roles({ userType: UserType.USER, role: '*' })
export class OnePayController {
  private readonly logger = new Logger(OnePayController.name);

  constructor(private readonly onePayService: OnePayService) {}

  @Get('create-token')
  createToken(@User() user: UserJwtInfo, @Req() request: Request) {
    return this.onePayService.createToken(user, request);
  }

  @Public()
  @Get('save-token')
  createTokenRaw(@Query() allParams: CallbackCreateTokenOnepay, @Res({ passthrough: true }) res: Response) {
    res.set('Content-Type', 'text/plain');
    return this.onePayService.handleAddPaymentCard(allParams);
  }
}
