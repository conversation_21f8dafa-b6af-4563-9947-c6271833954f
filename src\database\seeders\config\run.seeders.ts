import { config } from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load env variables
config();

// Initialize connection with TypeORM
async function bootstrap() {
  try {
    // Import DataSource to avoid errors when running this file directly
    const { connectionSource } = await import('../../../config/typeorm.config');

    // Ensure connection is initialized
    if (!connectionSource.isInitialized) {
      await connectionSource.initialize();
    }

    // Directory containing all seeders (parent directory)
    const seedersDir = path.join(__dirname, '..');
    const configDir = __dirname;

    // Get all files in the seeders directory (except config files)
    const seederFiles = fs
      .readdirSync(seedersDir)
      .filter(
        (file) =>
          (file.endsWith('.seeder.ts') || file.endsWith('.seeder.js')) &&
          !file.startsWith('index') &&
          path.join(seedersDir, file) !== configDir,
      );

    // Execute each seeder
    for (const file of seederFiles) {
      const seederPath = path.join(seedersDir, file);

      // Import dynamic seeder class
      const seederModule = await import(seederPath);
      const SeederClass = Object.values(seederModule)[0] as any;

      // Create seeder instance and execute
      const seeder = new SeederClass(connectionSource);
      await seeder.execute();
    }

    console.log('All seeders executed successfully.');
    process.exit(0);
  } catch (error) {
    console.error('Error executing seeders:', error);
    process.exit(1);
  }
}

// Call bootstrap function and catch errors
void bootstrap().catch((error) => {
  console.error('Error initializing seeders:', error);
  process.exit(1);
});
