import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';

import { RestaurantReview } from './restaurant-review.entity';

@Entity('restaurant_review_replies')
@Index(['restaurantReviewId', 'createdAt'])
export class RestaurantReviewReply extends BaseEntity {
  @Index()
  @Column({ name: 'restaurant_review_id', type: 'uuid' })
  restaurantReviewId: string;

  @Index()
  @Column({ name: 'merchant_staff_id', type: 'uuid', nullable: true })
  merchantStaffId: string;

  @Index()
  @Column({ name: 'user_id', type: 'uuid', nullable: true })
  userId: string;

  @Column({ type: 'text' })
  comment: string;

  @ManyToOne(() => RestaurantReview)
  @JoinColumn({ name: 'restaurant_review_id' })
  restaurantReview?: WrapperType<RestaurantReview>;

  @ManyToOne(() => MerchantStaff)
  @JoinColumn({ name: 'merchant_staff_id' })
  merchantStaff?: WrapperType<MerchantStaff>;
}
