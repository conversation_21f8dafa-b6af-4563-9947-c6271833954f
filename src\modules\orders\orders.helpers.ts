import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import { OrderItemOption } from './entities/order-item-option.entity';
import { OrderItem } from './entities/order-item.entity';
import { Order } from './entities/order.entity';

export const localizeOrderList = (orders: Order[], userLanguage: Language) => {
  if (!orders) return;
  for (const order of orders) {
    localizeOrder(order, userLanguage);
  }
};

export const localizeOrder = (order: Order, userLanguage: Language) => {
  const restaurantLanguage = order.restaurant?.defaultLanguage;
  localizeOrderItems(order.orderItems, userLanguage, restaurantLanguage);
};

const localizeOrderItems = (
  orderItems: OrderItem[] | undefined,
  userLanguage: Language,
  restaurantLanguage?: Language,
) => {
  if (!orderItems) return;

  for (const orderItem of orderItems) {
    LocalizationHelper.localizeOrderItemEntity(orderItem, userLanguage, restaurantLanguage);
    localizeOrderItemOptions(orderItem.orderItemOptions, userLanguage, restaurantLanguage);
  }
};

const localizeOrderItemOptions = (
  orderItemOptions: OrderItemOption[] | undefined,
  userLanguage: Language,
  restaurantLanguage?: Language,
) => {
  if (!orderItemOptions) return;

  for (const orderItemOption of orderItemOptions) {
    LocalizationHelper.localizeOrderItemOptionEntity(orderItemOption, userLanguage, restaurantLanguage);
  }
};
