import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldPriceMappingOfIteamAndOption1751442484234 implements MigrationInterface {
  name = 'AddFieldPriceMappingOfIteamAndOption1751442484234';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" ADD "price" numeric(10,2)`,
    );
    await queryRunner.query(`ALTER TABLE "mapping_menu_sections_menu_items" ADD "price" numeric(10,2)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "mapping_menu_sections_menu_items" DROP COLUMN "price"`);
    await queryRunner.query(`ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" DROP COLUMN "price"`);
  }
}
