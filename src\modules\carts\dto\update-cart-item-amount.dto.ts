import { Type } from 'class-transformer';
import { IsArray, IsInt, IsOptional, IsPositive, IsString, ValidateNested } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { CartItemGroupOptionDto } from './add-to-cart.dto';

export class UpdateCartItemAmountDto {
  @ApiProperty()
  @IsOptional()
  @IsInt()
  @IsPositive()
  amount?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    example: [
      {
        optionGroupId: 'UUID',
        options: [{ id: 'UUID', amount: 1 }],
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemGroupOptionDto)
  groupOptions?: CartItemGroupOptionDto[];
}
