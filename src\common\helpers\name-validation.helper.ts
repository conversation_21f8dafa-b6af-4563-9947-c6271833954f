import { Repository } from 'typeorm';

import { BadRequestException } from '@nestjs/common';

export class NameVali<PERSON>Helper {
  /**
   * Check if a name exists in the database
   * @param repository The TypeORM repository
   * @param entityName The entity name for the query
   * @param restaurantId The restaurant ID to scope the search
   * @param fieldName The field name to check ('internalName' or 'publishedName')
   * @param value The value to check
   * @param excludeId Optional ID to exclude from the search
   * @returns Promise<boolean> - true if name exists, false otherwise
   */
  static async checkNameExists(
    repository: Repository<any>,
    entityName: string,
    restaurantId: string,
    fieldName: string,
    value: string,
    excludeId?: string,
  ): Promise<boolean> {
    const query = repository
      .createQueryBuilder(entityName)
      .where(`${entityName}.restaurantId = :restaurantId`, { restaurantId })
      .andWhere(`${entityName}.${fieldName} = :value`, { value })
      .andWhere(`${entityName}.deletedAt IS NULL`);

    if (excludeId) {
      query.andWhere(`${entityName}.id != :excludeId`, { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Validate unique names for both internalName and publishedName
   * @param restaurantId The restaurant ID to scope the search
   * @param internalName The internal name to check (optional)
   * @param publishedName The published name to check (optional)
   * @param excludeId Optional ID to exclude from the search
   * @param checkNameExistsFunction Function to check if name exists
   * @throws BadRequestException if any name already exists
   */
  static async validateUniqueNames(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
    checkNameExistsFunction?: (
      restaurantId: string,
      internalName?: string,
      publishedName?: string,
      excludeId?: string,
    ) => Promise<boolean>,
  ): Promise<void> {
    // Check if internalName already exists
    if (internalName && checkNameExistsFunction) {
      const internalNameExists = await checkNameExistsFunction(restaurantId, internalName, undefined, excludeId);
      if (internalNameExists) {
        throw new BadRequestException('Internal name already exists');
      }
    }

    // Check if publishedName already exists
    if (publishedName && checkNameExistsFunction) {
      const publishedNameExists = await checkNameExistsFunction(restaurantId, undefined, publishedName, excludeId);
      if (publishedNameExists) {
        throw new BadRequestException('Published name already exists');
      }
    }
  }
}
