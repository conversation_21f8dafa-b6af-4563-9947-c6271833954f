import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AdminsService } from './admins.service';
import { CreateAbAdminDto } from './dto/create-ab-admin.dto';

@ApiTags('(Admin) Users')
@Controller('admin/users')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class AdminsController {
  constructor(private readonly adminsService: AdminsService) {}

  @Get()
  async getAllAdmin() {
    return this.adminsService.getAllAdmin();
  }

  @Post()
  async createAbAdmin(@Body() dto: CreateAbAdminDto) {
    return this.adminsService.createAbAdmin(dto);
  }
}
