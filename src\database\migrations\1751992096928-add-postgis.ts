import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPostgis1751992096928 implements MigrationInterface {
  name = 'AddPostgis1751992096928';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable PostGIS extension
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS postgis`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP EXTENSION IF EXISTS postgis`);
  }
}
