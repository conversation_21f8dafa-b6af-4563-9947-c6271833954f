import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateIndexUniqueOrderAndCart1752044512332 implements MigrationInterface {
  name = 'UpdateIndexUniqueOrderAndCart1752044512332';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" DROP CONSTRAINT "UQ_order_item_option_original"`,
    );
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP CONSTRAINT "UQ_order_item_option"`);
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "UQ_cart_item_option"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_45558199e58d93c04d9c2cdc31" ON "order_item_options_original" ("order_item_original_id", "menu_item_option_group_id", "menu_item_option_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_7913c36131d9c1a00726c67793" ON "order_item_options" ("order_item_id", "menu_item_option_group_id", "menu_item_option_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ceb63f6df6212692a033383a25" ON "cart_item_options" ("cart_item_id", "menu_item_option_group_id", "menu_item_option_id") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_ceb63f6df6212692a033383a25"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7913c36131d9c1a00726c67793"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_45558199e58d93c04d9c2cdc31"`);
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "UQ_cart_item_option" UNIQUE ("cart_item_id", "menu_item_option_group_id", "menu_item_option_id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_item_options" ADD CONSTRAINT "UQ_order_item_option" UNIQUE ("order_item_id", "menu_item_option_group_id", "menu_item_option_id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" ADD CONSTRAINT "UQ_order_item_option_original" UNIQUE ("order_item_original_id", "menu_item_option_group_id", "menu_item_option_id")`,
    );
  }
}
