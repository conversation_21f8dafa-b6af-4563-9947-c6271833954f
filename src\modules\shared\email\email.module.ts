import { BullModule } from '@nestjs/bullmq';
import { Global, Module } from '@nestjs/common';

import { EmailProcessor } from './email.processor';
import { EmailService } from './email.service';
import { TemplateService } from './template.service';

@Global() // Make services available globally
@Module({
  imports: [
    BullModule.registerQueue({
      name: 'email',
    }),
  ],
  providers: [EmailService, TemplateService, EmailProcessor],
  exports: [EmailService, TemplateService],
})
export class EmailModule {}
