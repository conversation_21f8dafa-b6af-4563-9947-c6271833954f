import { UserType } from '@/modules/auth/enums/user-type.enum';
import { AllUserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const User = createParamDecorator((data: keyof AllUserJwtInfo | undefined, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  const user: AllUserJwtInfo | undefined = request.user;

  // If data is provided, return the specific property of the user object
  return data ? user?.[data] : user;
});

export const UserMerchantId = createParamDecorator((_: unknown, ctx: ExecutionContext): string | null => {
  const request = ctx.switchToHttp().getRequest();
  const user: AllUserJwtInfo | undefined = request.user;
  if (user?.userType !== UserType.MERCHANT_USER || user?.role !== MerchantUserRole.MEMBER) return null;
  return user.id;
});
