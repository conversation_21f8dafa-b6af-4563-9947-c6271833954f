import { Repository } from 'typeorm';

import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class RestaurantAccessService {
  constructor(
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
  ) {}

  /**
   * Verify that a restaurant exists and the user has access to it
   * @param restaurantId The ID of the restaurant to verify access to
   * @param ownerId The ID of the user (null for admin users)
   * @throws NotFoundException if the restaurant doesn't exist or the user doesn't have access to it
   */
  async verifyAccessRestaurant(restaurantId: string, ownerId: string | null) {
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .where('restaurant.id = :restaurantId', { restaurantId });

    // For merchant users, only allow access to restaurants they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant with ID ${restaurantId} not found or you don't have access to it`);
    }

    return restaurant;
  }
}
