import { Pagination } from 'nestjs-typeorm-paginate';

import { OrderStatusGroup } from '../constants/order.enums';
import { Order } from '../entities/order.entity';

export interface OrderStatusCounts {
  needsAction: number;
  inProcess: number;
  completed: number;
}

export interface OrderWithCountsResponse extends Pagination<Order> {
  statusCounts?: OrderStatusCounts;
}

export interface StatusGroupCount {
  group: OrderStatusGroup;
  count: number;
}
