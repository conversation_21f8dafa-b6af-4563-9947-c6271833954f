import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { User } from '@/modules/users/entities/user.entity';

import { OnepayVoidRequest } from './onepay-void-request.entity';

@Entity('onepay_create_token_requests')
@Index(['updatedAt', 'txnResponseCode', 'drExists'])
@Index(['updatedAt'])
@Index(['txnResponseCode', 'isRefunded', 'retryCount'])
export class OnepayCreateTokenRequest extends BaseEntity {
  @Index()
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'currency', type: 'varchar' })
  currency: string;

  @Column({ name: 'locale', type: 'varchar' })
  locale: string;

  @Column({ name: 'return_url', type: 'varchar' })
  returnUrl: string;

  @Index({ unique: true })
  @Column({ name: 'merch_txn_ref', type: 'varchar' })
  merchTxnRef: string;

  @Column({ name: 'order_info', type: 'varchar' })
  orderInfo: string;

  @Column({ name: 'amount', type: 'integer' })
  amount: number;

  @Column({ name: 'ticket_no', type: 'varchar' })
  ticketNo: string;

  @Column({ name: 'again_link', type: 'varchar' })
  againLink: string;

  @Column({ name: 'title', type: 'varchar' })
  title: string;

  @Column({ name: 'is_refunded', type: 'boolean', default: false })
  isRefunded: boolean;

  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  @Index()
  @Column({ name: 'dr_exists', type: 'boolean', default: true })
  drExists: boolean;

  @Column({ name: 'is_invalid', type: 'boolean', default: false })
  isInvalid: boolean;

  @Index()
  @Column({ name: 'txn_response_code', type: 'varchar', nullable: true })
  txnResponseCode?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: WrapperType<User>;

  @OneToMany(() => OnepayVoidRequest, (voidRequest) => voidRequest.orgMerchTxnRef)
  voidRequests?: WrapperType<OnepayVoidRequest>[];
}
