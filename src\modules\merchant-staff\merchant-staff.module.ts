import { SharedModule } from '@/modules/shared/shared.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantStaff } from './entities/merchant-staff.entity';
import { MerchantStaffController } from './merchant-staff.controller';
import { MerchantStaffService } from './merchant-staff.service';
import { StaffFCMController } from './staff-fcm.controller';

@Module({
  imports: [TypeOrmModule.forFeature([MerchantStaff]), SharedModule],
  controllers: [MerchantStaffController, StaffFCMController],
  providers: [MerchantStaffService],
  exports: [MerchantStaffService],
})
export class MerchantStaffModule {}
