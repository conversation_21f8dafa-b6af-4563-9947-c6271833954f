import { DataSource, Repository } from 'typeorm';

import { RestaurantTag } from '@/modules/restaurant-tags/entities/restaurant-tag.entity';

import { Seeder } from './config/base.seeder';

export class RestaurantTag1752255000000 extends Seeder {
  private restaurantTagRepository: Repository<RestaurantTag>;

  constructor(dataSource: DataSource) {
    super(dataSource);
    this.restaurantTagRepository = dataSource.getRepository(RestaurantTag);
  }

  /**
   * Name of the seeder
   */
  get name(): string {
    return 'RestaurantTag1752255000000';
  }

  public async run(): Promise<void> {
    const tagNames = [
      'Baos & Buns',
      'American',
      'check',
      'BBQ',
      'Continental',
      'Burgers',
      'Egyptian',
      'Congee',
      'Japanese',
      'Noodles',
      'Korean',
      'Pizza',
      'Indian',
      'Ramen',
      'check',
      'Italian',
      'Rice',
      'International',
      'Sushi',
      'Lebanese',
      'Sandwiches',
      'Middle Eastern',
      'Snacks',
      'South African',
      'Tacos',
      'Vietnamese',
    ];

    // Remove duplicates from tagNames
    const uniqueTagNames = [...new Set(tagNames)];

    // Create tag entities
    const tags = uniqueTagNames.map((name) => this.restaurantTagRepository.create({ name }));

    // Bulk upsert - insert if not exists, do nothing if exists
    await this.restaurantTagRepository.save(tags);
  }
}
