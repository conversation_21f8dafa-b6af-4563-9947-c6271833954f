import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { Restaurant } from './restaurant.entity';

@Entity('restaurant_available_schedules')
export class RestaurantAvailableSchedule extends BaseEntity {
  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.availableSchedule, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  // Day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
  @Column({ name: 'day', type: 'int' })
  day: number;

  @Column({ name: 'start', type: 'time', default: '00:00:00' })
  start: string | null;

  @Column({ name: 'end', type: 'time', default: '23:59:59' })
  end: string | null;

  @Column({ name: 'is_all_day', type: 'boolean', default: false })
  isAllDay: boolean;
}
