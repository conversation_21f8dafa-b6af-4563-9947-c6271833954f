import { Response } from 'express';

import { Public } from '@auth/decorators/public.decorator';
import { Body, Controller, HttpCode, HttpStatus, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { FakeLoginDto } from './dtos/onboarding.dto';
import { OnboardingService } from './onboarding.service';

@ApiTags('(Auth) User Fake')
@Controller('auth/user-fake')
export class AuthUserFakeController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Public()
  @Post('login-fake')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Auto login for testing' })
  async fakeLogin(@Body() dto: FakeLoginDto, @Res({ passthrough: true }) response: Response) {
    return this.onboardingService.fakeLogin(dto.email, response);
  }
}
