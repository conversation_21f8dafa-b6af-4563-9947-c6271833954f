import { Pagination } from 'nestjs-typeorm-paginate';
import { ObjectLiteral, SelectQueryBuilder } from 'typeorm';

export async function paginateQueryBuilder<Entity extends ObjectLiteral>(
  queryBuilder: SelectQueryBuilder<Entity>,
  query: { page: number; limit: number },
): Promise<Pagination<Entity>> {
  const { page, limit } = query;

  const [items, totalCount] = await queryBuilder
    .skip((page - 1) * limit)
    .take(limit)
    .getManyAndCount();

  return {
    items,
    meta: {
      totalItems: totalCount,
      itemCount: items.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    },
  };
}
