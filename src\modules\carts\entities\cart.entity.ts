import { Column, <PERSON>tity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { CartItem } from './cart-item.entity';

@Entity('carts')
@Index(['userId', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL AND completed_at IS NULL' })
export class Cart extends BaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Column({ name: 'completed_at', nullable: true, type: 'timestamptz' })
  completedAt?: Date | null;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @OneToMany(() => CartItem, (cartItem) => cartItem.cart)
  cartItems: WrapperType<CartItem>[];
}
