import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { User } from '@/modules/users/entities/user.entity';

@Entity('user_favourite_restaurants')
@Index(['userId', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class UserFavouriteRestaurant extends BaseEntity {
  @Index()
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: WrapperType<User>;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;
}
