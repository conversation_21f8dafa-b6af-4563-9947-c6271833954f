import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldLocalization1752933554733 implements MigrationInterface {
  name = 'AddFieldLocalization1752933554733';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "description_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "description_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_vi" character varying`);
    await queryRunner.query(`CREATE TYPE "public"."restaurants_default_language_enum" AS ENUM('en', 'vi')`);
    await queryRunner.query(
      `ALTER TABLE "restaurants" ADD "default_language" "public"."restaurants_default_language_enum" NOT NULL DEFAULT 'en'`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "language" character varying NOT NULL DEFAULT 'en'`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" ADD "option_group_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" ADD "option_group_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" ADD "option_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" ADD "option_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_section_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_section_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_item_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_item_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_item_description_en" text`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_item_description_vi" text`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "option_group_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "option_group_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "option_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "option_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_section_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_section_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_item_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_item_name_vi" character varying`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_item_description_en" text`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_item_description_vi" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_item_description_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_item_description_en"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_item_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_item_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_section_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_section_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "option_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "option_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "option_group_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "option_group_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_item_description_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_item_description_en"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_item_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_item_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_section_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_section_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "option_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "option_name_en"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "option_group_name_vi"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "option_group_name_en"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "language"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "default_language"`);
    await queryRunner.query(`DROP TYPE "public"."restaurants_default_language_enum"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_en"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "description_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "description_en"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_en"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_en"`);
  }
}
