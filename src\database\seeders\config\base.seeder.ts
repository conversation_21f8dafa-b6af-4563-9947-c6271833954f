import { DataSource } from 'typeorm';

import { SeedVersion } from '../../entities/seed-version.entity';

/**
 * Abstract class for all seeders
 */
export abstract class Seeder {
  constructor(protected readonly dataSource: DataSource) {}

  /**
   * Name of the seeder, defaults to class name
   */
  abstract get name(): string;

  /**
   * Method to execute seed data
   */
  abstract run(): Promise<any>;

  /**
   * Check if seeder has already been executed
   */
  async hasRun(): Promise<boolean> {
    const seedVersion = await this.dataSource.getRepository(SeedVersion).findOne({ where: { name: this.name } });
    return !!seedVersion;
  }

  /**
   * Mark seeder as executed
   */
  async markAsRun(): Promise<void> {
    await this.dataSource.getRepository(SeedVersion).save({
      name: this.name,
      executedAt: new Date(),
    });
  }

  /**
   * Execute seeder if not already run
   */
  async execute(): Promise<void> {
    if (await this.hasRun()) {
      console.log(`Seeder ${this.name} has already been executed, skipping.`);
      return;
    }

    console.log(`Executing seeder: ${this.name}`);
    await this.run();
    await this.markAsRun();
    console.log(`Seeder ${this.name} executed successfully.`);
  }
}
