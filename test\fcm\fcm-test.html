<!doctype html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FCM Token Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .section h3 {
        margin-top: 0;
        color: #333;
      }

      input,
      select,
      textarea {
        width: 100%;
        padding: 8px;
        margin: 5px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }

      button {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }

      button:hover {
        background-color: #0056b3;
      }

      .success {
        color: green;
        background-color: #d4edda;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }

      .error {
        color: red;
        background-color: #f8d7da;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }

      .token-display {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        word-break: break-all;
        font-family: monospace;
        font-size: 12px;
      }

      .log {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <h1>FCM Token Test Tool</h1>

      <!-- Firebase Config Section -->
      <div class="section">
        <h3>1. Cấu hình Firebase</h3>
        <label>Firebase Config (JSON):</label>
        <textarea
          id="firebaseConfig"
          rows="8"
          placeholder='{
  "apiKey": "your-api-key",
  "authDomain": "your-project.firebaseapp.com",
  "projectId": "your-project-id",
  "storageBucket": "your-project.appspot.com",
  "messagingSenderId": "123456789",
  "appId": "your-app-id"
}'
        ></textarea>
        <label>VAPID Key:</label>
        <input type="text" id="vapidKey" placeholder="Your VAPID key" />
        <button onclick="initializeFirebase()">Khởi tạo Firebase</button>
        <div id="firebaseStatus"></div>
      </div>

      <!-- FCM Token Section -->
      <div class="section">
        <h3>2. Lấy FCM Token</h3>
        <button onclick="requestPermission()">Yêu cầu quyền thông báo</button>
        <button onclick="getFCMToken()">Lấy FCM Token</button>
        <div id="tokenStatus"></div>
        <div id="tokenDisplay" class="token-display"></div>
      </div>

      <!-- Notification Test Section -->
      <div class="section">
        <h3>3. Test Notification</h3>
        <label>Title:</label>
        <input type="text" id="notifTitle" value="Test Notification" />
        <label>Body:</label>
        <input type="text" id="notifBody" value="This is a test notification" />
        <label>Data (JSON):</label>
        <textarea id="notifData" rows="3" placeholder='{"key": "value"}'></textarea>
        <button onclick="sendTestNotification()">Gửi Test Notification</button>
        <div id="notificationStatus"></div>
      </div>

      <!-- Log Section -->
      <div class="section">
        <h3>4. Logs</h3>
        <button onclick="clearLogs()">Xóa Logs</button>
        <div id="logs" class="log"></div>
      </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>

    <script>
      document.querySelector('#vapidKey').value =
        'BGgfoXnj6i7CihL863kdHwMlHyg1kz5k6GVDWbgho9guHQX6EPyygE6G8cuTWIhZ2at68oV2Kl0l66zcN_FiyzM';
      document.querySelector('#firebaseConfig').value = JSON.stringify({
        apiKey: 'AIzaSyCBbXH1UwNGWNmtpwLqL8duX4Z4Zktd_bc',
        authDomain: 'anhbeo-f0e1a.firebaseapp.com',
        projectId: 'anhbeo-f0e1a',
        storageBucket: 'anhbeo-f0e1a.firebasestorage.app',
        messagingSenderId: '629612931539',
        appId: '1:629612931539:web:945eb08a85683e1b96a726',
        measurementId: 'G-P2P5908TMJ',
      });

      let messaging;
      let currentToken = '';

      function log(message) {
        const logs = document.getElementById('logs');
        const timestamp = new Date().toLocaleTimeString();
        logs.textContent += `[${timestamp}] ${message}\n`;
        logs.scrollTop = logs.scrollHeight;
        console.log(message);
      }

      function showStatus(elementId, message, isError = false) {
        const element = document.getElementById(elementId);
        element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
      }

      async function initializeFirebase() {
        try {
          const configText = document.getElementById('firebaseConfig').value;
          const vapidKey = document.getElementById('vapidKey').value;

          if (!configText || !vapidKey) {
            showStatus('firebaseStatus', 'Vui lòng nhập Firebase config và VAPID key', true);
            return;
          }

          const firebaseConfig = JSON.parse(configText);

          // Register service worker first
          if ('serviceWorker' in navigator) {
            try {
              const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
              log('Service Worker registered successfully');

              // Send Firebase config to service worker
              if (registration.active) {
                registration.active.postMessage({
                  type: 'FIREBASE_CONFIG',
                  config: firebaseConfig,
                });
              }
            } catch (swError) {
              log('Service Worker registration failed: ' + swError.message);
            }
          }

          // Initialize Firebase
          firebase.initializeApp(firebaseConfig);
          messaging = firebase.messaging();

          // Set VAPID key
          messaging.getToken({ vapidKey: vapidKey });

          showStatus('firebaseStatus', 'Firebase đã được khởi tạo thành công!');
          log('Firebase initialized successfully');

          // Listen for foreground messages
          messaging.onMessage((payload) => {
            log('Foreground message received: ' + JSON.stringify(payload));
            showNotification(payload);
          });
        } catch (error) {
          showStatus('firebaseStatus', 'Lỗi khởi tạo Firebase: ' + error.message, true);
          log('Firebase initialization error: ' + error.message);
        }
      }

      async function requestPermission() {
        try {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            showStatus('tokenStatus', 'Quyền thông báo đã được cấp!');
            log('Notification permission granted');
          } else {
            showStatus('tokenStatus', 'Quyền thông báo bị từ chối', true);
            log('Notification permission denied');
          }
        } catch (error) {
          showStatus('tokenStatus', 'Lỗi yêu cầu quyền: ' + error.message, true);
          log('Permission request error: ' + error.message);
        }
      }

      async function getFCMToken() {
        if (!messaging) {
          showStatus('tokenStatus', 'Vui lòng khởi tạo Firebase trước', true);
          return;
        }

        try {
          const vapidKey = document.getElementById('vapidKey').value;
          const token = await messaging.getToken({ vapidKey: vapidKey });

          if (token) {
            currentToken = token;
            document.getElementById('tokenDisplay').textContent = token;
            showStatus('tokenStatus', 'FCM Token đã được lấy thành công!');
            log('FCM Token retrieved: ' + token.substring(0, 50) + '...');
          } else {
            showStatus('tokenStatus', 'Không thể lấy FCM Token', true);
            log('No FCM token available');
          }
        } catch (error) {
          showStatus('tokenStatus', 'Lỗi lấy FCM Token: ' + error.message, true);
          log('FCM Token error: ' + error.message);
        }
      }

      async function addFCMToken() {
        if (!currentToken) {
          showStatus('apiResults', 'Vui lòng lấy FCM Token trước', true);
          return;
        }

        const apiUrl = document.getElementById('apiUrl').value;
        const authToken = document.getElementById('authToken').value;
        const deviceId = document.getElementById('deviceId').value;
        const platform = document.getElementById('platform').value;

        try {
          const response = await fetch(`${apiUrl}/user/fcm-tokens`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify({
              token: currentToken,
              deviceId: deviceId,
              platform: platform,
            }),
          });

          const result = await response.json();

          if (response.ok) {
            showStatus('apiResults', 'FCM Token đã được thêm thành công!');
            log('FCM Token added successfully: ' + JSON.stringify(result));
          } else {
            showStatus('apiResults', 'Lỗi thêm FCM Token: ' + JSON.stringify(result), true);
            log('Add FCM Token error: ' + JSON.stringify(result));
          }
        } catch (error) {
          showStatus('apiResults', 'Lỗi API: ' + error.message, true);
          log('API error: ' + error.message);
        }
      }

      async function getFCMTokens() {
        const apiUrl = document.getElementById('apiUrl').value;
        const authToken = document.getElementById('authToken').value;

        try {
          const response = await fetch(`${apiUrl}/user/fcm-tokens`, {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          const result = await response.json();

          if (response.ok) {
            showStatus('apiResults', 'Danh sách FCM Tokens: ' + JSON.stringify(result, null, 2));
            log('FCM Tokens retrieved: ' + JSON.stringify(result));
          } else {
            showStatus('apiResults', 'Lỗi lấy FCM Tokens: ' + JSON.stringify(result), true);
            log('Get FCM Tokens error: ' + JSON.stringify(result));
          }
        } catch (error) {
          showStatus('apiResults', 'Lỗi API: ' + error.message, true);
          log('API error: ' + error.message);
        }
      }

      async function removeFCMToken() {
        if (!currentToken) {
          showStatus('apiResults', 'Vui lòng lấy FCM Token trước', true);
          return;
        }

        const apiUrl = document.getElementById('apiUrl').value;
        const authToken = document.getElementById('authToken').value;

        try {
          const response = await fetch(`${apiUrl}/user/fcm-tokens`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify({
              token: currentToken,
            }),
          });

          const result = await response.json();

          if (response.ok) {
            showStatus('apiResults', 'FCM Token đã được xóa thành công!');
            log('FCM Token removed successfully: ' + JSON.stringify(result));
          } else {
            showStatus('apiResults', 'Lỗi xóa FCM Token: ' + JSON.stringify(result), true);
            log('Remove FCM Token error: ' + JSON.stringify(result));
          }
        } catch (error) {
          showStatus('apiResults', 'Lỗi API: ' + error.message, true);
          log('API error: ' + error.message);
        }
      }

      async function sendTestNotification() {
        const title = document.getElementById('notifTitle').value;
        const body = document.getElementById('notifBody').value;
        const dataText = document.getElementById('notifData').value;

        try {
          let data = {};
          if (dataText) {
            data = JSON.parse(dataText);
          }

          // Show browser notification
          if (Notification.permission === 'granted') {
            new Notification(title, {
              body: body,
              data: data,
              icon: '/favicon.ico',
            });
            showStatus('notificationStatus', 'Test notification đã được gửi!');
            log('Test notification sent: ' + title);
          } else {
            showStatus('notificationStatus', 'Quyền thông báo chưa được cấp', true);
          }
        } catch (error) {
          showStatus('notificationStatus', 'Lỗi gửi notification: ' + error.message, true);
          log('Notification error: ' + error.message);
        }
      }

      function showNotification(payload) {
        const { notification, data } = payload;
        console.log('notification :>> ', notification);
        console.log('data :>> ', data);

        if (Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.body,
            data: data,
            icon: '/favicon.ico',
          });
        }
      }

      function clearLogs() {
        document.getElementById('logs').textContent = '';
      }

      // Initialize on page load
      window.addEventListener('load', () => {
        log('FCM Test Tool loaded');

        // Check if browser supports notifications
        if (!('Notification' in window)) {
          log('This browser does not support notifications');
        }

        // Check if browser supports service workers
        if (!('serviceWorker' in navigator)) {
          log('This browser does not support service workers');
        }
      });
    </script>
  </body>
</html>
