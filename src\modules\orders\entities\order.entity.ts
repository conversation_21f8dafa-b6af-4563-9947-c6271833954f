import { Column, <PERSON>tity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { ChatConversation } from '@/modules/chat/entities/chat-conversation.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { User } from '@/modules/users/entities/user.entity';

import { OrderCancelReason, OrderStatus, PaymentMethod, PaymentStatus } from '../constants/order.enums';
import { OrderCustomer } from './order-customer.entity';
import { OrderItemOriginal } from './order-item-original.entity';
import { OrderItem } from './order-item.entity';

@Entity('orders')
@Index(['userId', 'restaurantId']) // Existing - for basic filtering
@Index(['userId', 'status', 'updatedAt']) // User orders filtering with status and time
@Index(['restaurantId', 'status', 'updatedAt']) // Restaurant orders filtering
@Index(['restaurantId', 'orderSequenceNumber']) // For optimized order code generation
export class Order extends BaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'cart_id', type: 'uuid' })
  cartId: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'order_code', type: 'varchar' })
  orderCode: string;

  @Column({ name: 'order_sequence_number', type: 'integer', default: 0 })
  orderSequenceNumber: number;

  @Column({ name: 'status', type: 'varchar', default: OrderStatus.NEW })
  status: OrderStatus;

  @Column({ name: 'payment_method', type: 'varchar', default: PaymentMethod.CASH })
  paymentMethod: PaymentMethod;

  @Column({ name: 'payment_card_id', type: 'uuid', nullable: true })
  paymentCardId?: string | null;

  @Index()
  @Column({ name: 'payment_status', type: 'varchar', nullable: true })
  paymentStatus: PaymentStatus;

  @Column({ name: 'include_utensils', type: 'boolean', default: false })
  includeUtensils: boolean;

  @Column({
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  subtotal: number;

  @Column({
    name: 'tax_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  taxAmount: number;

  @Column({
    name: 'shipping_fee',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  shippingFee: number;

  @Column({ name: 'order_customer_id', type: 'uuid' })
  orderCustomerId: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  discount: number;

  @Column({
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  total: number;

  @Column({ name: 'note', nullable: true, type: 'text' })
  note?: string | null;

  @Column({ name: 'restaurant_name', type: 'varchar' })
  restaurantName: string;

  @Column({ name: 'rejection_note', nullable: true, type: 'text' })
  rejectionNote?: string | null;

  @Column({ name: 'unfulfilled_at', nullable: true, type: 'timestamptz' })
  unfulfilledAt?: Date | null;

  @Column({ name: 'modified_at', nullable: true, type: 'timestamptz' })
  modifiedAt?: Date | null;

  @Column({ name: 'accepted_at', nullable: true, type: 'timestamptz' })
  acceptedAt?: Date | null; // accept modify by user

  @Column({ name: 'in_kitchen_at', nullable: true, type: 'timestamptz' })
  inKitchenAt?: Date | null; // confirm by staff

  @Column({ name: 'kitchen_eta', nullable: true, type: 'timestamptz' })
  kitchenEta?: Date | null;

  @Column({ name: 'completed_at', nullable: true, type: 'timestamptz' })
  completedAt?: Date | null;

  @Column({ name: 'cancelled_at', nullable: true, type: 'timestamptz' })
  cancelledAt?: Date | null;

  @Column({ name: 'rejection_reason', type: 'varchar', nullable: true })
  rejectionReason?: string | null;

  @Column({ name: 'cancel_reason', type: 'varchar', nullable: true })
  cancelReason?: OrderCancelReason | null;

  @Column({ name: 'modification_deadline', nullable: true, type: 'timestamptz' })
  modificationDeadline?: Date | null;

  @Column({ name: 'in_delivery_at', nullable: true, type: 'timestamptz' })
  inDeliveryAt?: Date | null;

  @Column({ name: 'delivery_from_eta', nullable: true, type: 'timestamptz' })
  deliveryFromEta?: Date | null;

  @Column({ name: 'delivery_to_eta', nullable: true, type: 'timestamptz' })
  deliveryToEta?: Date | null;

  @Column({ name: 'archived_at', nullable: true, type: 'timestamptz' })
  archivedAt?: Date | null;

  @Column({ name: 'is_user_seen', type: 'boolean', default: false })
  isUserSeen: boolean;

  @Column({ name: 'is_skip_review', type: 'boolean', default: false })
  isSkipReview: boolean;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: WrapperType<User>;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant?: WrapperType<Restaurant>;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.order)
  orderItems?: WrapperType<OrderItem>[];

  @OneToMany(() => OrderItemOriginal, (orderItemOriginal) => orderItemOriginal.order)
  orderItemsOriginal?: WrapperType<OrderItemOriginal>[];

  @OneToOne(() => OrderCustomer, (orderCustomer) => orderCustomer.order)
  @JoinColumn({ name: 'order_customer_id' })
  orderCustomer?: WrapperType<OrderCustomer>;

  @OneToOne(() => ChatConversation, (chatConversation) => chatConversation.order)
  chatConversation?: WrapperType<ChatConversation>;
}
