import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTrigramIndexes1752254222000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create trigram indexes for better fuzzy search performance
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_restaurant_tags_name_trigram 
      ON restaurant_tags USING gin (name gin_trgm_ops) 
      WHERE deleted_at IS NULL;
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_restaurants_published_name_trigram 
      ON restaurants USING gin (published_name gin_trgm_ops) 
      WHERE deleted_at IS NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove trigram indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_restaurant_tags_name_trigram;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_restaurants_published_name_trigram;`);
  }
}
