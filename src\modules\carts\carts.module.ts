import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MenuItemsModule } from '../menu-items/menu-items.module';
import { CartsService } from './carts.service';
import { CartsController } from './controllers/user-carts.controller';
import { CartItemOption } from './entities/cart-item-option.entity';
import { CartItem } from './entities/cart-item.entity';
import { Cart } from './entities/cart.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Cart, CartItem, CartItemOption]), MenuItemsModule],
  controllers: [CartsController],
  providers: [CartsService],
  exports: [CartsService],
})
export class CartsModule {}
