import { Type } from 'class-transformer';
import { IsInt, IsPositive, IsUUID, ValidateIf } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class SectionItemWithPriceDto {
  @ApiProperty({ description: 'Section ID' })
  @IsUUID()
  sectionId: string;

  @ApiProperty({
    description: 'Custom price for this item in this group (required, can be null)',
    required: true,
    nullable: true,
  })
  @ValidateIf((o) => o.price !== null)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  price: number | null;
}
