import * as bcrypt from 'bcrypt';
import { randomBytes } from 'crypto';
import { Repository } from 'typeorm';

import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateAbAdminDto } from './dto/create-ab-admin.dto';
import { Admin } from './entities/admin.entity';

@Injectable()
export class AdminsService {
  private readonly logger = new Logger(AdminsService.name);

  constructor(
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    private configService: ConfigService,
  ) {}

  // create root admin
  async ensureRootAdmin(): Promise<void> {
    const rootEmail = this.configService.get<string>('ROOT_ANHBEO_ADMIN_EMAIL');
    const defaultName = this.configService.get<string>('ROOT_ANHBEO_ADMIN_NAME');
    const defaultPassword = this.configService.get<string>('ROOT_ANHBEO_ADMIN_PASSWORD');

    if (!rootEmail || !defaultName || !defaultPassword) {
      this.logger.warn('Root admin not created. Missing environment variables.');
      return;
    }

    // check existing admin account
    const existingAdmin = await this.adminRepository.findOne({ where: { email: rootEmail } });

    if (existingAdmin) {
      this.logger.log('Root admin already exist');
      return;
    }

    const hashedPassword = await this.hashString(defaultPassword);
    const rootAdmin = this.adminRepository.create({
      name: defaultName,
      email: rootEmail,
      password: hashedPassword,
    });
    // save root admin info to database
    await this.adminRepository.save(rootAdmin);
    this.logger.log('Root admin created');
  }

  private async hashString(token: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(token, saltRounds);
  }

  private omitPassword(admin: Admin): Omit<Admin, 'password'> {
    const { password, ...safeData } = admin;
    void password;
    return safeData;
  }

  async getAllAdmin() {
    const admins = await this.adminRepository.find();
    return admins.map((admin) => this.omitPassword(admin));
  }

  async createAbAdmin(dto: CreateAbAdminDto): Promise<any> {
    const { email, name, role } = dto;

    await this.ensureEmailIsUnique(email);

    const defaultPassword = this.generateDefaultPassword();
    const hashedPassword = await this.hashString(defaultPassword);

    const admin = await this.saveNewAdmin({ name, email, role, password: hashedPassword });

    return this.buildAdminCreationResponse(admin, defaultPassword);
  }

  findOneByEmail(email: string) {
    return this.adminRepository.findOne({ where: { email } });
  }

  findById(id: string) {
    return this.adminRepository.findOne({ where: { id } });
  }

  getMe(id: string) {
    return this.adminRepository.findOne({ where: { id }, select: ['id', 'name', 'email', 'role', 'banned'] });
  }

  private async ensureEmailIsUnique(email: string): Promise<void> {
    const existing = await this.adminRepository.findOne({ where: { email } });
    if (existing) {
      throw new ConflictException('This Anh Beo email already exists.');
    }
  }

  private generateDefaultPassword(length = 10): string {
    return randomBytes(length).toString('base64').slice(0, length);
  }

  private async saveNewAdmin(data: Partial<Admin>): Promise<Admin> {
    const admin = this.adminRepository.create(data);
    return this.adminRepository.save(admin);
  }

  private buildAdminCreationResponse(admin: Admin, adminPassword: string): any {
    const adminWithoutPassword = this.omitPassword(admin);
    return {
      adminWithoutPassword,
      message: `Admin account created. Default password: ${adminPassword}`,
    };
  }
}
