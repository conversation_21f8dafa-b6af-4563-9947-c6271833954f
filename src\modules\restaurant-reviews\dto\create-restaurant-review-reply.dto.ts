import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CreateRestaurantReviewReplyDto {
  @ApiProperty({
    description: 'ID of the restaurant review to reply to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsUUID()
  restaurantReviewId: string;

  @ApiProperty({
    description: 'Reply content from the restaurant',
    example: 'Thank you for your feedback. We will improve our service quality.',
  })
  @IsNotEmpty()
  @IsString()
  comment: string;
}
