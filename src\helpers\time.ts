import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);

export const getCurrentTimeByTimeAndDay = () => {
  // const now = dayjs().utc(); // UTC time
  //   const currentTime = now.format('HH:mm:ssZ'); // UTC time without timezone offset
  //   const currentDayOfWeek = now.day();

  //   return { now, currentTime, currentDayOfWeek };
  const now = dayjs().tz('Asia/Ho_Chi_Minh'); // Timezone +7
  const currentTime = now.format('HH:mm:ss');
  const currentDayOfWeek = now.day();

  return { currentTime, currentDayOfWeek };
};

export const parseTimeStrFromDb = (timeStr: string) => {
  if (timeStr.includes('+')) {
    const timezoneOffset = timeStr.split('+')[1];
    if (timezoneOffset && !timezoneOffset.includes(':')) {
      return timeStr + ':00';
    }
  }

  if (timeStr.includes('-')) {
    const timezoneOffset = timeStr.split('-')[1];
    if (timezoneOffset && !timezoneOffset.includes(':')) {
      return timeStr + ':00';
    }
  }
  return timeStr;
};

export default dayjs;
