import { ValueTransformer } from 'typeorm';

/**
 * ColumnNumericTransformer ensures decimal string values from database
 * are converted to number when reading, and left unchanged when writing.
 */
export class ColumnNumericTransformer implements ValueTransformer {
  // Called when writing to DB
  to(value: number | null): number | null {
    return value;
  }

  // Called when reading from DB
  from(value: string | null): number | null {
    return value !== null ? parseFloat(value) : null;
  }
}
