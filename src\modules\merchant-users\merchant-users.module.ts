import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantUsersController } from './merchant-users.controller';
import { MerchantUsersService } from './merchant-users.service';

@Module({
  imports: [TypeOrmModule.forFeature([MerchantUser])],
  controllers: [MerchantUsersController],
  providers: [MerchantUsersService],
  exports: [MerchantUsersService],
})
export class MerchantUsersModule {}
