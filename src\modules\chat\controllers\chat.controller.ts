import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AllUserJwtInfo } from '../../auth/types/jwt-payload.type';
import { ChatService } from '../chat.service';
import { ConversationQueryDto } from '../dto/conversation-query.dto';
import { MessagesQueryDto } from '../dto/messages-query.dto';
import { SendMessageDto } from '../dto/send-message.dto';

@ApiTags('Chat')
@Controller('chat')
@Roles({ userType: UserType.USER, role: '*' }, { userType: UserType.MERCHANT_STAFF, role: '*' })
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('messages')
  async sendMessage(@User() user: AllUserJwtInfo, @Body() sendMessageDto: SendMessageDto) {
    return this.chatService.sendMessage(user, sendMessageDto);
  }

  @Get('conversations')
  async getConversations(@User() user: AllUserJwtInfo, @Query() query: ConversationQueryDto) {
    if (user.userType === UserType.USER) {
      return this.chatService.getConversationsByUser(user.id, query);
    } else if (user.userType === UserType.MERCHANT_STAFF) {
      return this.chatService.getConversationsByRestaurant(user.restaurantId, query);
    }
    return {
      data: [],
      cursor: undefined,
      hasMore: false,
    };
  }

  @Get('conversations/:orderId')
  getConversationByOrderId(@User() user: AllUserJwtInfo, @Param('orderId', ParseUUIDPipe) orderId: string) {
    return this.chatService.findOneConversationByOrderId(user, orderId);
  }

  @Get('conversations/:orderId/messages')
  async getMessages(
    @User() user: AllUserJwtInfo,
    @Param('orderId', ParseUUIDPipe) orderId: string,
    @Query() query: MessagesQueryDto,
  ) {
    return this.chatService.getMessages(user, orderId, query);
  }
}
