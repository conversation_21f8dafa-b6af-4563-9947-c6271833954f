import { EntityManager, Repository } from 'typeorm';

import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { OnePayService } from '../onepay/onepay.service';
import { CreatePaymentCardDto } from './dto/create-payment-card.dto';
import { PaymentCard } from './entities/payment-card.entity';

@Injectable()
export class PaymentCardService {
  private readonly logger = new Logger(PaymentCardService.name);

  constructor(
    @InjectRepository(PaymentCard)
    private paymentCardRepository: Repository<PaymentCard>,
    @Inject(forwardRef(() => OnePayService))
    private readonly onePayService: WrapperType<OnePayService>,
  ) {}

  async create(createPaymentCardDto: CreatePaymentCardDto, manager: EntityManager): Promise<void> {
    try {
      // Check if token already exists for this user
      const existingCard = await manager.findOne(PaymentCard, {
        where: { userId: createPaymentCardDto.userId, cardUid: createPaymentCardDto.cardUid },
      });

      if (existingCard) {
        this.logger.warn('Payment card with this cardUid already exists', {
          userId: createPaymentCardDto.userId,
          cardUid: createPaymentCardDto.cardUid,
        });

        // Update existing card instead of creating new one
        return await this.updateExistingCard(existingCard.id, createPaymentCardDto, manager);
      }

      const paymentCard = manager.create(PaymentCard, createPaymentCardDto);
      const savedCard = await manager.save(paymentCard);

      this.logger.log('Payment card created successfully', {
        cardId: savedCard.id,
        userId: savedCard.userId,
        tokenNumber: savedCard.tokenNumber,
      });
    } catch (error) {
      this.logger.error('Failed to create payment card:', error);
      throw error;
    }
  }

  async findByUserId(userId: string): Promise<PaymentCard[]> {
    return this.paymentCardRepository.find({
      where: { userId },
      order: { isDefault: 'DESC', createdAt: 'DESC' },
      select: {
        id: true,
        tokenExpiry: true,
        maskedCardNumber: true,
        cardBank: true,
        isDefault: true,
      },
    });
  }

  async findByIdAndUserId(id: string, userId: string): Promise<PaymentCard> {
    const card = await this.paymentCardRepository.findOne({ where: { id, userId } });
    if (!card) {
      throw new NotFoundException('Payment card not found');
    }
    return card;
  }

  async delete(id: string, userId: string): Promise<void> {
    const card = await this.findByIdAndUserId(id, userId);

    const deleteSuccess = await this.onePayService.deleteToken(card.tokenNumber, card.tokenExpiry);
    if (!deleteSuccess) {
      throw new BadRequestException('Failed to delete token from OnePay');
    }

    await this.paymentCardRepository.softDelete({ id: card.id });

    this.logger.log('Payment card deleted', { cardId: id });
  }

  private async updateExistingCard(cardId: string, newData: CreatePaymentCardDto, manager: EntityManager) {
    const partialUpdate: Partial<PaymentCard> = {};

    if (newData.maskedCardNumber) partialUpdate.maskedCardNumber = newData.maskedCardNumber;
    if (newData.tokenNumber) partialUpdate.tokenNumber = newData.tokenNumber;
    if (newData.tokenExpiry) partialUpdate.tokenExpiry = newData.tokenExpiry;
    if (newData.cardBank) partialUpdate.cardBank = newData.cardBank;

    await manager.update(PaymentCard, cardId, partialUpdate);
  }

  async setDefaultCard(cardId: string, userId: string): Promise<void> {
    const card = await this.findByIdAndUserId(cardId, userId);
    await this.unsetDefaultCards(card.userId);
    await this.paymentCardRepository.update({ id: cardId }, { isDefault: true });
  }

  private async unsetDefaultCards(userId: string): Promise<void> {
    await this.paymentCardRepository.update({ userId, isDefault: true }, { isDefault: false });
  }
}
