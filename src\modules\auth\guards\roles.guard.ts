import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If the route is public, allow access without checking roles
    if (isPublic) {
      return true;
    }

    const requiredRoles = this.reflector.getAllAndMerge<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      throw new ForbiddenException('This API is private.');
    }

    const { user } = context.switchToHttp().getRequest();

    // If no user is present, deny access
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check if the user has the required role
    // The format of roles is 'userType:role', e.g., 'ab-admin:superadmin'
    const userTypeRole = `${user.userType}:${user.role}`;
    const userTypeAllRole = `${user.userType}:*`;

    const hasRequiredRole = requiredRoles.some((role) => {
      // Otherwise, check the full userType:role combination
      return userTypeRole === role || userTypeAllRole === role;
    });

    if (!hasRequiredRole) {
      throw new ForbiddenException(`User does not have the permission to access this resource.`);
    }

    return true;
  }
}
