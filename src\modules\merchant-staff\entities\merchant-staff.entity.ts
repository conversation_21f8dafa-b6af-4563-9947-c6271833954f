import { Exclude } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { StaffFcmToken } from '@/modules/shared/fcm/entities/staff-fcm-token.entity';

import { StaffRole } from '../enums/staff-role.enum';

@Entity('merchant_staff')
export class MerchantStaff extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  username: string;

  @Column({ type: 'varchar' })
  @Exclude()
  password: string;

  @Column({ type: 'enum', enum: StaffRole, default: StaffRole.STAFF })
  role: StaffRole;

  @Column({ type: 'boolean', default: false })
  banned: boolean;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @OneToMany(() => StaffFcmToken, (staffFcmToken) => staffFcmToken.merchantStaff)
  fcmTokens?: StaffFcmToken[];
}
