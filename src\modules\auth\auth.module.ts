import { UserAddressesModule } from '@/modules/user-addresses/user-addresses.module';
import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';

import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { ProfileCompletionGuard } from './guards/profile-completion.guard';
import { RolesGuard } from './guards/roles.guard';
import { AuthAdminModule } from './modules/auth-admin/auth-admin.module';
import { AuthMerchantStaffModule } from './modules/auth-merchant-staff/auth-merchant-staff.module';
import { AuthMerchantUserModule } from './modules/auth-merchant-user/auth-merchant-user.module';
import { AuthUserModule } from './modules/auth-user/auth-user.module';

@Module({
  imports: [AuthUserModule, AuthMerchantUserModule, AuthMerchantStaffModule, AuthAdminModule, UserAddressesModule],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ProfileCompletionGuard,
    },
  ],
})
export class AuthModule {}
