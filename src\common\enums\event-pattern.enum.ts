export enum AppMode {
  GENERAL = 'general',
  API = 'api',
  JOB = 'job',
}

export enum ApiEventPattern {
  CHAT_USER_CONNECTED = 'chat.user_connected',
  CHAT_USER_JOINED_ORDER = 'chat.user_joined_order',

  ORDER_VALIDATE_ACCESS = 'order.validate_access',
}

export enum SocketEventPattern {
  CHAT_NEW_MESSAGE = 'chat.new_message',
  CHAT_MESSAGE_STATUS_UPDATE = 'chat.message_status_update',
  ORDER_NEW = 'order.new',
  ORDER_STATUS_UPDATE = 'order.status_update',
}
