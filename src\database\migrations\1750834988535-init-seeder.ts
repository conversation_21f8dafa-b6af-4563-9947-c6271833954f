import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitSeeder1750834988535 implements MigrationInterface {
  name = 'InitSeeder1750834988535';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "seed_versions" ("name" character varying NOT NULL, "executedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_63469db7b32696e8eb3d9745b63" PRIMARY KEY ("name"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "seed_versions"`);
  }
}
