import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTotalOrdersSoldMenuItem1752306494235 implements MigrationInterface {
  name = 'AddTotalOrdersSoldMenuItem1752306494235';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "total_orders_sold" integer NOT NULL DEFAULT '0'`);
    await queryRunner.query(`CREATE INDEX "IDX_ba96bbd861b8b0b4c2d2280a45" ON "menu_items" ("total_orders_sold") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_ba96bbd861b8b0b4c2d2280a45"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "total_orders_sold"`);
  }
}
