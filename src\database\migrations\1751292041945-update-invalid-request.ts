import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvalidRequest1751292041945 implements MigrationInterface {
  name = 'UpdateInvalidRequest1751292041945';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "onepay_create_token_requests" ADD "is_invalid" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "onepay_create_token_requests" DROP COLUMN "is_invalid"`);
  }
}
