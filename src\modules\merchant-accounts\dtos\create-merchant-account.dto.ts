import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CreateMerchantAccountDto {
  @ApiProperty({ description: 'Name of the merchant account' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'ID of the owner merchant user', required: false })
  @IsOptional()
  @IsUUID()
  ownerMerchantUserId?: string;
}
