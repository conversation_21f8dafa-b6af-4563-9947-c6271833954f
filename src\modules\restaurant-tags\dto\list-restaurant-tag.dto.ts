import { IsOptional, IsString, IsUUID } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListRestaurantTagDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by address variant', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Filter by formatted address', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;
}
