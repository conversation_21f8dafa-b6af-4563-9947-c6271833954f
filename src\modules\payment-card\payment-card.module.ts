import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { OnepayModule } from '../onepay/onepay.module';
import { UserPaymentCardController } from './controllers/user-payment-card.controller';
import { PaymentCard } from './entities/payment-card.entity';
import { PaymentCardService } from './payment-card.service';

@Module({
  imports: [TypeOrmModule.forFeature([PaymentCard]), forwardRef(() => OnepayModule)],
  controllers: [UserPaymentCardController],
  providers: [PaymentCardService],
  exports: [PaymentCardService],
})
export class PaymentCardModule {}
