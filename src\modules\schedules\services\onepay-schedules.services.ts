import { OnePayService } from '@/modules/onepay/onepay.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class OnepaySchedulesService {
  private readonly logger = new Logger(OnepaySchedulesService.name);
  private isProcessingAddCard = false;
  private isProcessingRefunds = false;
  private isProcessingPaymentOrder = false;

  constructor(private readonly onePayService: OnePayService) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkPendingPaymentCardRequests(): Promise<void> {
    if (this.isProcessingAddCard) {
      this.logger.log('Previous payment card check is still running, skipping...');
      return;
    }

    try {
      this.logger.log('Checking pending payment card requests...');
      await this.onePayService.checkPendingPaymentCardRequests();
    } catch (error) {
      this.logger.error('Error in scheduled payment card check:', error);
    } finally {
      this.isProcessingAddCard = false;
    }
  }

  @Cron('*/3 * * * *')
  async checkPendingPaymentOrderRequests(): Promise<void> {
    if (this.isProcessingPaymentOrder) {
      this.logger.log('Previous payment order check is still running, skipping...');
      return;
    }

    try {
      this.isProcessingPaymentOrder = true;
      this.logger.log('Checking pending payment order requests...');
      await this.onePayService.checkPendingPaymentOrderRequests();
    } catch (error) {
      this.logger.error('Error in scheduled payment order check:', error);
    } finally {
      this.isProcessingPaymentOrder = false;
    }
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async processUnrefundedCardAdditions(): Promise<void> {
    if (this.isProcessingRefunds) {
      this.logger.log('Previous refund processing is still running, skipping...');
      return;
    }

    try {
      this.isProcessingRefunds = true;
      this.logger.log('Processing unrefunded successful card additions...');
      await this.onePayService.processUnrefundedSuccessfulCardAdditions();
    } catch (error) {
      this.logger.error('Error in scheduled refund processing:', error);
    } finally {
      this.isProcessingRefunds = false;
    }
  }
}
