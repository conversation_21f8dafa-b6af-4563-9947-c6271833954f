import type { INestApplication } from '@nestjs/common';
import basicAuth from 'express-basic-auth';

import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication, configService: ConfigService): void {
  const enableSwagger = configService.get<boolean>('app.enableSwagger');
  if (!enableSwagger) {
    return;
  }

  const swaggerUser = configService.get<string>('app.swaggerUser') || '';
  const swaggerPassword = configService.get<string>('app.swaggerPassword') || '';

  if (swaggerUser && swaggerPassword) {
    // Apply basic authentication to Swagger UI routes
    app.use(
      ['/docs', '/docs-json'], // Adjust these paths if your Swagger UI is on a different route
      basicAuth({
        challenge: true,
        users: {
          [swaggerUser]: swaggerPassword,
        },
      }),
    );
  }

  const documentBuilder = new DocumentBuilder()
    .setTitle('Anh Beo API')
    .setDescription('API Documentation for the Anh Beo Food Delivery Platform')
    .setVersion('0.0.1') // Use a static version string
    .addBearerAuth();

  const document = SwaggerModule.createDocument(app, documentBuilder.build());

  // Apply ApiCookieAuth to all endpoints except those marked with @Public()
  const paths = Object.values(document.paths);
  for (const path of paths) {
    for (const method of Object.values(path)) {
      // Skip if the endpoint is marked as public
      const isPublic = method.security && method.security.some((sec) => sec.public);
      if (method.security && isPublic) {
        method.security = [];
        continue;
      }

      const roles = method.security
        ?.filter((sec) => sec.roles)
        .map((sec) => sec.roles)
        .flat();
      if (roles?.length) {
        method.summary = [method.summary, `Roles: ${roles.map((e) => e.split(':')[0]).join(', ')}`]
          .filter((e) => !!e)
          .join(' | ');
      }
      method.security = method.security?.filter((sec) => !sec.roles) ?? [];

      // Add bearer auth to all other endpoints
      method.security.push({ bearer: [] });
    }
  }

  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      filter: true,
      persistAuthorization: true,
      displayOperationId: true,
      deepLinking: true,
      tagsSorter: 'alpha',
      tryItOutEnabled: true,
      displayRequestDuration: true,
      syntaxHighlight: {
        activated: true,
        theme: 'tomorrow-night',
      },
      operationsSorter: 'alpha',
      // docExpansion: 'none', // Collapse tags by default
    },
  });
}
