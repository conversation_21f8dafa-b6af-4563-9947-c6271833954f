import { Type } from 'class-transformer';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';

import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMenuDto {
  @ApiProperty({ description: 'name of the menu', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Array of menu section IDs with positions', required: false, type: [PositionItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemDto)
  menuSectionIds?: PositionItemDto[];
}
