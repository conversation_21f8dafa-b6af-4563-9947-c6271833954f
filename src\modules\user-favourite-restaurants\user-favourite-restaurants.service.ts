import { Repository } from 'typeorm';

import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { RestaurantsService } from '../restaurants/restaurants.service';
import { UserFavouriteRestaurant } from './entities/user-favourite-restaurant.entity';

@Injectable()
export class UserFavouriteRestaurantsService {
  constructor(
    @InjectRepository(UserFavouriteRestaurant)
    private userFavouriteRestaurantRepository: Repository<UserFavouriteRestaurant>,

    @Inject(forwardRef(() => RestaurantsService))
    private restaurantsService: RestaurantsService,
  ) {}

  async setFavourite(
    userId: string,
    restaurantId: string,
    favourite: boolean,
  ): Promise<{ isFavourite: boolean; message: string }> {
    // First, verify that the restaurant exists and is active
    await this.validateRestaurantForFavourite(restaurantId);

    // Check if the favourite already exists
    const existingFavourite = await this.userFavouriteRestaurantRepository.findOne({
      where: { userId, restaurantId },
    });

    if (favourite) {
      // User wants to add to favourites
      if (existingFavourite) {
        // Already favourited, no action needed
        return {
          isFavourite: true,
          message: 'Restaurant is already in favourites',
        };
      } else {
        // Add to favourites
        const newFavourite = this.userFavouriteRestaurantRepository.create({
          userId,
          restaurantId,
        });
        await this.userFavouriteRestaurantRepository.save(newFavourite);
        return {
          isFavourite: true,
          message: 'Restaurant added to favourites',
        };
      }
    } else {
      // User wants to remove from favourites
      if (existingFavourite) {
        // Remove from favourites
        await this.userFavouriteRestaurantRepository.softRemove(existingFavourite);
        return {
          isFavourite: false,
          message: 'Restaurant removed from favourites',
        };
      } else {
        // Already not favourited, no action needed
        return {
          isFavourite: false,
          message: 'Restaurant is not in favourites',
        };
      }
    }
  }

  private async validateRestaurantForFavourite(restaurantId: string): Promise<void> {
    // Check if restaurant exists and get its brand and merchant account info
    const restaurant = await this.restaurantsService.findRestaurantWithBrandAndMerchantAccount(restaurantId);

    if (!restaurant) {
      throw new NotFoundException('Restaurant not found');
    }

    // Check if brand is active
    if (!restaurant.brand?.activeAt) {
      throw new NotFoundException('Restaurant brand is not active');
    }

    // Check if merchant account is active
    if (!restaurant.brand?.merchantAccount?.activeAt) {
      throw new NotFoundException('Restaurant merchant account is not active');
    }
  }
}
