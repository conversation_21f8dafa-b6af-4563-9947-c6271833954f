import { Pagination } from 'nestjs-typeorm-paginate';

import { Public } from '@auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CreateRestaurantTagDto } from './dto/create-restaurant-tag.dto';
import { ListRestaurantTagDto } from './dto/list-restaurant-tag.dto';
import { UpdateRestaurantTagDto } from './dto/update-restaurant-tag.dto';
import { RestaurantTag } from './entities/restaurant-tag.entity';
import { RestaurantTagsService } from './restaurant-tags.service';

@ApiTags('Restaurant Tags')
@Controller('restaurant-tags')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class RestaurantTagsController {
  constructor(private readonly restaurantTagsService: RestaurantTagsService) {}

  @Public()
  @Get()
  findAll(@Query() listRestaurantTagDto: ListRestaurantTagDto): Promise<Pagination<RestaurantTag>> {
    return this.restaurantTagsService.findAll(listRestaurantTagDto);
  }

  @Public()
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<RestaurantTag> {
    return this.restaurantTagsService.findOne(id);
  }

  @Post()
  create(@Body() createRestaurantTagDto: CreateRestaurantTagDto) {
    return this.restaurantTagsService.create(createRestaurantTagDto);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRestaurantTagDto: UpdateRestaurantTagDto,
  ): Promise<RestaurantTag> {
    return this.restaurantTagsService.update(id, updateRestaurantTagDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.restaurantTagsService.remove(id);
  }
}
