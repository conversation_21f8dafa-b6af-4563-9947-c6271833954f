import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsSeenOrder1750997195224 implements MigrationInterface {
  name = 'AddIsSeenOrder1750997195224';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "is_user_seen" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "is_user_seen"`);
  }
}
