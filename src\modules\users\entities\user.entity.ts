import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Language } from '@/common/enums/language.enum';
import { UserFcmToken } from '@/modules/shared/fcm/entities/user-fcm-token.entity';

@Entity('users')
@Index(['phone', 'phoneCountryCode'], { unique: true, where: 'deleted_at IS NULL AND phone IS NOT NULL' })
export class User extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string | null;

  @Index()
  @Column({ type: 'varchar', nullable: true, default: '+84' })
  phoneCountryCode: string | null;

  @Column({ type: 'varchar', nullable: true })
  firstName: string;

  @Column({ type: 'varchar', nullable: true })
  lastName: string;

  @Column({ default: false })
  banned: boolean;

  @Column({ type: 'varchar', default: Language.EN })
  language: Language;

  @OneToMany(() => UserFcmToken, (fcmToken) => fcmToken.user)
  fcmTokens: UserFcmToken[];
}
