import { Is<PERSON><PERSON>, IsNotEmpty, IsString } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

const isDev = process.env.NODE_ENV === 'development';

export class LoginAdminDto {
  @ApiProperty({
    description: 'Email address',
    example: isDev ? process.env.ROOT_ANHBEO_ADMIN_EMAIL : undefined,
  })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({
    description: 'Password',
    example: isDev ? process.env.ROOT_ANHBEO_ADMIN_PASSWORD : undefined,
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
