import { Observable } from 'rxjs';

import { AppMode } from '@/common/enums/event-pattern.enum';
import { BadRequestException, CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DisableRoutesInterceptor implements NestInterceptor {
  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (AppMode.JOB === this.configService.get<string>('app.appMode')) {
      throw new BadRequestException('Block Access');
    }

    return next.handle();
  }
}
