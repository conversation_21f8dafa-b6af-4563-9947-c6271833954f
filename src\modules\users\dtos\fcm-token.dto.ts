import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { FcmPlatform } from '../enums/fcm-platform.enum';

export class AddFCMTokenDto {
  @ApiProperty({
    description: 'FCM token for push notifications',
    example: 'dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu...',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'Device ID',
    example: 'device-123',
  })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'Platform type',
    enum: FcmPlatform,
    example: FcmPlatform.ANDROID,
  })
  @IsEnum(FcmPlatform)
  platform: FcmPlatform;
}

export class RemoveFCMTokenDto {
  @ApiProperty({
    description: 'FCM token to remove',
    example: 'dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu...',
    required: false,
  })
  @IsString()
  @IsOptional()
  token?: string;

  @ApiProperty({
    description: 'Device ID to remove',
    example: 'device-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  deviceId?: string;
}
