import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>al, IsString, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class UpdateIngredientDto {
  @ApiProperty({ description: 'Internal name of the ingredient', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the ingredient', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  publishedName?: string;

  @ApiProperty({
    description: 'Array of menu item IDs to add this ingredient to',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuItemIds?: string[];
}
