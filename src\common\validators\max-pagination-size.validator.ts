import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

/**
 * Validates that page * limit does not exceed a maximum value
 * @param maxSize The maximum allowed value for page * limit
 * @param validationOptions Additional validation options
 * @returns PropertyDecorator
 */
export function MaxPaginationSize(maxSize: number, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'maxPaginationSize',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [maxSize],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [maxSize] = args.constraints;
          const object = args.object as any;

          // Get the limit value from the object
          const limit = object.limit || 10; // Default limit is 10

          // Calculate total size
          const totalSize = value * limit;

          // Check if total size exceeds max size
          return totalSize <= maxSize;
        },
        defaultMessage(args: ValidationArguments) {
          const [maxSize] = args.constraints;
          const object = args.object as any;
          const limit = object.limit || 10;
          return `page * limit (${args.value} * ${limit} = ${args.value * limit}) cannot exceed ${maxSize}`;
        },
      },
    });
  };
}
