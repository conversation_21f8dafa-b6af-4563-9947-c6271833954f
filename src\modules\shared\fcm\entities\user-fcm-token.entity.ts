import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { User } from '@/modules/users/entities/user.entity';
import { FcmPlatform } from '@/modules/users/enums/fcm-platform.enum';

@Entity('user_fcm_tokens')
@Index(['userId', 'deviceId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['userId'])
@Index(['token'])
export class UserFcmToken extends BaseEntity {
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column()
  token: string;

  @Column({ name: 'device_id', type: 'varchar' })
  deviceId: string;

  @Column({ type: 'varchar' })
  platform: FcmPlatform;

  @ManyToOne(() => User, (user) => user.fcmTokens)
  @JoinColumn({ name: 'user_id' })
  user: WrapperType<User>;
}
