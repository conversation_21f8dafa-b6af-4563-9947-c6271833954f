import { IsNotEmpty, IsString, <PERSON><PERSON>ength } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class RestaurantSuggestionDto {
  @ApiProperty({
    description: 'Text to search for suggestions. Supports both exact matching and fuzzy matching for typos.',
    example: 'pizza',
    minLength: 3,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3, { message: 'Search text must be at least 3 characters' })
  search: string;
}

export class SuggestionResponseDto {
  @ApiProperty({
    description:
      'List of suggestion texts (tags + restaurant names) ordered by relevance. Uses fuzzy matching to handle typos.',
    example: ['Pizza Hut', "Pizza 4P's", 'Fast Food', 'Italian'],
  })
  suggestions: string[];
}
