import { Type } from 'class-transformer';
import { <PERSON><PERSON>rray, IsBoolean, IsNumber, IsOptional, IsString, IsUUI<PERSON>, Max, <PERSON> } from 'class-validator';

import { ToArray, ToBoolean } from '@/common/decorators/transforms.decorator';
import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListRestaurantDto extends PaginationDto {
  @ApiProperty({ description: 'Search by published name and restaurant tags', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter restaurants that are currently open', required: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  openNow?: boolean;

  @ApiProperty({ description: 'Filter by minimum star rating', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  minStarRated?: number;

  @ApiProperty({ description: 'Filter by brand ID', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;

  @ApiProperty({ description: 'Filter by restaurant tags', required: false })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  @ToArray()
  @Type(() => String)
  restaurantTagIds?: string[];

  @ApiProperty({ description: 'Filter by rating', required: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  rating?: boolean;
}
