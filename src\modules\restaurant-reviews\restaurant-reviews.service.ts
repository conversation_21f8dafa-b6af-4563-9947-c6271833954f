import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, FindOptionsWhere, IsNull, Not, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { OrderStatus } from '@/modules/orders/constants/order.enums';
import { Order } from '@/modules/orders/entities/order.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { ReviewTag } from '@/modules/review-tags/entities/review-tag.entity';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { RestaurantAccessService } from '../shared/restaurant-access/restaurant-access.service';
import { CreateRestaurantReviewReplyDto } from './dto/create-restaurant-review-reply.dto';
import { CreateRestaurantReviewDto } from './dto/create-restaurant-review.dto';
import { ListRestaurantReviewDto } from './dto/list-restaurant-review.dto';
import { ListStaffRestaurantReviewDto } from './dto/list-staff-restaurant-review.dto';
import { MappingRestaurantReviewTag } from './entities/mapping-restaurant-review-tag.entity';
import { RestaurantReviewReply } from './entities/restaurant-review-reply.entity';
import { RestaurantReview } from './entities/restaurant-review.entity';

@Injectable()
export class RestaurantReviewsService {
  constructor(
    @InjectRepository(RestaurantReview)
    private readonly restaurantReviewRepository: Repository<RestaurantReview>,
    @InjectRepository(RestaurantReviewReply)
    private readonly restaurantReviewReplyRepository: Repository<RestaurantReviewReply>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly dataSource: DataSource,
    private readonly restaurantAccessService: RestaurantAccessService,
  ) {}

  /**
   * Update restaurant rating within transaction
   */
  private async updateRestaurantAverageRating(restaurantId: string, manager = this.dataSource.manager): Promise<void> {
    await manager
      .createQueryBuilder()
      .update(Restaurant)
      .set({
        starRated: () => `(
          SELECT COALESCE(ROUND(AVG(rating), 1), 0.0)
          FROM restaurant_reviews 
          WHERE restaurant_id = :restaurantId 
          AND deleted_at IS NULL
        )`,
        totalReviews: () => `(
          SELECT COUNT(*)
          FROM restaurant_reviews 
          WHERE restaurant_id = :restaurantId 
          AND deleted_at IS NULL
        )`,
      })
      .where('id = :restaurantId', { restaurantId })
      .execute();
  }

  async createRestaurantReview(userId: string, createReviewDto: CreateRestaurantReviewDto): Promise<RestaurantReview> {
    const { orderId, rating, comment, tagIds = [] } = createReviewDto;

    // Check if order exists and belongs to user
    const order = await this.orderRepository.findOne({
      where: { id: orderId, userId },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Check order status and completion
    if (order.status !== OrderStatus.DELIVERED) {
      throw new BadRequestException('Order must be delivered to be reviewed');
    }

    // Check if already reviewed
    const existingReview = await this.restaurantReviewRepository.findOne({
      where: { orderId, userId },
    });

    if (existingReview) {
      throw new BadRequestException('Order has already been reviewed');
    }

    return this.dataSource.transaction(async (manager) => {
      // Create review
      const review = await manager.save(RestaurantReview, {
        userId,
        restaurantId: order.restaurantId,
        orderId,
        rating,
        comment,
      });

      // Get unique tagIds to avoid duplicates
      const uniqueTagIds = [...new Set(tagIds)];

      // Create tag mappings if any
      if (uniqueTagIds.length > 0) {
        // Validate tagIds exist and are active
        const existingTags = await manager.find(ReviewTag, {
          where: uniqueTagIds.map((tagId) => ({
            id: tagId,
            active: true,
          })),
        });

        for (const tagId of uniqueTagIds) {
          const existingTag = existingTags.find((tag) => tag.id === tagId);
          if (!existingTag || !existingTag.ratings.some((rate) => rate.rating === rating)) {
            throw new BadRequestException(`Invalid or inactive tagId: ${tagId}`);
          }
        }

        const tagMappings = uniqueTagIds.map((tagId) => ({
          restaurantReviewId: review.id,
          reviewTagId: tagId,
        }));
        await manager.save(MappingRestaurantReviewTag, tagMappings);
      }

      // Update restaurant average rating
      await this.updateRestaurantAverageRating(order.restaurantId, manager);

      return review;
    });
  }

  async getRestaurantReviewStats(restaurantId: string) {
    // Get total reviews and average rating
    const reviewStats = await this.restaurantReviewRepository
      .createQueryBuilder('restaurantReview')
      .select(['COUNT(*) as "totalReviews"', 'AVG(restaurantReview.rating) as "averageRating"'])
      .where('restaurantReview.restaurantId = :restaurantId', { restaurantId })
      .getRawOne();

    const totalReviews = parseInt(reviewStats?.totalReviews || 0);
    const averageRating = parseFloat(reviewStats?.averageRating || 0);

    // Get rating distribution
    const ratingDistribution = await this.restaurantReviewRepository
      .createQueryBuilder('restaurantReview')
      .select(['restaurantReview.rating as rating', 'COUNT(*) as count'])
      .where('restaurantReview.restaurantId = :restaurantId', { restaurantId })
      .groupBy('restaurantReview.rating')
      .orderBy('restaurantReview.rating', 'DESC')
      .getRawMany();

    // Get tag statistics
    const reviewTagStats = await this.dataSource
      .createQueryBuilder()
      .select([
        'rt.id as "reviewTagId"',
        'rt.name as "reviewTagName"',
        'COUNT(mrrt.reviewTagId) as "count"',
        'ROUND((COUNT(mrrt.reviewTagId)::numeric / :totalReviews * 100), 0) as "percentage"',
      ])
      .from(ReviewTag, 'rt')
      .leftJoin(MappingRestaurantReviewTag, 'mrrt', 'mrrt.reviewTagId = rt.id')
      .leftJoin(RestaurantReview, 'rr', 'rr.id = mrrt.restaurantReviewId')
      .where('rr.restaurantId = :restaurantId', { restaurantId })
      .andWhere('rr.deletedAt IS NULL')
      .andWhere('rt.active = true')
      .andWhere('rt.type = :tagType', { tagType: 'positive' })
      .andWhere('mrrt.reviewTagId IS NOT NULL') // Only tags that have been used
      .groupBy('rt.id')
      .having('ROUND((COUNT(mrrt.reviewTagId)::numeric / :totalReviews * 100), 0) > 50')
      .orderBy('percentage', 'DESC')
      .limit(5)
      .setParameters({ restaurantId, totalReviews: totalReviews || 1 })
      .getRawMany();

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
      ratingDistribution: ratingDistribution.map((item) => ({
        rating: parseInt(item.rating),
        count: parseInt(item.count),
      })),
      reviewTagStats: reviewTagStats.map((stat) => ({
        reviewTagId: stat.reviewTagId,
        reviewTagName: stat.reviewTagName,
        count: parseInt(stat.count),
        percentage: parseInt(stat.percentage),
      })),
    };
  }

  async findRestaurantReviewList(
    restaurantId: string,
    query: ListRestaurantReviewDto,
  ): Promise<Pagination<RestaurantReview>> {
    const { rating, sortBy, sort, page, limit } = query;

    const queryBuilder = this.restaurantReviewRepository
      .createQueryBuilder('restaurantReview')
      .leftJoinAndSelect('restaurantReview.user', 'user')
      .leftJoinAndSelect('restaurantReview.replies', 'replies')
      .leftJoinAndSelect('replies.merchantStaff', 'merchantStaff')
      .leftJoinAndSelect('restaurantReview.order', 'order')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.orderItemOptions', 'orderItemOptions')
      .leftJoinAndSelect('restaurantReview.mappingRestaurantReviewTags', 'mappingRestaurantReviewTags')
      .leftJoinAndSelect('mappingRestaurantReviewTags.reviewTag', 'reviewTag')
      .where('restaurantReview.restaurantId = :restaurantId', { restaurantId });

    if (rating) {
      queryBuilder.andWhere('restaurantReview.rating = :rating', { rating });
    }

    queryBuilder.orderBy(`restaurantReview.${sortBy}`, sort);

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  // Staff methods
  async findRestaurantReviewsForMerchantUser(
    query: ListStaffRestaurantReviewDto,
    ownerId: string | null,
  ): Promise<Pagination<RestaurantReview>> {
    const { rating, sortBy, sort, page, limit, restaurantId } = query;

    await this.restaurantAccessService.verifyAccessRestaurant(restaurantId, ownerId);

    const queryBuilder = this.restaurantReviewRepository
      .createQueryBuilder('restaurantReview')
      .leftJoinAndSelect('restaurantReview.user', 'user')
      .leftJoinAndSelect('restaurantReview.order', 'order')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.orderItemOptions', 'orderItemOptions')
      .leftJoinAndSelect('restaurantReview.replies', 'replies')
      .where('restaurantReview.restaurantId = :restaurantId', { restaurantId });

    if (rating) {
      queryBuilder.andWhere('restaurantReview.rating = :rating', { rating });
    }

    queryBuilder.orderBy(`restaurantReview.${sortBy}`, sort);

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async createRestaurantReviewReplyByMerchantUser(
    createReplyDto: CreateRestaurantReviewReplyDto,
    ownerId: string | null,
  ): Promise<RestaurantReviewReply> {
    const { restaurantReviewId, comment } = createReplyDto;

    // Verify review exists and belongs to restaurant
    const review = await this.restaurantReviewRepository.findOne({
      where: { id: restaurantReviewId },
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    await this.restaurantAccessService.verifyAccessRestaurant(review.restaurantId, ownerId);

    // Check if reply count exceeds limit
    const replyCount = await this.restaurantReviewReplyRepository.count({
      where: { restaurantReviewId, merchantStaffId: Not(IsNull()) },
    });

    if (replyCount >= 50) {
      throw new BadRequestException('Review has reached maximum reply limit (50)');
    }

    return this.restaurantReviewReplyRepository.save({
      restaurantReviewId,
      comment,
    });
  }

  async findOneByWhere(where: FindOptionsWhere<RestaurantReview>) {
    return this.restaurantReviewRepository.findOne({ where });
  }

  async createRestaurantReviewReplyByUser(
    userId: string,
    createReplyDto: CreateRestaurantReviewReplyDto,
  ): Promise<RestaurantReviewReply> {
    const { restaurantReviewId, comment } = createReplyDto;

    // Verify review exists and belongs to restaurant
    const review = await this.restaurantReviewRepository.findOne({
      where: { id: restaurantReviewId },
      relations: ['order'],
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    if (review.order?.userId !== userId) {
      throw new BadRequestException('You are not allowed to reply to this review');
    }

    // Check if reply count exceeds limit
    const replyCount = await this.restaurantReviewReplyRepository.count({
      where: { restaurantReviewId, userId: Not(IsNull()) },
    });

    if (replyCount >= 50) {
      throw new BadRequestException('Review has reached maximum reply limit (50)');
    }

    return this.restaurantReviewReplyRepository.save({
      restaurantReviewId,
      userId,
      comment,
    });
  }
}
