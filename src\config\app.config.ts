import { AppMode } from '@/common/enums/event-pattern.enum';
import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  appMode: process.env.APP_MODE ?? AppMode.GENERAL,
  nodeEnv: process.env.NODE_ENV ?? 'development',
  port: parseInt(process.env.PORT ?? '3000', 10),
  enablePublicFolder: process.env.ENABLE_PUBLIC_FOLDER === 'true',
  enableSwagger: process.env.ENABLE_SWAGGER === 'true',
  swaggerUser: process.env.SWAGGER_USER,
  swaggerPassword: process.env.SWAGGER_PASSWORD,
}));
