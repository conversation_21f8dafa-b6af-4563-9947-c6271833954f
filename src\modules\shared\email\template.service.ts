import * as fs from 'fs/promises';
import * as Handlebars from 'handlebars';
import * as path from 'path';

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TemplateService implements OnModuleInit {
  private readonly logger = new Logger(TemplateService.name);
  private templates = new Map<string, Handlebars.TemplateDelegate>();
  private templateDir: string;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    this.templateDir = this.configService.get<string>('email.templatePath', './templates');
    await this.loadTemplates();
  }

  private async loadTemplates() {
    try {
      const templateFiles = await fs.readdir(this.templateDir);
      for (const file of templateFiles) {
        if (file.endsWith('.hbs')) {
          const templateName = path.basename(file, '.hbs');
          const templatePath = path.join(this.templateDir, file);
          const content = await fs.readFile(templatePath, 'utf-8');
          this.templates.set(templateName, Handlebars.compile(content));
          this.logger.log(`Loaded template: ${templateName}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to load email templates from ${this.templateDir}: ${error.message}`, error.stack);
      // Decide if this is critical - maybe throw an error?
    }
  }

  render(templateName: string, context: object): string {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found.`);
    }
    return template(context);
  }
}
