export interface CallbackCreateTokenOnepay {
  vpc_Command: string;

  vpc_MerchTxnRef: string;

  vpc_Merchant: string;

  vpc_OrderInfo: string;

  vpc_Amount: string;

  vpc_TxnResponseCode: string;

  vpc_Message: string;

  vpc_SecureHash: string;

  vpc_Version?: string;

  vpc_TokenNum: string;

  vpc_TokenExp: string;

  vpc_TransactionNo?: string;

  vpc_PayChannel?: string;

  vpc_Card?: string;

  vpc_CardUid: string;

  vpc_CardNum?: string;

  vpc_CardExp?: string;

  vpc_AuthorizeId?: string;

  vpc_AcqResponseCode?: string;

  vpc_CardHolderName?: string;

  vpc_ItaBank?: string;

  vpc_ItaFeeAmount?: string;

  vpc_ItaTime?: string;

  vpc_ItaMobile?: string;

  vpc_ItaEmail?: string;

  vpc_OrderAmount?: string;

  vpc_PaymentTime?: string;

  vpc_BinCountry?: string;

  user_CustomerId?: string;
}
