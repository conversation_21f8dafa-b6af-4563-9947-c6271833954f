import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAppVersionsTable1752812540551 implements MigrationInterface {
  name = 'AddAppVersionsTable1752812540551';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."app_versions_platform_enum" AS ENUM('ios', 'android')`);
    await queryRunner.query(
      `CREATE TABLE "app_versions" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "version" character varying NOT NULL, "build_number" integer NOT NULL, "platform" "public"."app_versions_platform_enum" NOT NULL DEFAULT 'android', "is_force_update" boolean NOT NULL DEFAULT false, "is_active" boolean NOT NULL DEFAULT true, "release_notes" text, "download_url" character varying, "released_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_8d36b0dcf0c026c7aad923c80fd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_764a2ab4e49ad36d1f9eca9cc9" ON "app_versions" ("platform") `);
    await queryRunner.query(`CREATE INDEX "IDX_a4ea236d6e82865f48820980f9" ON "app_versions" ("is_active") `);
    await queryRunner.query(`CREATE INDEX "IDX_b65e8d6b262ae8a2014e86c823" ON "app_versions" ("released_at") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_0b758e7f49dcbcfc9f1e88f812" ON "app_versions" ("platform", "version") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_0b758e7f49dcbcfc9f1e88f812"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b65e8d6b262ae8a2014e86c823"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a4ea236d6e82865f48820980f9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_764a2ab4e49ad36d1f9eca9cc9"`);
    await queryRunner.query(`DROP TABLE "app_versions"`);
    await queryRunner.query(`DROP TYPE "public"."app_versions_platform_enum"`);
  }
}
