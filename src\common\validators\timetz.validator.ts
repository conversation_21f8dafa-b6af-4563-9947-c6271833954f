import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { TIME_REGEX, TIMETZ_REGEX } from '../constants/time.constant';

/**
 * Custom validator for PostgreSQL timetz format
 * Validates time with timezone in format: HH:MM:SS+TZ or HH:MM:SS-TZ
 *
 * Valid examples:
 * - 10:30:00+07:00
 * - 14:15:30-05:00
 * - 23:59:59+00:00
 * - 00:00:00+12:00
 *
 * Invalid examples:
 * - 10:30 (missing seconds and timezone)
 * - 25:00:00+07:00 (invalid hour)
 * - 10:30:00+25:00 (invalid timezone)
 * - 10:30:00 (missing timezone)
 */
export function IsTimeTz(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isTimeTz',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          // If value is null or undefined, it's valid (handled by @IsOptional)
          if (value == null) {
            return true;
          }

          // Must be string
          if (typeof value !== 'string') {
            return false;
          }

          return TIMETZ_REGEX.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid time with timezone format (HH:MM:SS+TZ, e.g., 10:30:00+07:00)`;
        },
      },
    });
  };
}

export function IsTimeWithoutTz(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isTimeWithoutTz',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          // If value is null or undefined, it's valid (handled by @IsOptional)
          if (value == null) {
            return true;
          }

          // Must be string
          if (typeof value !== 'string') {
            return false;
          }

          return TIME_REGEX.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid time format (HH:MM, e.g., 10:30)`;
        },
      },
    });
  };
}
