import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { TranslateItemDto } from './translate-item.dto';

export class UpdateTranslatesDto {
  @ApiProperty({
    description: 'Array of translation items to update',
    type: [TranslateItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TranslateItemDto)
  items: TranslateItemDto[];
}
