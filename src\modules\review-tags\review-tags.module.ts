import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ReviewTag } from './entities/review-tag.entity';
import { ReviewTagsController } from './review-tags.controller';
import { ReviewTagsService } from './review-tags.service';

@Module({
  imports: [TypeOrmModule.forFeature([ReviewTag])],
  controllers: [ReviewTagsController],
  providers: [ReviewTagsService],
  exports: [ReviewTagsService],
})
export class ReviewTagsModule {}
