import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateReviewTag1751389634370 implements MigrationInterface {
  name = 'UpdateReviewTag1751389634370';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_cff991e11a4ba656fb30f58e50"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a8209057d1a2c75464d22765a"`);
    await queryRunner.query(`ALTER TABLE "mapping_restaurant_review_tags" DROP COLUMN "created_at"`);
    await queryRunner.query(`ALTER TABLE "mapping_restaurant_review_tags" DROP COLUMN "updated_at"`);
    await queryRunner.query(`ALTER TABLE "mapping_restaurant_review_tags" DROP COLUMN "deleted_at"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" DROP CONSTRAINT "PK_491eba079fc5e193b6b59e00570"`,
    );
    await queryRunner.query(`ALTER TABLE "mapping_restaurant_review_tags" DROP COLUMN "id"`);
    await queryRunner.query(`ALTER TABLE "review_tags" DROP COLUMN "display_order"`);
    await queryRunner.query(`ALTER TABLE "review_tags" ADD "ratings" jsonb NOT NULL DEFAULT '[]'`);
    await queryRunner.query(`ALTER TABLE "review_tags" ADD "type" character varying(20) NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD CONSTRAINT "PK_8ec69ba5e3c0958dff0f6f71cd2" PRIMARY KEY ("restaurant_review_id", "review_tag_id")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" DROP CONSTRAINT "PK_8ec69ba5e3c0958dff0f6f71cd2"`,
    );
    await queryRunner.query(`ALTER TABLE "review_tags" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "review_tags" DROP COLUMN "ratings"`);
    await queryRunner.query(`ALTER TABLE "review_tags" ADD "display_order" integer NOT NULL DEFAULT '0'`);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD CONSTRAINT "PK_491eba079fc5e193b6b59e00570" PRIMARY KEY ("id")`,
    );
    await queryRunner.query(`ALTER TABLE "mapping_restaurant_review_tags" ADD "deleted_at" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_review_tags" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a8209057d1a2c75464d22765a" ON "mapping_restaurant_review_tags" ("review_tag_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cff991e11a4ba656fb30f58e50" ON "mapping_restaurant_review_tags" ("restaurant_review_id") `,
    );
  }
}
