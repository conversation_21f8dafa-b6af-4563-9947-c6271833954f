import { IsEmail, IsEnum, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

import { MerchantUserRole } from '../enums/merchant-users-role.enum';

export class CreateMerchantUserDto {
  @ApiProperty({ description: 'Email of the merchant user' })
  @IsNotEmpty()
  @IsEmail()
  @ToLowerCase()
  email: string;

  @ApiProperty({ description: 'First name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Role of the merchant user', enum: MerchantUserRole, required: false })
  @IsOptional()
  @IsEnum(MerchantUserRole)
  role?: MerchantUserRole;

  @ApiProperty({ description: 'Last name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Password of the merchant user' })
  @IsNotEmpty()
  @IsString()
  password: string;
}
