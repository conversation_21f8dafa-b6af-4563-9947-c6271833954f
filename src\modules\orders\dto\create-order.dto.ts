import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, ValidateIf } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

import { PaymentMethod } from '../constants/order.enums';

export class CreateOrderDto {
  @ApiProperty({ description: 'Cart ID' })
  @IsNotEmpty()
  @IsUUID()
  cartId: string;

  @ApiProperty({ description: 'Note' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({ description: 'Payment method', enum: PaymentMethod })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: 'User address ID' })
  @IsOptional()
  @IsUUID()
  userAddressId?: string;

  @ApiProperty({ description: 'Include utensils', required: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  includeUtensils?: boolean;

  @ApiProperty({ description: 'Payment card ID', required: false })
  @IsOptional()
  @ValidateIf((o) => o.paymentMethod === PaymentMethod.CREDIT_CARD)
  @IsUUID()
  paymentCardId?: string;
}
