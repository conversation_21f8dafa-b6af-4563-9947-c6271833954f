import { IsInt, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UUI<PERSON>, Min } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class PositionItemDto {
  @ApiProperty({ description: 'Item ID' })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({ description: 'Position order', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  position: number;
}
