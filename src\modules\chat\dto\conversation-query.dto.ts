import { Type } from 'class-transformer';
import { <PERSON>N<PERSON><PERSON>, IsOptional, Min } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class ConversationQueryDto {
  @ApiProperty({ description: 'Limit number of results', minimum: 1, default: 20, required: false })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 20;

  @ApiProperty({ description: 'Timestamp cursor for pagination', required: false })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  cursor?: number; // Timestamp for lastMessageAt cursor
}
