import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import { MenuItem } from './entities/menu-item.entity';

export const localizeMenuItem = (menuItem: MenuItem, userLanguage: Language, forceRestaurantLanguage?: Language) => {
  const restaurantLanguage = forceRestaurantLanguage ?? menuItem?.restaurant?.defaultLanguage;
  LocalizationHelper.localizeMenuItemEntity(menuItem, userLanguage, restaurantLanguage);

  if (menuItem.mappingMenuSections) {
    for (const { menuSection } of menuItem.mappingMenuSections) {
      LocalizationHelper.localizeMenuItemEntity(menuSection, userLanguage, restaurantLanguage);
    }
  }

  if (menuItem.mappingMenuItemOptionGroups) {
    for (const { menuItemOptionGroup } of menuItem.mappingMenuItemOptionGroups) {
      LocalizationHelper.localizeMenuItemEntity(menuItemOptionGroup, userLanguage, restaurantLanguage);
      if (menuItemOptionGroup.mappingMenuItemOptions) {
        for (const { menuItemOption } of menuItemOptionGroup.mappingMenuItemOptions) {
          LocalizationHelper.localizeMenuItemEntity(menuItemOption, userLanguage, restaurantLanguage);
        }
      }
    }
  }
};
