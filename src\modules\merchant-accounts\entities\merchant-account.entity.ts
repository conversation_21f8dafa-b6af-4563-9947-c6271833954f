import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { MerchantUser } from '@/modules/merchant-users/entities/merchant-user.entity';

@Entity('merchant_accounts')
export class MerchantAccount extends BaseEntity {
  @Column({ type: 'varchar' })
  name: string;

  @Index()
  @Column({ name: 'owner_merchant_user_id', nullable: true, type: 'uuid' })
  ownerMerchantUserId?: string | null;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  // Many accounts can be owned by one user
  @ManyToOne(() => MerchantUser, (merchantUser) => merchantUser.merchantAccounts)
  @JoinColumn({ name: 'owner_merchant_user_id' })
  ownerMerchantUser?: WrapperType<MerchantUser> | null;

  @OneToMany(() => Brand, (brand) => brand.merchantAccount)
  brands: WrapperType<Brand>[];
}
