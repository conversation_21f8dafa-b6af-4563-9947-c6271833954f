import { Type } from 'class-transformer';
import {
  Is<PERSON>rray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PositionItemWithPricePositiveDto } from '@/common/dtos/position-item-with-price.dto';
import { IsTimeWithoutTz } from '@/common/validators/timetz.validator';
import { ApiProperty } from '@nestjs/swagger';

import { ViewType } from '../enums/view-type.enum';

export class ScheduleItem {
  @ApiProperty({ description: 'Day of the week (0-6, where 0 is Sunday)', example: 0 })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  @Max(6)
  day: number;

  @ApiProperty({ description: 'Start time (HH:MM format)', example: '10:00', required: false })
  @IsString()
  @IsTimeWithoutTz()
  start: string;

  @ApiProperty({ description: 'End time (HH:MM format)', example: '14:00', required: false })
  @IsString()
  @IsTimeWithoutTz()
  end: string;

  @ApiProperty({ description: 'Is open all day', example: false, default: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isAllDay?: boolean;
}

export class CreateMenuSectionDto {
  @ApiProperty({ description: 'Internal name of the menu section' })
  @IsNotEmpty()
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the menu section' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'Published name in English', required: false })
  @IsOptional()
  @IsString()
  publishedNameEn?: string;

  @ApiProperty({ description: 'Published name in Vietnamese', required: false })
  @IsOptional()
  @IsString()
  publishedNameVi?: string;

  @ApiProperty({
    description: 'View type of the menu section',
    enum: ViewType,
    example: ViewType.GRID,
    required: false,
  })
  @IsEnum(ViewType)
  viewType: ViewType;

  @ApiProperty({
    description: 'Available schedule for the menu section',
    required: false,
    type: [ScheduleItem],
    example: [{ day: 1, start: '00:00', end: '14:00', isAllDay: false }],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  availableSchedule?: ScheduleItem[];

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({
    description: 'Array of menu item IDs with positions and optional price',
    required: false,
    type: [PositionItemWithPricePositiveDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemWithPricePositiveDto)
  menuItemIds?: PositionItemWithPricePositiveDto[];

  @ApiProperty({ description: 'Array of menu IDs to add this section to', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuIds?: string[];
}
