export interface OnepayCreateTokenPayload {
  vpc_CreateToken: true;
  vpc_Customer_Id?: string; // max 64 characters

  vpc_Version: string; // default 2
  vpc_Currency: string; // default VND
  vpc_Command: string; // default pay
  vpc_AccessCode: string;
  vpc_Merchant: string;
  vpc_Locale: 'en' | 'vn'; // default vn
  vpc_ReturnURL: string;

  vpc_CardList: 'INTERCARD';

  vpc_MerchTxnRef: string; // max 40 characters

  vpc_OrderInfo: string; // max 34 characters
  vpc_Amount: string;
  vpc_TicketNo: string;
  AgainLink: string;
  Title: string;

  vpc_SecureHash?: string;
  vpc_Customer_Phone?: string;
  vpc_Customer_Email?: string;
}

export interface OnepayPaymentPayload {
  vpc_TokenNum: string;
  vpc_TokenExp: string;
  vpc_Customer_Id: string;

  vpc_Version: string; // default 2
  vpc_Currency: string; // default VND
  vpc_Command: string; // default pay
  vpc_AccessCode: string;
  vpc_Merchant: string;
  vpc_Locale: 'en' | 'vn'; // default vn

  vpc_VerType: '2D';
  vpc_MerchTxnRef: string; // max 40 characters

  vpc_OrderInfo: string; // max 34 characters
  vpc_Amount: string;
  vpc_TicketNo: string;

  vpc_Customer_Phone?: string;
  vpc_Customer_Email?: string;
}

export interface PaymentResponse {
  vpc_Amount: string;
  vpc_Card: string;
  vpc_CardExp: string;
  vpc_CardNum: string;
  vpc_CardUid: string;
  vpc_Command: string;
  vpc_MerchTxnRef: string;
  vpc_Merchant: string;
  vpc_Message: string;
  vpc_OrderInfo: string;
  vpc_PayChannel: string;
  vpc_TokenExp: string;
  vpc_TokenNum: string;
  vpc_TransactionNo: string;
  vpc_TxnResponseCode: OnePayTxnResponseCode;
  vpc_Version: string;
  vpc_BinCountry: string;
  vpc_PaymentTime: string;
  vpc_SecureHash: string;
}

export enum OnePayTxnResponseCode {
  SUCCESS = '0',
  PAYING = '100',
  PENDING = '300',
}

export enum PaymentOnepayStatus {
  SUCCESS = 'success',
  PENDING = 'pending',
  FAILED = 'failed',
}

export interface OnepayVoidPayload {
  vpc_Command: 'voidPurchase';
  vpc_Version: string; // default 2
  vpc_Amount: string | number;
  vpc_AccessCode: string;
  vpc_Merchant: string;
  vpc_MerchTxnRef: string; // max 40 characters
  vpc_OrgMerchTxnRef: string; // OnePay transaction number from original transaction
  vpc_Operator: string;
  vpc_SecureHash?: string;
}

export interface OnepayVoidResponse {
  vpc_Command: string;
  vpc_MerchTxnRef: string;
  vpc_OrgMerchTxnRef: string;
  vpc_Amount: string;
  vpc_Merchant: string;
  vpc_Operator: string;
  vpc_TxnResponseCode: string;
  vpc_Message: string;
  vpc_SecureHash: string;
  vpc_Version?: string;
}
