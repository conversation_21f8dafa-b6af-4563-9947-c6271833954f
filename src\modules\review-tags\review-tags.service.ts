import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ReviewTag } from './entities/review-tag.entity';

@Injectable()
export class ReviewTagsService {
  constructor(
    @InjectRepository(ReviewTag)
    private readonly reviewTagRepository: Repository<ReviewTag>,
  ) {}

  async getReviewTags(): Promise<ReviewTag[]> {
    return this.reviewTagRepository.find({ where: { active: true } });
  }
}
