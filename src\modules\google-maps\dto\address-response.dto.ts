import { ApiProperty } from '@nestjs/swagger';

export class AddressResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ example: 40.7128 })
  latitude: number;

  @ApiProperty({ example: -74.006 })
  longitude: number;

  @ApiProperty({ example: '123 Main St, New York, NY 10001, USA' })
  fullAddress: string;

  @ApiProperty({ example: '123', required: false })
  streetNumber?: string;

  @ApiProperty({ example: 'Main St', required: false })
  streetName?: string;

  @ApiProperty({ example: 'New York', required: false })
  city?: string;

  @ApiProperty({ example: 'NY', required: false })
  state?: string;

  @ApiProperty({ example: 'USA', required: false })
  country?: string;

  @ApiProperty({ example: '10001', required: false })
  postalCode?: string;

  @ApiProperty({ example: 'home' })
  addressType: string;

  @ApiProperty({ example: 'Home' })
  addressLabel: string;

  @ApiProperty({ example: 'Ring doorbell twice', required: false })
  deliveryInstructions?: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  updatedAt: Date;
}

export class GeocodeResponseDto {
  @ApiProperty({ example: 40.7128 })
  latitude: number;

  @ApiProperty({ example: -74.006 })
  longitude: number;

  @ApiProperty({ example: '123 Main St, New York, NY 10001, USA' })
  fullAddress: string;

  @ApiProperty({
    example: {
      streetNumber: '123',
      streetName: 'Main St',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      postalCode: '10001',
    },
  })
  addressComponents: any;

  @ApiProperty({ example: 'ChIJ...' })
  placeId: string;
}

export class ApiResponseDto<T> {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty()
  data?: T;

  @ApiProperty({ example: 'Operation completed successfully' })
  message?: string;

  @ApiProperty({ example: 'Error message', required: false })
  error?: string;
}
