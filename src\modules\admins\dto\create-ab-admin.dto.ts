import { IsEmail, IsEnum, <PERSON>Not<PERSON>mpty, IsOption<PERSON> } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

import { ABRole } from '../enums/admin-role.enum';

export class CreateAbAdminDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: 'John' })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ enum: ABRole, example: 'admin' })
  @IsEnum(ABRole)
  @IsOptional()
  role?: ABRole;
}
