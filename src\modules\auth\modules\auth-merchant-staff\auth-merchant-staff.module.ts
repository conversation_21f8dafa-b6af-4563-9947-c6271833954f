import { MerchantStaffModule } from '@/modules/merchant-staff/merchant-staff.module';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AuthMerchantStaffController } from './auth-merchant-staff.controller';
import { AuthMerchantStaffService } from './auth-merchant-staff.service';
import { MerchantStaffJwtStrategy } from './strategies/merchant-staff-jwt.strategy';

@Module({
  imports: [
    MerchantStaffModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('auth.merchantStaffJwtAccessSecret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.accessTokenExpiresIn'),
        },
      }),
    }),
  ],
  controllers: [AuthMerchantStaffController],
  providers: [AuthMerchantStaffService, MerchantStaffJwtStrategy],
  exports: [AuthMerchantStaffService],
})
export class AuthMerchantStaffModule {}
