import { MigrationInterface, QueryRunner } from 'typeorm';

export class UdpateFieldNamRestaurant1752254220101 implements MigrationInterface {
  name = 'UdpateFieldNamRestaurant1752254220101';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_dfeffbef9c31936dbac54733da"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "internal_name" character varying NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "published_name" character varying NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_edd073c215aed688883d9b5125" ON "restaurants" ("published_name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6975a531379c6bd79fe494cc10" ON "restaurants" ("brand_id", "internal_name") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_6975a531379c6bd79fe494cc10"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_edd073c215aed688883d9b5125"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "published_name"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "internal_name"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "name" character varying NOT NULL`);
    await queryRunner.query(`CREATE INDEX "IDX_dfeffbef9c31936dbac54733da" ON "restaurants" ("name") `);
  }
}
