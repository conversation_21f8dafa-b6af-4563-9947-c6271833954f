import { Type } from 'class-transformer';
import { IsInt, IsOptional, Min } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class MessagesQueryDto {
  @ApiProperty({ description: 'Limit number of messages', minimum: 1, default: 50, required: false })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 50;

  @ApiProperty({ description: 'Message ID cursor for pagination', required: false })
  @IsInt()
  @Type(() => Number)
  @IsOptional()
  cursor?: number; // Message ID cursor for pagination
}
