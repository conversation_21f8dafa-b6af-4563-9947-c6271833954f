import { Request, Response } from 'express';

import { User } from '@/common/decorators/user.decorator';
import { AdminsService } from '@/modules/admins/admins.service';
import { Public } from '@auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UserType } from '../../enums/user-type.enum';
import { AuthAdminService } from './auth-admin.service';
import { LoginAdminDto } from './dtos/login.dto';

@ApiTags('(Auth) Admin')
@Controller('admin/auth')
export class AuthAdminController {
  constructor(
    private readonly authAdminService: AuthAdminService,
    private adminsService: AdminsService,
  ) {}

  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginAdminDto, @Res({ passthrough: true }) response: Response) {
    return this.authAdminService.login(loginDto, response);
  }

  @Public()
  @Post('refresh')
  async refreshToken(@Req() request: Request, @Res({ passthrough: true }) response: Response) {
    return this.authAdminService.refreshToken(request, response);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Post('logout')
  logout(@Res({ passthrough: true }) response: Response) {
    return this.authAdminService.logout(response);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Get('me')
  getMe(@User() user) {
    return this.adminsService.getMe(user.id);
  }
}
