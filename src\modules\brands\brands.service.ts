import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';

import { generateCode } from '@/helpers/string';
import { MerchantAccountsService } from '@/modules/merchant-accounts/merchant-accounts.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateBrandDto } from './dtos/create-brand.dto';
import { ListBrandDto } from './dtos/list-brand.dto';
import { UpdateBrandDto } from './dtos/update-brand.dto';
import { Brand } from './entities/brand.entity';

@Injectable()
export class BrandsService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,

    private merchantAccountService: MerchantAccountsService,
  ) {}

  async create(createBrandDto: CreateBrandDto, ownerId: string | null): Promise<Brand> {
    // For merchant users, verify they own the merchant account they're trying to create a brand for
    // Check if the merchant account belongs to this user
    const merchantAccount = await this.merchantAccountService.getOne(createBrandDto.merchantAccountId, ownerId);

    if (!merchantAccount) {
      throw new NotFoundException(`You don't have permission to create a brand for this merchant account`);
    }

    const brand = this.brandRepository.create({ ...createBrandDto, code: generateCode(3), activeAt: new Date() });
    return this.brandRepository.save(brand);
  }

  async findAll(listBrandDto: ListBrandDto, ownerId: string | null): Promise<Pagination<Brand>> {
    const { name, merchantAccountId, page, limit } = listBrandDto;

    const queryBuilder = this.brandRepository.createQueryBuilder('brand');

    // Add relation to merchantAccount
    queryBuilder.leftJoinAndSelect('brand.merchantAccount', 'merchantAccount');

    if (ownerId) {
      // For merchant users, only show brands associated with their merchant accounts
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    if (merchantAccountId) {
      queryBuilder.andWhere('brand.merchantAccountId = :merchantAccountId', { merchantAccountId });
    }

    if (name) {
      queryBuilder.andWhere('brand.name ILIKE :name', { name: `%${name}%` });
    }

    // Order by most recently updated
    queryBuilder.orderBy('brand.updatedAt', 'DESC');

    const options: IPaginationOptions = { page, limit };

    return paginate<Brand>(queryBuilder, options);
  }

  async findOne(id: string, ownerId: string | null): Promise<Brand> {
    // Create query builder to handle user-specific conditions
    const queryBuilder = this.brandRepository
      .createQueryBuilder('brand')
      .leftJoinAndSelect('brand.merchantAccount', 'merchantAccount')
      .leftJoinAndSelect('brand.restaurants', 'restaurants')
      .where('brand.id = :id', { id });

    // For merchant users, only allow access to their own brands
    if (ownerId) {
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const brand = await queryBuilder.getOne();

    if (!brand) {
      throw new NotFoundException(`Brand with ID ${id} not found or you don't have access to it`);
    }

    return brand;
  }

  async update(id: string, updateBrandDto: UpdateBrandDto, ownerId: string | null): Promise<Brand> {
    const brand = await this.findOne(id, ownerId);

    // If we get here, the user has permission to update the brand
    Object.assign(brand, updateBrandDto);

    return this.brandRepository.save(brand);
  }

  async activate(id: string, ownerId: string | null): Promise<Brand> {
    const brand = await this.findOne(id, ownerId);

    // If we get here, the user has permission to activate the brand
    brand.activeAt = new Date();

    return this.brandRepository.save(brand);
  }

  async deactivate(id: string, ownerId: string | null): Promise<Brand> {
    const brand = await this.findOne(id, ownerId);

    // If we get here, the user has permission to deactivate the brand
    brand.activeAt = null;

    return this.brandRepository.save(brand);
  }

  async verifyAccessBrand(brandId: string, ownerId: string | null): Promise<void> {
    const queryBuilder = this.brandRepository
      .createQueryBuilder('brand')
      .select('brand.id')
      .where('brand.id = :brandId', { brandId });
    if (ownerId) {
      queryBuilder
        .innerJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const brand = await queryBuilder.getOne();

    if (!brand) {
      throw new NotFoundException(`You don't have permission to create a restaurant for this brand`);
    }
  }
}
