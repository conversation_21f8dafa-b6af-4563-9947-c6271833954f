import { Is<PERSON>rray, IsInt, IsNotEmpty, IsO<PERSON>al, IsString, Is<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CreateRestaurantReviewDto {
  @ApiProperty({ description: 'Order ID to review' })
  @IsNotEmpty()
  @IsUUID()
  orderId: string;

  @ApiProperty({ description: 'Rating from 1 to 5 stars' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Review comment', required: false })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiProperty({ description: 'Selected tag IDs', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  tagIds?: string[];
}
