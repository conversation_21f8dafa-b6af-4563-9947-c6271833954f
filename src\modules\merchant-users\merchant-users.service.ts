import * as bcrypt from 'bcrypt';
import { isBoolean } from 'lodash';
import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateMerchantUserDto } from './dtos/create-merchant-user.dto';
import { ListMerchantUserDto } from './dtos/list-merchant-user.dto';
import { UpdateMerchantUserDto } from './dtos/update-merchant-user.dto';
import { MerchantUser } from './entities/merchant-user.entity';

@Injectable()
export class MerchantUsersService {
  constructor(
    @InjectRepository(MerchantUser)
    private merchantUserRepository: Repository<MerchantUser>,
  ) {}

  async create(createMerchantUserDto: CreateMerchantUserDto): Promise<MerchantUser> {
    // Check if a user with the same email already exists
    const existingUser = await this.merchantUserRepository.findOne({
      where: { email: createMerchantUserDto.email.toLowerCase() },
    });

    if (existingUser) {
      throw new BadRequestException(`Email already exists`);
    }

    // Hash the password before saving
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(createMerchantUserDto.password, saltRounds);

    // Create a new object with the hashed password
    const merchantUserData = {
      ...createMerchantUserDto,
      password: hashedPassword,
    };

    const merchantUser = this.merchantUserRepository.create(merchantUserData);
    return this.merchantUserRepository.save(merchantUser);
  }

  getMe(id: string) {
    return this.merchantUserRepository.findOne({
      where: { id },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        activeAt: true,
        banned: true,
      },
    });
  }

  async findAll(listMerchantUserDto: ListMerchantUserDto): Promise<Pagination<MerchantUser>> {
    const { email, firstName, lastName, banned, page, limit } = listMerchantUserDto;

    const condition: FindOptionsWhere<MerchantUser> = {};
    if (email) {
      condition.email = ILike(`%${email}%`);
    }

    if (firstName) {
      condition.firstName = ILike(`%${firstName}%`);
    }

    if (lastName) {
      condition.lastName = ILike(`%${lastName}%`);
    }

    if (isBoolean(banned)) {
      condition.banned = banned;
    }
    const options: IPaginationOptions = { page, limit };

    return paginate<MerchantUser>(this.merchantUserRepository, options, {
      where: condition,
      relations: ['merchantAccounts'],
      select: {
        merchantAccounts: {
          id: true,
          name: true,
        },
      },
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  async findOne(id: string): Promise<MerchantUser> {
    // Use query builder to select only specific fields from the relation
    const merchantUser = await this.merchantUserRepository
      .createQueryBuilder('merchantUser')
      .leftJoinAndSelect('merchantUser.merchantAccounts', 'merchantAccounts')
      .select(['merchantUser', 'merchantAccounts.id', 'merchantAccounts.name'])
      .where('merchantUser.id = :id', { id })
      .getOne();

    if (!merchantUser) {
      throw new NotFoundException(`Merchant user with ID ${id} not found`);
    }

    return merchantUser;
  }

  async update(id: string, updateMerchantUserDto: UpdateMerchantUserDto): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    // If password is being updated, hash it
    if (updateMerchantUserDto.password) {
      const saltRounds = 10;
      updateMerchantUserDto.password = await bcrypt.hash(updateMerchantUserDto.password, saltRounds);
    }

    Object.assign(merchantUser, updateMerchantUserDto);

    return this.merchantUserRepository.save(merchantUser);
  }

  async activate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = new Date();

    return this.merchantUserRepository.save(merchantUser);
  }

  async deactivate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = null;

    return this.merchantUserRepository.save(merchantUser);
  }

  async ban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = true;

    return this.merchantUserRepository.save(merchantUser);
  }

  async unban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = false;

    return this.merchantUserRepository.save(merchantUser);
  }

  findOneByEmail(email: string) {
    return this.merchantUserRepository.findOne({ where: { email } });
  }

  findById(id: string) {
    return this.merchantUserRepository.findOne({ where: { id } });
  }
}
