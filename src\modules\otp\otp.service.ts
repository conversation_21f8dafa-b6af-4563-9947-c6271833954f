import { EmailService } from '@/modules/shared/email/email.service';
import { RedisService } from '@/modules/shared/redis/redis.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { OtpResponseDto } from './dto/otp-response.dto';
import { OtpPurpose, OtpType } from './otp.enums';

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);
  private readonly OTP_EXPIRY_MINUTES: number;

  constructor(
    private configService: ConfigService,
    private emailService: EmailService,
    private redisService: RedisService,
  ) {
    this.OTP_EXPIRY_MINUTES = this.configService.get<number>('otp.otpExpiryMinutes') || 5;
  }

  private createPhoneIdentifier(phone: string, phoneCountryCode: string): string {
    return `${phoneCountryCode}${phone}`;
  }

  private generateOtpCode(): string {
    // Generate a 6-digit random OTP
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private getOtpCacheKey(type: OtpType, identifier: string, purpose: OtpPurpose): string {
    return `otp:${type}:${identifier}:${purpose}`;
  }

  async generateOtp(
    type: OtpType,
    identifier: string,
    purpose: OtpPurpose = OtpPurpose.ONBOARDING,
  ): Promise<OtpResponseDto> {
    // Generate new OTP code
    const otpCode = this.generateOtpCode();

    // Calculate expiry time
    const expiresAt = new Date(Date.now() + this.OTP_EXPIRY_MINUTES * 60 * 1000);

    // Create cache key
    const cacheKey = this.getOtpCacheKey(type, identifier, purpose);

    // Store OTP in cache with TTL
    await this.redisService.setKey(cacheKey, otpCode, this.OTP_EXPIRY_MINUTES * 60);

    this.logger.log(`Generated ${type} OTP for ${identifier}: ${otpCode} (expires at: ${expiresAt.toISOString()})`);

    return new OtpResponseDto(otpCode, expiresAt, this.OTP_EXPIRY_MINUTES);
  }

  async verifyOtp(
    type: OtpType,
    identifier: string,
    code: string,
    purpose: OtpPurpose = OtpPurpose.ONBOARDING,
  ): Promise<boolean> {
    const cacheKey = this.getOtpCacheKey(type, identifier, purpose);

    // Get stored OTP from cache
    const storedOtp = await this.redisService.getKey(cacheKey);

    if (!storedOtp) {
      this.logger.warn(`OTP not found or expired for ${identifier}`);
      return false;
    }

    if (storedOtp === code) {
      // Delete OTP from cache after successful verification
      await this.redisService.deleteKey(cacheKey);
      this.logger.log(`OTP verified successfully for ${identifier}`);
      return true;
    }

    this.logger.warn(`Invalid OTP attempt for ${identifier}: ${code}`);
    return false;
  }

  async sendPhoneOtp(phone: string, phoneCountryCode: string): Promise<OtpResponseDto> {
    // Check rate limiting
    const isLimited = await this.isRateLimited(phone);
    if (isLimited) {
      throw new BadRequestException('Rate limit exceeded. Please try again later.');
    }

    const phoneIdentifier = this.createPhoneIdentifier(phone, phoneCountryCode);
    const otpResponse = await this.generateOtp(OtpType.PHONE, phoneIdentifier);

    // TODO: Integrate with SMS service
    this.logger.log(`SMS OTP ${otpResponse.code} would be sent to ${phoneIdentifier}`);

    return otpResponse;
  }

  async sendEmailOtp(email: string, isFake = true) {
    // Check rate limiting
    const isLimited = await this.isRateLimited(email);
    if (isLimited) {
      throw new BadRequestException('Rate limit exceeded. Please try again later.');
    }

    const { code, ...res } = await this.generateOtp(OtpType.EMAIL, email, OtpPurpose.ONBOARDING);

    try {
      // Use queue for better performance and error handling
      await this.emailService.queueOtpEmail(email, code, this.OTP_EXPIRY_MINUTES);
      this.logger.log(`Email OTP queued for ${email}`);
    } catch (error) {
      this.logger.error(`Failed to queue email OTP for ${email}:`, error);
      throw new BadRequestException('Failed to send OTP email');
    }

    return isFake ? { code, ...res } : res;
  }

  async sendUpdateEmailOtp(newEmail: string) {
    // Check rate limiting
    const isLimited = await this.isRateLimited(newEmail, OtpPurpose.UPDATE_EMAIL);
    if (isLimited) {
      throw new BadRequestException('Rate limit exceeded. Please try again later.');
    }

    const { code, ...res } = await this.generateOtp(OtpType.EMAIL, newEmail, OtpPurpose.UPDATE_EMAIL);

    try {
      await this.emailService.queueOtpEmail(newEmail, code, this.OTP_EXPIRY_MINUTES);
      this.logger.log(`Update email OTP queued for ${newEmail}`);
    } catch (error) {
      this.logger.error(`Failed to queue update email OTP for ${newEmail}:`, error);
      throw new BadRequestException('Failed to send OTP email');
    }

    // Don't return the code to client for security
    return res;
  }

  async sendUpdatePhoneOtp(phone: string, phoneCountryCode: string): Promise<OtpResponseDto> {
    // Check rate limiting
    const phoneIdentifier = this.createPhoneIdentifier(phone, phoneCountryCode);
    const isLimited = await this.isRateLimited(phoneIdentifier, OtpPurpose.UPDATE_PHONE);
    if (isLimited) {
      throw new BadRequestException('Rate limit exceeded. Please try again later.');
    }

    const otpResponse = await this.generateOtp(OtpType.PHONE, phoneIdentifier, OtpPurpose.UPDATE_PHONE);

    // TODO: Integrate with SMS service
    this.logger.log(`Update phone SMS OTP ${otpResponse.code} would be sent to ${phoneIdentifier}`);

    return otpResponse;
  }

  async verifyPhoneOtp(phone: string, code: string, phoneCountryCode: string): Promise<boolean> {
    const phoneIdentifier = this.createPhoneIdentifier(phone, phoneCountryCode);
    return this.verifyOtp(OtpType.PHONE, phoneIdentifier, code, OtpPurpose.ONBOARDING);
  }

  async verifyEmailOtp(email: string, code: string): Promise<boolean> {
    return this.verifyOtp(OtpType.EMAIL, email, code, OtpPurpose.ONBOARDING);
  }

  async verifyUpdateEmailOtp(newEmail: string, code: string): Promise<boolean> {
    return this.verifyOtp(OtpType.EMAIL, newEmail, code, OtpPurpose.UPDATE_EMAIL);
  }

  async verifyUpdatePhoneOtp(phone: string, code: string, phoneCountryCode: string): Promise<boolean> {
    const phoneIdentifier = this.createPhoneIdentifier(phone, phoneCountryCode);
    return this.verifyOtp(OtpType.PHONE, phoneIdentifier, code, OtpPurpose.UPDATE_PHONE);
  }

  private async isRateLimited(
    identifier: string,
    purpose: OtpPurpose = OtpPurpose.ONBOARDING,
    limit = 3,
    durationSec = 60,
  ): Promise<boolean> {
    const key = `otp:rate:${identifier}:${purpose}`;
    const count = await this.redisService.incrKey(key);
    if (count === 1) {
      await this.redisService.expireKey(key, durationSec);
    }
    return count > limit;
  }
}
