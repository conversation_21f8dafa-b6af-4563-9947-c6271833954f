import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateBulkGeofencingDto } from '../dto/create-geofencing.dto';
import { GeofencingService } from '../geofencing.service';

@ApiTags('geofencing')
@Controller('geofencing')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class GeofencingController {
  constructor(private readonly geofencingService: GeofencingService) {}

  @Post()
  @ApiOperation({ summary: 'Create/Update geofencing areas' })
  async create(@Body() createGeofencingDto: CreateBulkGeofencingDto, @UserMerchantId() ownerId: string | null) {
    return this.geofencingService.createOrUpdateBulk(createGeofencingDto, ownerId);
  }

  @Get('restaurant/:restaurantId')
  @ApiOperation({ summary: 'Get all geofencing areas for a restaurant' })
  async findByRestaurant(@Param('restaurantId') restaurantId: string, @UserMerchantId() ownerId: string | null) {
    return this.geofencingService.findByRestaurant(restaurantId, ownerId);
  }
}
