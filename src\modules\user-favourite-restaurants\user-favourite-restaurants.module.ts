import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RestaurantsModule } from '../restaurants/restaurants.module';
import { UserAddressesModule } from '../user-addresses/user-addresses.module';
import { UserFavouriteRestaurant } from './entities/user-favourite-restaurant.entity';
import { UserFavouriteRestaurantsController } from './user-favourite-restaurants.controller';
import { UserFavouriteRestaurantsService } from './user-favourite-restaurants.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserFavouriteRestaurant]),
    forwardRef(() => RestaurantsModule),
    UserAddressesModule,
  ],
  controllers: [UserFavouriteRestaurantsController],
  providers: [UserFavouriteRestaurantsService],
  exports: [UserFavouriteRestaurantsService],
})
export class UserFavouriteRestaurantsModule {}
