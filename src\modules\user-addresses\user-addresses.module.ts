import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { GoogleMapsService } from '../google-maps/google-maps.service';
import { UserAddress } from './entities/user-address.entity';
import { UserAddressesController } from './user-addresses.controller';
import { UserAddressesService } from './user-addresses.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserAddress])],
  controllers: [UserAddressesController],
  providers: [UserAddressesService, GoogleMapsService],
  exports: [UserAddressesService],
})
export class UserAddressesModule {}
