import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldSectionCartAndOrder1751480445308 implements MigrationInterface {
  name = 'AddFieldSectionCartAndOrder1751480445308';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_section_name" character varying NOT NULL`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_section_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "cart_items" ADD "menu_section_id" uuid NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "cart_items" ADD CONSTRAINT "FK_1e411326e3ca898ec2a457cca90" FOREIGN KEY ("menu_section_id") REFERENCES "menu_sections"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_1e411326e3ca898ec2a457cca90"`);
    await queryRunner.query(`ALTER TABLE "cart_items" DROP COLUMN "menu_section_id"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_section_id"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_section_name"`);
  }
}
