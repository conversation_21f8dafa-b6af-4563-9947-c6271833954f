import { DataSource, Repository } from 'typeorm';

import { ReviewTag } from '@/modules/review-tags/entities/review-tag.entity';
import { ReviewTagType } from '@/modules/review-tags/review-tags.enum';

import { Seeder } from './config/base.seeder';

const { POSITIVE, NEGATIVE } = ReviewTagType;

/**
 * Seeder for ReviewTag entity
 */
export class ReviewTag1750621006687 extends Seeder {
  private reviewTagRepository: Repository<ReviewTag>;

  constructor(dataSource: DataSource) {
    super(dataSource);
    this.reviewTagRepository = dataSource.getRepository(ReviewTag);
  }

  /**
   * Name of the seeder
   */
  get name(): string {
    return 'ReviewTag1750621006687';
  }

  /**
   * Execute seeder
   */
  async run(): Promise<void> {
    const reviewTags = {
      DELICIOUS_FOOD: { name: 'Delicious Food', type: POSITIVE },
      FAST_DELIVERY: { name: 'Fast Delivery', type: POSITIVE },
      GREAT_VALUE: { name: 'Great Value', type: POSITIVE },
      QUICK_COMMUNICATION: { name: 'Quick Communication', type: POSITIVE },
      FRIENDLY_SERVICE: { name: 'Friendly Service', type: POSITIVE },
      GREAT_PACKAGING: { name: 'Great Packaging', type: POSITIVE },
      FOLLOWED_INSTRUCTIONS: { name: 'Followed Instructions', type: POSITIVE },
      ON_TIME_DELIVERY: { name: 'On-Time Delivery', type: POSITIVE },
      GOOD_VALUE: { name: 'Good Value', type: POSITIVE },
      GOOD_FOOD: { name: 'Good Food', type: POSITIVE },
      SLOW_COMMUNICATION: { name: 'Slow Communication', type: NEGATIVE },
      SLIGHTLY_OVERPRICED: { name: 'Slightly Overpriced', type: NEGATIVE },
      MINOR_MISTAKES_IN_ORDER: { name: 'Minor Mistakes in Order', type: NEGATIVE },
      AVERAGE_FOOD: { name: 'Average Food', type: NEGATIVE },
      LATE_DELIVERY: { name: 'Late Delivery', type: NEGATIVE },
      SLIGHTLY_COLD_FOOD: { name: 'Slighty Cold Food', type: NEGATIVE },
      RUDE_SERVICE: { name: 'Rude Service', type: NEGATIVE },
      MESSY_PACKAGING: { name: 'Messy Packaging', type: NEGATIVE },
      BLAND_FOOD: { name: 'Bland Food', type: NEGATIVE },
      OVERPRICED: { name: 'Overpriced', type: NEGATIVE },
      COLD_FOOD: { name: 'Cold Food', type: NEGATIVE },
      LACK_OF_COMMUNICATION: { name: 'Lack of Communication', type: NEGATIVE },
      MISSING_ITEMS: { name: 'Missing Items', type: NEGATIVE },
      WRONG_ITEMS: { name: 'Wrong Items', type: NEGATIVE },
      TERIBLE_FOOD: { name: 'Terrible Food', type: NEGATIVE },
      VERY_LATE_DELIVERY: { name: 'Very Late Delivery', type: NEGATIVE },
      SPOILED_FOOD: { name: 'Spoiled Food', type: NEGATIVE },
      UNPROFESSIONAL_SERVICE: { name: 'Unprofessional Service', type: NEGATIVE },
      DAMAGED_PACKAGING: { name: 'Damaged Packaging', type: NEGATIVE },
    };

    const tagByStars = [
      {
        rating: 5,
        tags: [
          reviewTags.DELICIOUS_FOOD,
          reviewTags.FAST_DELIVERY,
          reviewTags.GREAT_VALUE,
          reviewTags.QUICK_COMMUNICATION,
          reviewTags.FRIENDLY_SERVICE,
          reviewTags.GREAT_PACKAGING,
          reviewTags.FOLLOWED_INSTRUCTIONS,
        ],
      },
      {
        rating: 4,
        tags: [
          reviewTags.DELICIOUS_FOOD,
          reviewTags.ON_TIME_DELIVERY,
          reviewTags.GOOD_VALUE,
          reviewTags.GOOD_FOOD,
          reviewTags.SLOW_COMMUNICATION,
          reviewTags.SLIGHTLY_OVERPRICED,
          reviewTags.FRIENDLY_SERVICE,
          reviewTags.MINOR_MISTAKES_IN_ORDER,
        ],
      },
      {
        rating: 3,
        tags: [
          reviewTags.AVERAGE_FOOD,
          reviewTags.LATE_DELIVERY,
          reviewTags.SLIGHTLY_OVERPRICED,
          reviewTags.SLIGHTLY_COLD_FOOD,
          reviewTags.SLOW_COMMUNICATION,
          reviewTags.RUDE_SERVICE,
          reviewTags.MESSY_PACKAGING,
          reviewTags.MINOR_MISTAKES_IN_ORDER,
        ],
      },
      {
        rating: 2,
        tags: [
          reviewTags.BLAND_FOOD,
          reviewTags.LATE_DELIVERY,
          reviewTags.OVERPRICED,
          reviewTags.COLD_FOOD,
          reviewTags.LACK_OF_COMMUNICATION,
          reviewTags.RUDE_SERVICE,
          reviewTags.MESSY_PACKAGING,
          reviewTags.MISSING_ITEMS,
          reviewTags.WRONG_ITEMS,
        ],
      },
      {
        rating: 1,
        tags: [
          reviewTags.TERIBLE_FOOD,
          reviewTags.VERY_LATE_DELIVERY,
          reviewTags.SPOILED_FOOD,
          reviewTags.LACK_OF_COMMUNICATION,
          reviewTags.UNPROFESSIONAL_SERVICE,
          reviewTags.DAMAGED_PACKAGING,
          reviewTags.MISSING_ITEMS,
          reviewTags.WRONG_ITEMS,
        ],
      },
    ];

    const reviewTagsEntity: ReviewTag[] = [];

    for (const { rating, tags } of tagByStars) {
      let index = 1;
      for (const tag of tags) {
        const existingTag = reviewTagsEntity.find((t) => t.name === tag.name);
        if (existingTag) {
          existingTag.ratings.push({ rating, displayOrder: index });
          continue;
        }
        const newReviewTag = this.reviewTagRepository.create({
          name: tag.name,
          ratings: [{ rating, displayOrder: index }],
          type: tag.type,
          active: true,
        });
        reviewTagsEntity.push(newReviewTag);
        index++;
      }
    }

    await this.reviewTagRepository.save(reviewTagsEntity);
  }
}
