import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class RequestUpdateEmailDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  newEmail: string;
}

export class VerifyUpdateEmailDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  newEmail: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  otp: string;
}

export class RequestUpdatePhoneDto {
  @ApiProperty({ example: '987654321' })
  @IsString()
  @IsNotEmpty()
  newPhone: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  newPhoneCountryCode: string;
}

export class VerifyUpdatePhoneDto {
  @ApiProperty({ example: '987654321' })
  @IsString()
  @IsNotEmpty()
  newPhone: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  newPhoneCountryCode: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  otp: string;
}
