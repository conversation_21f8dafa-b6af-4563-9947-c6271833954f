import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RestaurantTag } from './entities/restaurant-tag.entity';
import { RestaurantTagsController } from './restaurant-tags.controller';
import { RestaurantTagsService } from './restaurant-tags.service';

@Module({
  imports: [TypeOrmModule.forFeature([RestaurantTag])],
  controllers: [RestaurantTagsController],
  providers: [RestaurantTagsService],
  exports: [RestaurantTagsService],
})
export class RestaurantTagsModule {}
