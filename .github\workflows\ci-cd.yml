name: CI/CD Pipeline

on:
  push:
    branches:
      - develop
      - staging

env:
  AWS_REGION: ap-southeast-1
  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    environment: develop
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set ECR repository
        id: set-ecr-repository
        run: |
          if [ "${{ github.ref }}" == "refs/heads/develop" ]; then
            echo "ECR_REPOSITORY=${{ vars.AWS_ECR_REPOSITORY_DEV }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
            echo "ECR_REPOSITORY=${{ vars.AWS_ECR_REPOSITORY_STG }}" >> $GITHUB_OUTPUT
          fi

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ steps.set-ecr-repository.outputs.ECR_REPOSITORY }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - name: Set EC2 host
        id: set-ec2-host
        run: |
          if [ "${{ github.ref }}" == "refs/heads/develop" ]; then
            echo "EC2_HOST=${{ vars.EC2_HOST_DEV }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
            echo "EC2_HOST=${{ vars.EC2_HOST_STG }}" >> $GITHUB_OUTPUT
          fi

      - name: Deploy to EC2
        uses: appleboy/ssh-action@master
        env:
          # IMAGE_TAG: ${{ github.sha }}
          IMAGE_TAG: latest
        with:
          host: ${{ steps.set-ec2-host.outputs.EC2_HOST }}
          username: ${{ secrets.EC2_USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            set -e
            # Check if AWS CLI is installed, if not, install it
            if ! command -v aws &> /dev/null; then
              echo "AWS CLI not found, installing..."
              sudo apt-get update
              sudo apt-get install -y unzip
              curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
              unzip awscliv2.zip
              sudo ./aws/install
              rm -rf aws awscliv2.zip
              echo "AWS CLI installed successfully"
            fi
            echo "${{ env.IMAGE_TAG }}"
            sudo aws ecr get-login-password --region ${{ env.AWS_REGION }} | sudo docker login --username AWS --password-stdin ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com
            cd ~/anhbeo-be
            sudo docker compose stop api
            sudo docker compose rm -f api
            sudo IMAGE_TAG=${{ env.IMAGE_TAG }} docker compose up -d api
            echo "Waiting for database to be ready..."
            sleep 10
            echo "Running database migrations..."
            sudo docker compose exec -T api npm run migration:run:prod
            sudo docker system prune -f