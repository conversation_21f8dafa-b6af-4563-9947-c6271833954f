import { Public } from '@/modules/auth/decorators/public.decorator';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { FakeSendNotificationDto } from '../dto/fake-send-notification.dto';
import { FakeNotificationService } from '../fake-notification.service';

@ApiTags('🧪 Fake Notifications')
@Controller('fake-notifications')
@Public()
export class FakeNotificationController {
  constructor(private readonly fakeNotificationService: FakeNotificationService) {}

  @Get('notification-types')
  @ApiOperation({
    summary: 'Get available notification types',
    description: 'Get list of all available user and staff notification types',
  })
  getAvailableNotificationTypes() {
    return this.fakeNotificationService.getAvailableNotificationTypes();
  }

  @Post('send')
  @ApiOperation({
    summary: 'Send fake notification',
    description: 'Send fake notification to users and/or staff for testing. Feature must be enabled first.',
  })
  async sendFakeNotification(@Body() fakeSendDto: FakeSendNotificationDto) {
    const { orderId, userNotificationType, staffNotificationType, note } = fakeSendDto;

    return this.fakeNotificationService.sendFakeNotification(
      orderId,
      userNotificationType,
      staffNotificationType,
      note,
    );
  }
}
