import { In, Repository } from 'typeorm';

import { RestaurantAccessService } from '@/modules/shared/restaurant-access/restaurant-access.service';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateBulkGeofencingDto, GeofencingItemDto } from './dto/create-geofencing.dto';
import { Geofencing } from './entities/geofencing.entity';
import { GeofencingType } from './geofencing.types';

interface GeofencingQueryParams {
  restaurantId?: string;
  name: string;
  description: string | null;
  type: string;
  shippingFee: number;
  geometryData: string;
  geometryWKT: string;
  radiusInDegrees?: number;
  id?: string;
}

@Injectable()
export class GeofencingService {
  constructor(
    @InjectRepository(Geofencing)
    private readonly geofencingRepository: Repository<Geofencing>,
    private readonly restaurantAccessService: RestaurantAccessService,
  ) {}

  /**
   * Create/Update/Delete geofencing areas in bulk
   */
  async createOrUpdateBulk(createData: CreateBulkGeofencingDto, ownerId: string | null): Promise<Geofencing[]> {
    // Validate ownership
    await this.restaurantAccessService.verifyAccessRestaurant(createData.restaurantId, ownerId);

    // Get existing geofencing areas for this restaurant
    const existingGeofencings = await this.geofencingRepository.find({
      where: { restaurantId: createData.restaurantId },
    });

    const newGeofencings: Geofencing[] = [];
    const updatedIds = new Set<string>();

    // Process each geofencing area in the request
    for (const item of createData.geofencingAreas) {
      const geofencing = await this.processGeofencingItem(item, createData.restaurantId, existingGeofencings);
      if (geofencing) {
        newGeofencings.push(geofencing);
        updatedIds.add(geofencing.id);
      }
    }

    // Delete geofencing areas that are not in the new list
    const deletedIds = existingGeofencings.filter((g) => !updatedIds.has(g.id)).map((g) => g.id);
    if (deletedIds.length > 0) {
      await this.geofencingRepository.softDelete({ id: In(deletedIds) });
    }

    return newGeofencings;
  }

  /**
   * Process individual geofencing item (create or update)
   */
  private async processGeofencingItem(
    item: GeofencingItemDto,
    restaurantId: string,
    existingGeofencings: Geofencing[],
  ): Promise<Geofencing | null> {
    let existingGeofencing: Geofencing | undefined;

    // Find existing geofencing by id only
    if (item.id) {
      existingGeofencing = existingGeofencings.find((g) => g.id === item.id);
    }

    const queryParams = this.prepareQueryParams(item, restaurantId, existingGeofencing?.id);

    if (existingGeofencing) {
      // Update existing geofencing
      await this.executeUpdateQuery(queryParams);
      return this.geofencingRepository.findOne({ where: { id: existingGeofencing.id } });
    } else {
      // Create new geofencing
      const result = await this.executeInsertQuery(queryParams);
      return this.geofencingRepository.findOne({ where: { id: result.id } });
    }
  }

  /**
   * Prepare common query parameters
   */
  private prepareQueryParams(item: GeofencingItemDto, restaurantId?: string, id?: string): GeofencingQueryParams {
    const geometryWKT = this.convertToPostGISGeometry(item.geometryData);
    const isCircle = geometryWKT.startsWith('__CIRCLE__');

    const params: GeofencingQueryParams = {
      name: item.name,
      description: item.description || null,
      type: item.geometryData.type,
      shippingFee: item.shippingFee,
      geometryData: JSON.stringify(item.geometryData),
      geometryWKT,
    };

    if (restaurantId) params.restaurantId = restaurantId;
    if (id) params.id = id;

    if (isCircle) {
      const { radiusInDegrees } = this.parseAndValidateCircle(geometryWKT);
      params.radiusInDegrees = radiusInDegrees;
      // Convert circle marker to POINT for SQL
      params.geometryWKT = this.convertCircleToPoint(geometryWKT);
    }

    return params;
  }

  /**
   * Execute insert query based on geometry type
   */
  private async executeInsertQuery(params: GeofencingQueryParams): Promise<{ id: string }> {
    const isCircle = params.radiusInDegrees !== undefined;

    const baseFields = 'restaurant_id, name, description, type, shipping_fee, geometry_data, geometry';
    const basePlaceholders = '$1, $2, $3, $4, $5, $6';

    let geometryFunction: string;
    const queryParams = [
      params.restaurantId,
      params.name,
      params.description,
      params.type,
      params.shippingFee,
      params.geometryData,
      params.geometryWKT,
    ];

    if (isCircle) {
      geometryFunction = `ST_Buffer(ST_GeomFromText($7, 4326), $8)`;
      queryParams.push(params.radiusInDegrees);
    } else {
      geometryFunction = `ST_GeomFromText($7, 4326)`;
    }

    const sql = `INSERT INTO geofencing (${baseFields}) VALUES (${basePlaceholders}, ${geometryFunction}) RETURNING id`;
    const result = await this.geofencingRepository.query(sql, queryParams);

    return result[0];
  }

  /**
   * Execute update query based on geometry type
   */
  private async executeUpdateQuery(params: GeofencingQueryParams): Promise<void> {
    if (!params.id) {
      throw new Error('Geofencing ID is required');
    }

    const baseFields = `name = $1, description = $2, type = $3, shipping_fee = $4, geometry_data = $5`;

    let geometryField: string;
    const queryParams = [
      params.name,
      params.description,
      params.type,
      params.shippingFee,
      params.geometryData,
      params.geometryWKT,
    ];

    // Circle
    if (params.radiusInDegrees !== undefined) {
      geometryField = `geometry = ST_Buffer(ST_GeomFromText($6, 4326), $7)`;
      queryParams.push(params.geometryWKT, params.radiusInDegrees);
    } else {
      geometryField = `geometry = ST_GeomFromText($6, 4326)`;
      queryParams.push(params.geometryWKT);
    }

    queryParams.push(params.id);
    const sql = `UPDATE geofencing SET ${baseFields}, ${geometryField}, updated_at = NOW() WHERE id = $${queryParams.length}`;
    await this.geofencingRepository.query(sql, queryParams);
  }

  /**
   * Convert circle WKT to POINT format
   */
  private convertCircleToPoint(geometryWKT: string): string {
    const [lng, lat] = geometryWKT.replace('__CIRCLE__', '').split(',');
    return `POINT(${lng} ${lat})`;
  }

  /**
   * Parse and validate circle parameters
   */
  private parseAndValidateCircle(geometryWKT: string): { lng: string; lat: string; radiusInDegrees: number } {
    const [lng, lat, radius] = geometryWKT.replace('__CIRCLE__', '').split(',');
    const radiusInDegrees = parseFloat(radius) / 111320;

    // Validate radius values
    if (!lng || !lat || !radius || isNaN(parseFloat(radius)) || parseFloat(radius) <= 0) {
      throw new Error(`Invalid circle parameters: lng=${lng}, lat=${lat}, radius=${radius}`);
    }

    if (isNaN(radiusInDegrees) || radiusInDegrees <= 0 || !isFinite(radiusInDegrees)) {
      throw new Error(`Invalid radius in degrees: ${radiusInDegrees} (original radius: ${radius})`);
    }

    return { lng, lat, radiusInDegrees };
  }

  /**
   * Check if a point is inside any geofencing area for a restaurant
   */
  async findGeofencingForPoint(restaurantId: string, lat: number, lng: number): Promise<Geofencing[]> {
    // Using PostGIS ST_Contains function for spatial query
    const geofencings = await this.geofencingRepository
      .createQueryBuilder('g')
      .where('g.restaurantId = :restaurantId', { restaurantId })
      .andWhere('ST_Contains(g.geometry, ST_GeomFromText(:point, 4326))', {
        point: `POINT(${lng} ${lat})`,
      })
      .getMany();

    return geofencings;
  }

  /**
   * Get all geofencing areas for a restaurant
   */
  async findByRestaurant(restaurantId: string, ownerId: string | null): Promise<Geofencing[]> {
    // Validate ownership
    await this.restaurantAccessService.verifyAccessRestaurant(restaurantId, ownerId);
    return this.geofencingRepository.find({
      where: { restaurantId },
      order: { shippingFee: 'DESC', createdAt: 'DESC' },
    });
  }

  /**
   * Convert geometry data to PostGIS format based on type
   */
  convertToPostGISGeometry(geometryData: any): string {
    const { type, snapshot } = geometryData;

    switch (type) {
      case GeofencingType.POLYGON:
        return this.createPolygonWKT(snapshot.path);

      case GeofencingType.CIRCLE:
        // Return special marker for circle - will be handled in SQL
        return `__CIRCLE__${snapshot.center.lng},${snapshot.center.lat},${snapshot.radius}`;

      case GeofencingType.RECTANGLE:
        return this.createRectangleWKT(snapshot.bounds);

      default:
        throw new Error(`Unsupported geometry type: ${type}`);
    }
  }

  /**
   * Create polygon WKT from path points
   */
  private createPolygonWKT(path: Array<{ lat: number; lng: number }>): string {
    //TODO: validate them nếu các điểm nằm trên 1 đường thẳng thì throw error
    if (path.length < 3) {
      throw new Error('Polygon must have at least 3 points');
    }

    // Close the polygon by adding the first point at the end if not already closed
    const closedPath = [...path];
    const firstPoint = path[0];
    const lastPoint = path[path.length - 1];

    if (firstPoint.lat !== lastPoint.lat || firstPoint.lng !== lastPoint.lng) {
      closedPath.push(firstPoint);
    }

    const coordinates = closedPath.map((point) => `${point.lng} ${point.lat}`).join(', ');

    return `POLYGON((${coordinates}))`;
  }

  /**
   * Create rectangle WKT from bounds
   */
  private createRectangleWKT(bounds: { south: number; west: number; north: number; east: number }): string {
    const { south, west, north, east } = bounds;

    // Create rectangle as polygon with 4 corners + closing point
    return `POLYGON((${west} ${south}, ${east} ${south}, ${east} ${north}, ${west} ${north}, ${west} ${south}))`;
  }
}
