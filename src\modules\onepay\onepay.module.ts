import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { OrdersModule } from '../orders/orders.module';
import { PaymentCardModule } from '../payment-card/payment-card.module';
import { OnepayCreateTokenRequest } from './entities/onepay-create-token-request.entity';
import { OnepayOrderRequest } from './entities/onepay-order-request.entity';
import { OnepayVoidRequest } from './entities/onepay-void-request.entity';
import { OnePayController } from './onepay.controller';
import { OnePayService } from './onepay.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([OnepayCreateTokenRequest, OnepayOrderRequest, OnepayVoidRequest]),
    forwardRef(() => PaymentCardModule),
    forwardRef(() => OrdersModule),
  ],
  controllers: [OnePayController],
  providers: [OnePayService],
  exports: [OnePayService],
})
export class OnepayModule {}
