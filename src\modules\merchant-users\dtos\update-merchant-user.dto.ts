import { IsEnum, IsOptional, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { MerchantUserRole } from '../enums/merchant-users-role.enum';

export class UpdateMerchantUserDto {
  @ApiProperty({ description: 'First name of the merchant user', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: 'Last name of the merchant user', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: 'Role of the merchant user', enum: MerchantUserRole, required: false })
  @IsOptional()
  @IsEnum(MerchantUserRole)
  role?: MerchantUserRole;

  @ApiProperty({ description: 'Password of the merchant user', required: false })
  @IsOptional()
  @IsString()
  password?: string;
}
