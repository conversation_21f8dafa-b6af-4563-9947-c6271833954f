import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuItemOptionGroupDto } from './dtos/create-menu-item-option-group.dto';
import { ListMenuItemOptionGroupDto } from './dtos/list-menu-item-option-group.dto';
import { UpdateMenuItemOptionGroupDto } from './dtos/update-menu-item-option-group.dto';
import { MenuItemOptionGroup } from './entities/menu-item-option-group.entity';
import { MenuItemOptionGroupsService } from './menu-item-option-groups.service';

@ApiTags('Menu Item Option Groups')
@Controller('menu-item-option-groups')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenuItemOptionGroupsController {
  constructor(private readonly menuItemOptionGroupsService: MenuItemOptionGroupsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu item option group name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuItemOptionGroupsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(
    @Body() createMenuItemOptionGroupDto: CreateMenuItemOptionGroupDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    return this.menuItemOptionGroupsService.create(createMenuItemOptionGroupDto, ownerId);
  }

  @Get()
  findAll(
    @Query() listMenuItemOptionGroupDto: ListMenuItemOptionGroupDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<MenuItemOptionGroup>> {
    return this.menuItemOptionGroupsService.findAll(listMenuItemOptionGroupDto, ownerId);
  }

  @Get(':id')
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    return this.menuItemOptionGroupsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuItemOptionGroupDto: UpdateMenuItemOptionGroupDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    return this.menuItemOptionGroupsService.update(id, updateMenuItemOptionGroupDto, ownerId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a menu item option group and all its relations' })
  delete(
    @Param('id', ParseUUIDPipe) id: string,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    return this.menuItemOptionGroupsService.delete(id, ownerId);
  }
}
