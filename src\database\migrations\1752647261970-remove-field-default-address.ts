import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveFieldDefaultAddress1752647261970 implements MigrationInterface {
  name = 'RemoveFieldDefaultAddress1752647261970';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_5b1afc35ebbc50959dab3aac6e"`);
    await queryRunner.query(`ALTER TABLE "user-addresses" DROP COLUMN "is_default"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user-addresses" ADD "is_default" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `CREATE INDEX "IDX_5b1afc35ebbc50959dab3aac6e" ON "user-addresses" ("user_id", "is_default") `,
    );
  }
}
