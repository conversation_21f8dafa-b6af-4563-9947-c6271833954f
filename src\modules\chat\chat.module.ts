import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { OrdersModule } from '../orders/orders.module';
import { ChatController } from './controllers/chat.controller';
import { ChatService } from './chat.service';
import { ChatConversation } from './entities/chat-conversation.entity';
import { ChatMessage } from './entities/chat-message.entity';
import { ChatMicroserviceController } from './controllers/chat.microservice.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ChatConversation, ChatMessage]), OrdersModule],
  controllers: [ChatController, ChatMicroserviceController],
  providers: [ChatService],
  exports: [ChatService],
})
export class ChatModule {}
