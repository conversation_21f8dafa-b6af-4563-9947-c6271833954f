import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginMerchantDto {
  @ApiProperty({ description: 'Email address' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ description: 'Password' })
  @IsString()
  @IsNotEmpty()
  password: string;
}
