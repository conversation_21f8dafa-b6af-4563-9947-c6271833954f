import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationDto } from '@/common/dtos/pagination.dto';

export class ListBrandDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Filter by merchant account ID', required: false })
  @IsOptional()
  @IsUUID()
  merchantAccountId?: string;
}
