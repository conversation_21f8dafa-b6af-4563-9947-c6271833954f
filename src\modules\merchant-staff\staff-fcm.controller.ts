import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { FCMService } from '@/modules/shared/fcm/fcm.service';
import { Body, Controller, Delete, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { AddStaffFCMTokenDto, RemoveStaffFCMTokenDto } from './dtos/staff-fcm-token.dto';

@ApiTags('(Staff) FCM Tokens')
@Controller('staff/fcm-tokens')
@Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
export class StaffFCMController {
  constructor(private readonly fcmService: FCMService) {}

  @Post()
  @ApiOperation({ summary: 'Add FCM token for staff' })
  async addFCMToken(@User() user: UserMerchantStaffJwtInfo, @Body() addStaffFCMTokenDto: AddStaffFCMTokenDto) {
    return this.fcmService.addStaffFCMToken(user.id, addStaffFCMTokenDto);
  }

  @Delete()
  @ApiOperation({ summary: 'Remove FCM token for staff' })
  async removeFCMToken(@User() user: UserMerchantStaffJwtInfo, @Body() removeStaffFCMTokenDto: RemoveStaffFCMTokenDto) {
    return this.fcmService.removeStaffFCMToken(user.id, removeStaffFCMTokenDto);
  }
}
