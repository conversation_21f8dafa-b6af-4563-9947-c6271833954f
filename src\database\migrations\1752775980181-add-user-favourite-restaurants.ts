import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserFavouriteRestaurants1752775980181 implements MigrationInterface {
  name = 'AddUserFavouriteRestaurants1752775980181';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_favourite_restaurants" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, CONSTRAINT "PK_d92325fb669b5121c28d759380f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a6ea8735a2b5ce2f792ff7196" ON "user_favourite_restaurants" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d544d2bc07b3107ac0dde71d35" ON "user_favourite_restaurants" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ed7ad42e6e81e3312ce45f57f0" ON "user_favourite_restaurants" ("user_id", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_favourite_restaurants" ADD CONSTRAINT "FK_3a6ea8735a2b5ce2f792ff71965" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_favourite_restaurants" ADD CONSTRAINT "FK_d544d2bc07b3107ac0dde71d35e" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_favourite_restaurants" DROP CONSTRAINT "FK_d544d2bc07b3107ac0dde71d35e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_favourite_restaurants" DROP CONSTRAINT "FK_3a6ea8735a2b5ce2f792ff71965"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_ed7ad42e6e81e3312ce45f57f0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d544d2bc07b3107ac0dde71d35"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a6ea8735a2b5ce2f792ff7196"`);
    await queryRunner.query(`DROP TABLE "user_favourite_restaurants"`);
  }
}
