import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Patch, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { ListMerchantStaffDto } from './dtos/list-merchant-staff.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { MerchantStaffService } from './merchant-staff.service';

@ApiTags('Merchant Staff')
@Controller('merchant-staff')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MerchantStaffController {
  constructor(private readonly merchantStaffService: MerchantStaffService) {}

  @Get()
  findAll(@Query() listMerchantStaffDto: ListMerchantStaffDto, @UserMerchantId() ownerId: string | null) {
    return this.merchantStaffService.findAll(listMerchantStaffDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @UserMerchantId() ownerId: string | null) {
    return this.merchantStaffService.findOne(id, ownerId);
  }

  @Patch(':id/change-password')
  updatePassword(
    @Param('id') id: string,
    @Body() updatePasswordDto: UpdatePasswordDto,
    @UserMerchantId() ownerId: string | null,
  ) {
    return this.merchantStaffService.updatePassword(id, updatePasswordDto, ownerId);
  }

  @Put('ban/:id')
  ban(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null) {
    return this.merchantStaffService.ban(id, ownerId);
  }

  @Put('unban/:id')
  unban(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null) {
    return this.merchantStaffService.unban(id, ownerId);
  }
}
