import { Exclude, Expose } from 'class-transformer';
import { isNil } from 'lodash';
import { Column, Entity, Generated, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { MappingMenuMenuSection } from './mapping-menu-menu-section.entity';

@Entity('menus')
@Index(['code', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['name', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class Menu extends BaseEntity {
  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @Generated('uuid')
  @Index({ unique: true })
  @Column({ name: 'code', type: 'uuid' })
  code: string;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant?: WrapperType<Restaurant>;

  @Expose()
  get menuSections() {
    const dataSort = this.mappingMenuSections
      ?.filter((m) => !isNil(m.menuSection))
      .map((m) => {
        m.menuSection.position = m.position;
        return m.menuSection;
      })
      .sort((a, b) => a.position - b.position);
    return dataSort;
  }

  @Exclude()
  @OneToMany(() => MappingMenuMenuSection, (mappingMenuSection) => mappingMenuSection.menu)
  mappingMenuSections?: WrapperType<MappingMenuMenuSection>[];

  // Virtual fields for counts
  sectionsCount?: number;
  itemsCount?: number;
  itemOutOfStockCount?: number;
}
