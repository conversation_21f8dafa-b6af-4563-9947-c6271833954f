# OnePay Payment Gateway Integration Documentation

## Overview

This documentation covers the OnePay payment gateway integration for handling card payments, including regular payments, tokenization, and installment payments. The integration provides a secure callback mechanism (IPN - Instant Payment Notification) to receive payment status updates from OnePay.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [API Endpoints](#api-endpoints)
3. [Callback Parameters](#callback-parameters)
4. [Security](#security)
5. [Implementation Guide](#implementation-guide)
6. [Error Handling](#error-handling)
7. [Testing](#testing)
8. [Examples](#examples)

## Architecture Overview

### Payment Flow

```
Customer → Your Website → OnePay Gateway → Bank → OnePay Gateway → IPN Callback → Your Server
```

1. Customer initiates payment on your website
2. You redirect customer to OnePay payment gateway
3. Customer completes payment on OnePay
4. OnePay sends IPN callback to your server with payment result
5. Your server processes the callback and updates order status

### Key Components

- **Controller** (`OnePayController`): Handles HTTP endpoints for receiving OnePay callbacks
- **Service** (`OnePayService`): Processes payment callbacks and verifies security
- **DTO** (`CallbackOnepay`): Validates and structures callback data

## API Endpoints

### IPN Endpoint

**URL**: `GET /onepay/ipn`  
**Authentication**: Public endpoint (no authentication required)  
**Content-Type Response**: `text/plain`

This endpoint receives payment notifications from OnePay. OnePay will call this endpoint when a payment is completed (successfully or failed).

#### Success Response
```
responsecode=1&desc=confirm-success
```

#### Error Response
```
responsecode=0&desc=error-description
```

**Important**: OnePay will retry failed callbacks up to 3 times with 50-second intervals.

### Example IPN URL
```
https://api-dev.anhbeo.com/onepay/ipn?vpc_Command=pay&vpc_MerchTxnRef=TXN123456&vpc_Merchant=MERCHANT001&vpc_OrderInfo=ORDER123&vpc_Amount=10000&vpc_TxnResponseCode=0&vpc_Message=Approved&vpc_SecureHash=ABC123...
```
note: front-end send GET Method to OnePay URL, then OnePay will send GET METHOD to backend. Process the data received from backend and return to front-end.

## Callback Parameters

### Required Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vpc_Command` | string | Command sent to payment gateway | "pay" |
| `vpc_MerchTxnRef` | string | Your unique transaction reference | "TXN123456" |
| `vpc_Merchant` | string | Your merchant ID | "MERCHANT001" |
| `vpc_OrderInfo` | string | Your order reference | "ORDER123" |
| `vpc_Amount` | string | Amount in smallest currency unit (cents) | "10000" (= $100.00) |
| `vpc_TxnResponseCode` | string | Response code ("0" = success) | "0" |
| `vpc_Message` | string | Response message | "Approved" |
| `vpc_SecureHash` | string | HMAC-SHA256 hash for verification | "ABC123..." |

### Optional Parameters

#### Basic Transaction Info
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vpc_Version` | string | OnePay API version | "2" |
| `vpc_TransactionNo` | string | OnePay's unique transaction ID | "123456789" |
| `vpc_PayChannel` | string | Payment channel (WEB/APP) | "WEB" |
| `vpc_PaymentTime` | string | Transaction timestamp | "20240115143052" |

#### Card Information
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vpc_Card` | string | Card type (VC/MC/JC/AE/CUP or BIN) | "VC" |
| `vpc_CardUid` | string | Unique card identifier | "ABE4864268F5..." |
| `vpc_CardNum` | string | Masked card number | "412345xxxxxx1234" |
| `vpc_BinCountry` | string | Card issuing country | "VN" |

#### Tokenization
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vpc_TokenNum` | string | Token for saved card | "412345xxx123" |
| `vpc_TokenExp` | string | Token expiry (MMYY format) | "1225" |

#### Installment Payments
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vpc_ItaBank` | string | Bank SWIFT code | "VNIBVNVX" |
| `vpc_ItaFeeAmount` | string | Installment fee (in cents) | "5000" |
| `vpc_ItaTime` | string | Installment period (months) | "6" |
| `vpc_ItaMobile` | string | Customer phone | "**********" |
| `vpc_ItaEmail` | string | Customer email | "<EMAIL>" |
| `vpc_OrderAmount` | string | Original amount before fees | "100000" |
| `vpc_CardHolderName` | string | Cardholder name | "NGUYEN VAN A" |

#### Custom Parameters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `user_Xxxxxx` | string | Your custom parameters | "user_CustomerId=12345" |

### Response Codes

Common `vpc_TxnResponseCode` values:

| Code | Description |
|------|-------------|
| 0 | Transaction Successful |
| 1 | Transaction could not be processed |
| 2 | Bank Declined Transaction |
| 3 | No Reply from Bank |
| 4 | Expired Card |
| 5 | Insufficient Funds |
| 6 | Error Communicating with Bank |
| 7 | Payment Server System Error |
| 8 | Transaction Type Not Supported |
| 9 | Bank declined transaction (Do not contact Bank) |

## Security

### Hash Verification

All callbacks include a `vpc_SecureHash` parameter that must be verified to ensure the request is from OnePay.

#### Verification Process

1. Extract all parameters starting with `vpc_` or `user_` (except `vpc_SecureHash` and `vpc_SecureHashType`)
2. Sort parameters alphabetically by key name (case-sensitive)
3. Create string in format: `key1=value1&key2=value2&key3=value3`
4. Generate HMAC-SHA256 hash using your secret key
5. Compare with received `vpc_SecureHash` (case-insensitive)

#### Configuration

Set your OnePay secure hash code in environment variables:
```bash
ONEPAY_SECURE_HASH_CODE=your_hex_encoded_secret_key
```

## Implementation Guide

### 1. Environment Setup

Add to your `.env` file:
```bash
# OnePay Configuration
ONEPAY_MERCHANT_ID=your_merchant_id
ONEPAY_ACCESS_CODE=your_access_code
ONEPAY_SECURE_HASH_CODE=your_secure_hash_code
```

### 2. Database Schema

You'll need to implement these tables/entities:

```typescript
// Payment Transaction
interface PaymentTransaction {
  id: string;
  merchantTxnRef: string;         // vpc_MerchTxnRef
  orderId: string;                // vpc_OrderInfo
  amount: number;                 // vpc_Amount / 100
  status: 'PENDING' | 'SUCCESS' | 'FAILED';
  responseCode: string;           // vpc_TxnResponseCode
  message: string;                // vpc_Message
  gatewayTransactionId?: string;  // vpc_TransactionNo
  createdAt: Date;
  updatedAt: Date;
}

// Saved Payment Cards
interface PaymentCard {
  id: string;
  userId: string;
  tokenNumber: string;      // vpc_TokenNum
  tokenExpiry: string;      // vpc_TokenExp
  cardType: string;         // vpc_Card
  maskedCardNumber: string; // vpc_CardNum
  isDefault: boolean;
  createdAt: Date;
}
```

### 3. Required Service Implementations

The following methods in `OnePayService` need to be implemented based on your business logic:

#### Update Payment Status
```typescript
async updatePaymentStatus(paymentInfo: any) {
  // 1. Find the payment transaction by merchantTxnRef
  // 2. Update status based on responseCode
  // 3. Store gateway transaction ID
  // 4. Update related order status
}
```

#### Handle Successful Payment
```typescript
async handleSuccessfulPayment(paymentInfo: any) {
  // 1. Update order to PAID status
  // 2. Send confirmation email to customer
  // 3. Trigger fulfillment process
  // 4. Save payment card if token provided
  // 5. Send webhooks to other services
}
```

#### Handle Failed Payment
```typescript
async handleFailedPayment(paymentInfo: any) {
  // 1. Update order to FAILED status
  // 2. Send failure notification to customer
  // 3. Log failure reason for analysis
  // 4. Release any reserved inventory
}
```

#### Check Pending Payment (QuerryDR Request)
```typescript
async checkPendingPayment(paymentInfo: any) {
  // 1. Call OnePay Api check payment not response after 5 minutes
  // 2. handle SusscessfulPayment or handle FailedPayment
}
```
Note: Link -> 01 -> 4.1
```
https://drive.google.com/drive/u/1/folders/1tjQsVhucHWLh1qFwH7CNrCYp4CpOOaLC
```

## Error Handling

### Callback Retries

OnePay will retry failed callbacks:
- Maximum retries: 3 times
- Retry interval: 50 seconds

To stop retries, always return HTTP 200 with appropriate response body.

### Common Issues

1. **Invalid Hash**
   - Check your secure hash code configuration
   - Ensure all parameters are included in hash calculation
   - Verify parameter sorting is correct

2. **Missing Required Parameters**
   - OnePay may omit optional parameters
   - Always check for undefined values
   - Use optional chaining or default values

3. **Duplicate Callbacks**
   - Implement idempotency using `vpc_MerchTxnRef`
   - Check if transaction already processed
   - Return success to prevent retries

## Testing

### Test Card Numbers

Contact OnePay for test card numbers. Common test scenarios:

| Scenario | Card Number | CVV | Expiry | Card Holder Name | Card Type |
|----------|-------------|-----|---------|-----------------|-----------|
| Success | (9704360000000000002) | 123456 | 01/13 | NGUYEN VAN A | VCB-ATM |
| Success | (****************) | 123456 | 05/2026 | - | VISA |
| Success | (****************) | 123456 | 05/2026 | - | VISA |
| Insufficient Funds | (Contact OnePay) | 123 | 12/25 | Contact | Contact |
| Expired Card | (Contact OnePay) | 123 | 12/20 | Contact | Contact |

### Manual Testing

use PostMan or Front-end call API Onepay, then check log backend
url EXAMPLE GET METHOD:

```
https://mtf.onepay.vn/paygate/vpcpay.op?vpc_Version=2&vpc_Currency=VND&vpc_AccessCode=6BEB2546&vpc_Merchant=TESTDEFAULT&vpc_Locale=vn&vpc_ReturnURL=https://api-dev.anhbeo.com/onepay/ipn&vpc_MerchTxnRef=test11211512&vpc_OrderInfo=ord1234&vpc_Amount=3000000&vpc_TicketNo=111&vpc_SecureHash={{secureHash}}&vpc_Command=pay&vpc_CreateToken=false&vpc_Customer_Id=11223330011&vpc_TokenNum=9704368625035135&vpc_TokenExp=0629
```

note: {{secureHash}} need to generate from all different params

```
pm.sendRequest("https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/crypto-js.min.js", (err, res) => {
    pm.collectionVariables.set("js_lib1", res.text());
    pm.sendRequest("https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/hmac-sha256.min.js", (err, res) => {
        pm.collectionVariables.set("js_lib2", res.text());
        pm.sendRequest("https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/enc-base64.min.js", (err, res) => {
            pm.collectionVariables.set("js_lib3", res.text());

            eval(pm.collectionVariables.get("js_lib1"));
            eval(pm.collectionVariables.get("js_lib2"));
            eval(pm.collectionVariables.get("js_lib3"));
            let reqData = pm.request.url.query;
            console.log("reqData = " + reqData);
            let fieldNames = new Array;
            for (let i = 0; i < reqData.count(); i++) {
                fieldNames[i] = reqData.idx(i).key;
            }
            fieldNames.sort()
            let notEncodedData = "";
            const time = new Date();
            let a = time.getTime();

            let merchTXNRef = reqData.get('vpc_MerchTxnRef');

            if (merchTXNRef == null || merchTXNRef == "{{merTxnRef}}") {
                merchTXNRef = "TEST_" + a;
                pm.variables.set('merTxnRef', merchTXNRef);
            }

            for (let j = 0; j < fieldNames.length; j++) {
                let pref4 = fieldNames[j][0] + fieldNames[j][1] + fieldNames[j][2] + fieldNames[j][3];
                let pref5 = fieldNames[j][0] + fieldNames[j][1] + fieldNames[j][2] + fieldNames[j][3] + fieldNames[j][4];
                if (pref4 == "vpc_" || pref5 == "user_") {
                    if (fieldNames[j] != "vpc_SecureHashType" && fieldNames[j] != "vpc_SecureHash") {
                        for (let i = 0; i < reqData.count(); i++) {
                            if (fieldNames[j] == reqData.idx(i).key) {
                                if (notEncodedData.length > 0) { notEncodedData += "&" }
                                if (fieldNames[j] == "vpc_MerchTxnRef") {
                                    notEncodedData += fieldNames[j] + "=" + merchTXNRef;
                                } else {
                                    notEncodedData += fieldNames[j] + "=" + reqData.idx(i).value;
                                }
                            };
                        }
                    }
                }
            }
            console.log("notEncodedData = " + notEncodedData);
            // console.log("merchTXNRef = "+ merchTXNRef);
            let secKey = CryptoJS.enc.Hex.parse(merchTXNRef);
            var key = CryptoJS.enc.Hex.parse("6D0870CDE5F24F34F3915FB0045120DB");
            var hash = CryptoJS.HmacSHA256(notEncodedData, key);
            var hashInHex = CryptoJS.enc.Hex.stringify(hash).toUpperCase();
            pm.environment.set("secureHash", hashInHex);
            console.log("vpc_SecureHash = " + hashInHex + " ;length: " + hashInHex.length);
        })
    })
})
```

## Best Practices

1. **Always verify the secure hash** before processing any callback
2. **Implement idempotency** to handle duplicate callbacks gracefully
3. **Log all callbacks** for debugging and reconciliation
4. **Return proper response format** to control OnePay retry behavior
5. **Handle missing optional parameters** gracefully
6. **Store raw callback data** for audit trail
7. **Use environment variables** for all sensitive configuration
8. **Implement monitoring** for payment success rates
9. **Set up alerts** for high failure rates or hash verification failures
10. **Test all payment scenarios** including edge cases

## Support

For OnePay technical support and documentation:
- Contact your OnePay account manager
- Refer to OnePay's official integration guide
- Test in OnePay's sandbox environment before going live

## Changelog

- **v1.0.0** - Initial implementation with basic payment callback handling
- Support for card tokenization
- Support for installment payments
- Comprehensive parameter handling