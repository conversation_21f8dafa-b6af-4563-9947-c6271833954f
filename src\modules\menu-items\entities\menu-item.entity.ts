import { Exclude, Expose } from 'class-transformer';
import { isNil } from 'lodash';
import { Column, Entity, Generated, Index, JoinColumn, ManyToMany, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { Ingredient } from '@/modules/ingredients/entities/ingredient.entity';
import { MappingMenuItemOptionGroupMenuItemOption } from '@/modules/menu-item-option-groups/entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MappingMenuSectionMenuItem } from '@/modules/menu-sections/entities/mapping-menu-section-menu-item.entity';
import { MenuSection } from '@/modules/menu-sections/entities/menu-section.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { MenuItemType } from '../menu-items.constants';
import { MappingMenuItemMenuItemOptionGroup } from './mapping-menu-item-menu-item-option-group.entity';

@Entity('menu_items')
@Index(['code', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['internalName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['publishedName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class MenuItem extends BaseEntity {
  @Generated('uuid')
  @Index({ unique: true })
  @Column({ name: 'code', type: 'uuid' })
  code: string;

  @Column({ name: 'internal_name', type: 'varchar' })
  internalName: string;

  @Index()
  @Column({ name: 'published_name', type: 'varchar' })
  publishedName: string;

  @Exclude()
  @Column({ name: 'published_name_en', type: 'varchar', nullable: true })
  publishedNameEn?: string | null;

  @Exclude()
  @Column({ name: 'published_name_vi', type: 'varchar', nullable: true })
  publishedNameVi?: string | null;

  @Column({ nullable: true, type: 'varchar' })
  description?: string | null;

  @Exclude()
  @Column({ name: 'description_en', type: 'varchar', nullable: true })
  descriptionEn?: string | null;

  @Exclude()
  @Column({ name: 'description_vi', type: 'varchar', nullable: true })
  descriptionVi?: string | null;

  @Column({
    name: 'base_price',
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  basePrice: number;

  @Column({ name: 'type', type: 'enum', enum: MenuItemType, default: MenuItemType.ITEM })
  type: MenuItemType;

  @Column({ name: 'is_alcohol', type: 'boolean', default: false })
  isAlcohol: boolean;

  @Column({ name: 'image_urls', type: 'json', default: [] })
  imageUrls: string[];

  @Index()
  @Column({ name: 'total_orders_sold', type: 'integer', default: 0 })
  totalOrdersSold: number;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @Index()
  @Column({ name: 'schedule_active_at', nullable: true, type: 'timestamptz' })
  scheduleActiveAt?: Date | null;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @Expose()
  @ManyToMany(() => Ingredient, (ingredient) => ingredient.menuItems)
  ingredients?: WrapperType<Ingredient>[];

  @Expose()
  get menuSections() {
    return this.mappingMenuSections
      ?.filter((m) => !isNil(m.menuSection))
      .map((m) => {
        m.menuSection.itemPrice = m.price;
        return m.menuSection;
      });
  }

  @Exclude()
  @OneToMany(() => MappingMenuSectionMenuItem, (menuSection) => menuSection.menuItem)
  mappingMenuSections?: WrapperType<MappingMenuSectionMenuItem>[];

  @Expose()
  get menuItemOptionGroups() {
    return this.mappingMenuItemOptionGroups
      ?.filter((m) => !isNil(m.menuItemOptionGroup))
      .map((m) => {
        m.menuItemOptionGroup.position = m.position;
        return m.menuItemOptionGroup;
      })
      .sort((a, b) => a.position - b.position);
  }

  @Exclude()
  @OneToMany(() => MappingMenuItemMenuItemOptionGroup, (m) => m.menuItem)
  mappingMenuItemOptionGroups?: WrapperType<MappingMenuItemMenuItemOptionGroup>[];

  @Expose()
  get optionOfMenuItemOptionGroups() {
    return this.mappingMenuItemOptionGroupMenuItemOption
      ?.filter((m) => !isNil(m.menuItemOptionGroup))
      .map((m) => {
        m.menuItemOptionGroup.optionPrice = m.price;
        return m.menuItemOptionGroup;
      });
  }

  @Exclude()
  @OneToMany(() => MappingMenuItemOptionGroupMenuItemOption, (m) => m.menuItemOption)
  mappingMenuItemOptionGroupMenuItemOption?: WrapperType<MappingMenuItemOptionGroupMenuItemOption>[];

  // field not in database
  position: number;
  price?: number | null;
  menuSection?: WrapperType<MenuSection>;
}
