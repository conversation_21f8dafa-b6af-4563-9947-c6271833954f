import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class GeocodeDto {
  @ApiProperty({
    description: 'Address to geocode',
    example: '123 Main St, New York, NY',
  })
  @IsString()
  @IsNotEmpty()
  address: string;
}

export class ReverseGeocodeDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 40.7128,
  })
  @Type(() => Number)
  @IsNumber()
  latitude: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: -74.006,
  })
  @Type(() => Number)
  @IsNumber()
  longitude: number;
}
