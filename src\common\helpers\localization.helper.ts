import { Language } from '@/common/enums/language.enum';

export interface MenuLocalizableEntity {
  publishedName?: string;
  publishedNameVi?: string | null;
  publishedNameEn?: string | null;
  description?: string | null;
  descriptionVi?: string | null;
  descriptionEn?: string | null;
  defaultLanguage?: Language;
}

export interface OrderItemLocalizableEntity {
  menuItemName?: string;
  menuItemNameVi?: string | null;
  menuItemNameEn?: string | null;
  menuItemDescription?: string | null;
  menuItemDescriptionVi?: string | null;
  menuItemDescriptionEn?: string | null;
  menuSectionName?: string;
  menuSectionNameVi?: string | null;
  menuSectionNameEn?: string | null;
}

export interface OrderItemOptionLocalizableEntity {
  optionName?: string;
  optionNameVi?: string | null;
  optionNameEn?: string | null;
  optionGroupName?: string;
  optionGroupNameVi?: string | null;
  optionGroupNameEn?: string | null;
}

export class LocalizationHelper {
  static getLocalizedPublishedName(entity: MenuLocalizableEntity, userLanguage: Language): string | null | undefined {
    // If entity has defaultLanguage and it matches userLanguage, return the original publishedName
    if (entity.defaultLanguage && entity.defaultLanguage === userLanguage) {
      return entity.publishedName;
    }

    switch (userLanguage) {
      case Language.EN:
        return entity.publishedNameEn;
      case Language.VI:
        return entity.publishedNameVi;
      default:
        return entity.publishedName;
    }
  }

  static getLocalizedDescription(entity: MenuLocalizableEntity, userLanguage: Language): string | null | undefined {
    // If entity has defaultLanguage and it matches userLanguage, return the original description
    if (entity.defaultLanguage && entity.defaultLanguage === userLanguage) {
      return entity.description;
    }

    switch (userLanguage) {
      case Language.EN:
        return entity.descriptionEn;
      case Language.VI:
        return entity.descriptionVi;
      default:
        return entity.description;
    }
  }

  // Order Item localization methods
  static getLocalizedMenuItemName(
    entity: OrderItemLocalizableEntity,
    userLanguage: Language,
  ): string | null | undefined {
    switch (userLanguage) {
      case Language.EN:
        return entity.menuItemNameEn;
      case Language.VI:
        return entity.menuItemNameVi;
      default:
        return entity.menuItemName;
    }
  }

  static getLocalizedMenuItemDescription(
    entity: OrderItemLocalizableEntity,
    userLanguage: Language,
  ): string | null | undefined {
    switch (userLanguage) {
      case Language.EN:
        return entity.menuItemDescriptionEn;
      case Language.VI:
        return entity.menuItemDescriptionVi;
      default:
        return entity.menuItemDescription;
    }
  }

  static getLocalizedMenuSectionName(
    entity: OrderItemLocalizableEntity,
    userLanguage: Language,
  ): string | null | undefined {
    switch (userLanguage) {
      case Language.EN:
        return entity.menuSectionNameEn;
      case Language.VI:
        return entity.menuSectionNameVi;
      default:
        return entity.menuSectionName;
    }
  }

  // Order Item Option localization methods
  static getLocalizedOptionName(
    entity: OrderItemOptionLocalizableEntity,
    userLanguage: Language,
  ): string | null | undefined {
    switch (userLanguage) {
      case Language.EN:
        return entity.optionNameEn;
      case Language.VI:
        return entity.optionNameVi;
      default:
        return entity.optionName;
    }
  }

  static getLocalizedOptionGroupName(
    entity: OrderItemOptionLocalizableEntity,
    userLanguage: Language,
  ): string | null | undefined {
    switch (userLanguage) {
      case Language.EN:
        return entity.optionGroupNameEn;
      case Language.VI:
        return entity.optionGroupNameVi;
      default:
        return entity.optionGroupName;
    }
  }

  static localizeMenuItemEntity<T extends MenuLocalizableEntity>(
    entity: T | undefined,
    userLanguage: Language,
    restaurantLanguage: Language | undefined,
  ) {
    if (!entity) return;
    if (restaurantLanguage && restaurantLanguage === userLanguage) return;

    if ('publishedName' in entity) {
      // Override publishedName with localized version
      entity.publishedName = this.getLocalizedPublishedName(entity, userLanguage) || entity.publishedName;
    }

    if ('description' in entity) {
      // Override description with entity version
      entity.description = this.getLocalizedDescription(entity, userLanguage) || entity.description;
    }
  }

  static localizeOrderItemEntity<T extends OrderItemLocalizableEntity>(
    entity: T | undefined,
    userLanguage: Language,
    restaurantLanguage: Language | undefined,
  ) {
    if (!entity) return;
    if (restaurantLanguage && restaurantLanguage === userLanguage) return;

    if ('menuItemName' in entity) {
      entity.menuItemName = this.getLocalizedMenuItemName(entity, userLanguage) || entity.menuItemName;
    }

    if ('menuItemDescription' in entity) {
      entity.menuItemDescription =
        this.getLocalizedMenuItemDescription(entity, userLanguage) || entity.menuItemDescription;
    }

    if ('menuSectionName' in entity) {
      entity.menuSectionName = this.getLocalizedMenuSectionName(entity, userLanguage) || entity.menuSectionName;
    }
  }

  static localizeOrderItemOptionEntity<T extends OrderItemOptionLocalizableEntity>(
    entity: T | undefined,
    userLanguage: Language,
    restaurantLanguage: Language | undefined,
  ) {
    if (!entity) return;
    if (restaurantLanguage && restaurantLanguage === userLanguage) return;

    if ('optionName' in entity) {
      entity.optionName = this.getLocalizedOptionName(entity, userLanguage) || entity.optionName;
    }

    if ('optionGroupName' in entity) {
      entity.optionGroupName = this.getLocalizedOptionGroupName(entity, userLanguage) || entity.optionGroupName;
    }
  }
}
