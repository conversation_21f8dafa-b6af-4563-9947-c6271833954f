import { Public } from '@auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { AppVersionService } from './app-version.service';
import { GetAppVersionDto } from './dto/get-app-version.dto';
import { UpdateAppVersionDto } from './dto/update-app-version.dto';

@ApiTags('App Version')
@Controller('app-version')
export class AppVersionController {
  constructor(private readonly appVersionService: AppVersionService) {}

  @Public()
  @Get('current')
  @ApiOperation({ summary: 'Get current app version' })
  async getCurrentVersion(@Query() query: GetAppVersionDto) {
    return this.appVersionService.getCurrentVersion(query);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Get()
  @ApiOperation({ summary: 'Get all app versions (Admin only)' })
  async getAllVersions(@Query() query: GetAppVersionDto) {
    return this.appVersionService.getAllVersions(query.platform);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Get(':id')
  @ApiOperation({ summary: 'Get app version by ID (Admin only)' })
  async getVersionById(@Param('id') id: string) {
    return this.appVersionService.getVersionById(id);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Post()
  @ApiOperation({ summary: 'Create new app version (Admin only)' })
  async createVersion(@Body() dto: UpdateAppVersionDto) {
    return this.appVersionService.createVersion(dto);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Put(':id')
  @ApiOperation({ summary: 'Update app version (Admin only)' })
  async updateVersion(@Param('id') id: string, @Body() dto: Partial<UpdateAppVersionDto>) {
    return this.appVersionService.updateVersion(id, dto);
  }

  @Roles({ userType: UserType.AB_ADMIN, role: '*' })
  @Delete(':id')
  @ApiOperation({ summary: 'Delete app version (Admin only)' })
  async deleteVersion(@Param('id') id: string) {
    await this.appVersionService.deleteVersion(id);
    return { message: 'App version deleted successfully' };
  }
}
