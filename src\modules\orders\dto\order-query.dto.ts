import { Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

import { OrderStatus, OrderStatusGroup, PaymentStatus } from '../constants/order.enums';

export class OrderQueryDto extends PaginationDto {
  @ApiProperty({ required: false, description: 'Restaurant ID' })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;

  @ApiProperty({ required: false, description: 'Search keyword' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by status list',
    enum: OrderStatus,
    isArray: true,
    example: [],
  })
  @IsArray()
  @IsOptional()
  @IsEnum(OrderStatus, { each: true })
  @Transform(({ value }) => (typeof value === 'string' ? [value] : value))
  status?: OrderStatus[];

  @ApiProperty({ required: false, description: 'Payment status', enum: PaymentStatus })
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @ApiProperty({ required: false, description: 'Include archived orders' })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  archived?: boolean;

  @ApiProperty({
    required: false,
    description: 'Filter by status groups',
    enum: OrderStatusGroup,
    example: OrderStatusGroup.NEEDS_ACTION,
  })
  @IsOptional()
  @IsEnum(OrderStatusGroup)
  statusGroups?: OrderStatusGroup;

  @ApiProperty({
    required: false,
    description: 'Hide orders that user has already seen - true: hide seen orders, false: show all orders',
  })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isHideUserSeen?: boolean;
}
