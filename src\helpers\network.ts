import { Request } from 'express';

import { Logger } from '@nestjs/common';

/**
 * Get client IP address from request
 */

export const getClientIP = (request: Request, logger?: Logger, defaultIp = '127.0.0.1'): string => {
  const forwarded = request.headers['x-forwarded-for'];
  let ip = Array.isArray(forwarded) ? forwarded[0] : forwarded?.split(',')[0] || request.socket.remoteAddress;

  if (!ip || !isValidIPv4(ip)) {
    if (logger) {
      logger.warn(`Invalid IP address received: ${ip}, using default IP: ${defaultIp}`);
    }
    ip = defaultIp;
  }

  return ip;
};

/**
 * Validate IPv4 address
 */
const isValidIPv4 = (ip: string): boolean => {
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipv4Regex.test(ip)) return false;

  const parts = ip.split('.');
  return parts.every((part) => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255;
  });
};
