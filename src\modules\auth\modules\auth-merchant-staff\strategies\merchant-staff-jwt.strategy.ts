import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo, UserMerchantStaffJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

@Injectable()
export class MerchantStaffJwtStrategy extends PassportStrategy(Strategy, 'merchant-staff-jwt') {
  constructor(
    private configService: ConfigService,
    private merchantStaffService: MerchantStaffService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        (request: Request) => {
          const accessToken = request?.cookies?.access_token;
          if (!accessToken) {
            return null;
          }
          return accessToken;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.merchantStaffJwtAccessSecret') as string,
    });
  }

  async validate(payload: UserMerchantStaffJwtPayload) {
    const user = await this.merchantStaffService.findById(payload.sub);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    const userInfo: UserMerchantStaffJwtInfo = {
      id: payload.sub,
      username: user.username,
      restaurantId: user.restaurantId,
      userType: UserType.MERCHANT_STAFF,
      role: user.role,
    };

    return userInfo;
  }
}
