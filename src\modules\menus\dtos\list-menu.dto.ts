import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { PaginationSortDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export enum MenuSortBy {
  NAME = 'name',
  UPDATED_AT = 'updatedAt',
  SECTIONS_COUNT = 'sectionsCount',
  ITEMS_COUNT = 'itemsCount',
  ACTIVE_AT = 'activeAt',
}

export class ListMenuDto extends PaginationSortDto {
  @ApiProperty({ description: 'Filter by name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;

  @ApiProperty({
    description: 'Sort by field',
    required: false,
    enum: MenuSortBy,
    default: MenuSortBy.UPDATED_AT,
  })
  @IsOptional()
  @IsEnum(MenuSortBy)
  sortBy: MenuSortBy = MenuSortBy.UPDATED_AT;
}
