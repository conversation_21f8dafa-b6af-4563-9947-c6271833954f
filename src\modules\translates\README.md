# Translates Module

This module provides APIs for managing translations of menu entities (menu sections, menu items, and menu item option groups).

## APIs

### GET /translates

Retrieves all translation data for menu entities in a restaurant.

**Query Parameters:**
- `restaurantId` (required): UUID of the restaurant
- `type` (optional): Filter by entity type (`menu_section`, `menu_item`, `menu_item_option_group`)

**Response:**
Returns an array of translation items with the following structure:
```json
[
  {
    "id": "uuid",
    "type": "menu_section|menu_item|menu_item_option_group",
    "publishedName": "string",
    "publishedNameEn": "string|null",
    "publishedNameVi": "string|null",
    "description": "string|null", // Only available for menu_item
    "descriptionEn": "string|null", // Only available for menu_item
    "descriptionVi": "string|null" // Only available for menu_item
  }
]
```

**Example:**
```
GET /translates?restaurantId=123e4567-e89b-12d3-a456-************&type=menu_item
```

### PUT /translates

Updates translation data for multiple menu entities.

**Request Body:**
```json
{
  "items": [
    {
      "id": "uuid",
      "type": "menu_section|menu_item|menu_item_option_group",
      "publishedName": "string (optional)",
      "publishedNameEn": "string (optional)",
      "publishedNameVi": "string (optional)",
      "description": "string (optional)", // Only for menu_item
      "descriptionEn": "string (optional)", // Only for menu_item
      "descriptionVi": "string (optional)" // Only for menu_item
    }
  ]
}
```

**Response:**
```json
{
  "updated": 5
}
```

## Entity Support

### Menu Section
- ✅ `publishedName`, `publishedNameEn`, `publishedNameVi`
- ❌ `description` fields (not available in database)

### Menu Item
- ✅ `publishedName`, `publishedNameEn`, `publishedNameVi`
- ✅ `description`, `descriptionEn`, `descriptionVi`

### Menu Item Option Group
- ✅ `publishedName`, `publishedNameEn`, `publishedNameVi`
- ❌ `description` fields (not available in database)

## Authentication

All endpoints require merchant user authentication with any role.

## Error Handling

- `404 Not Found`: Entity with specified ID and type not found
- `403 Forbidden`: User doesn't have access to the restaurant
- `400 Bad Request`: Invalid request data
