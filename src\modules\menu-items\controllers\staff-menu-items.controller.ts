import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Controller, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { MenuItem } from '../entities/menu-item.entity';
import { MenuItemsService } from '../menu-items.service';

@ApiTags('(Ops) Menu Items')
@Controller('staff/menu-items')
@Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
export class StaffMenuItemsController {
  constructor(private readonly menuItemsService: MenuItemsService) {}

  @Get(':menuSectionId/:menuItemId')
  userFindOne(
    @User() user: UserMerchantStaffJwtInfo,
    @Param('menuSectionId', ParseUUIDPipe) menuSectionId: string,
    @Param('menuItemId', ParseUUIDPipe) menuItemId: string,
  ): Promise<MenuItem> {
    return this.menuItemsService.findOneByUser(user.restaurantId, menuSectionId, menuItemId);
  }
}
