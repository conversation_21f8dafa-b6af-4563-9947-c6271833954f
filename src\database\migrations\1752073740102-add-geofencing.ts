import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGeofencing1752073740102 implements MigrationInterface {
  name = 'AddGeofencing1752073740102';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."geofencing_type_enum" AS ENUM('polygon', 'circle', 'rectangle')`);
    await queryRunner.query(
      `CREATE TABLE "geofencing" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "restaurant_id" uuid NOT NULL, "name" character varying NOT NULL, "description" character varying, "type" "public"."geofencing_type_enum" NOT NULL, "shipping_fee" numeric(10,2) NOT NULL, "geometry" geometry(Geometry,4326) NOT NULL, "geometry_data" jsonb NOT NULL, CONSTRAINT "PK_6136c7f09dd0db8a98cff0eb389" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_651a142582b49f73fcaed66772" ON "geofencing" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_8b44b2384fd44f07294e83b88b" ON "geofencing" ("shipping_fee") `);
    await queryRunner.query(`CREATE INDEX "IDX_geofencing_geometry_gist" ON "geofencing" USING GIST ("geometry")`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "address" character varying`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "ward" character varying`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "district" character varying`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "province" character varying`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "latitude" numeric(10,8)`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "longitude" numeric(11,8)`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "location" geometry(Point,4326)`);
    await queryRunner.query(`CREATE INDEX "IDX_6de2961809c2a7f90f20d45d7e" ON "restaurants" ("latitude") `);
    await queryRunner.query(`CREATE INDEX "IDX_f2d957e44b59ca1e7f45613c08" ON "restaurants" ("longitude") `);
    await queryRunner.query(
      `ALTER TABLE "geofencing" ADD CONSTRAINT "FK_651a142582b49f73fcaed667727" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "geofencing" DROP CONSTRAINT "FK_651a142582b49f73fcaed667727"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2d957e44b59ca1e7f45613c08"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6de2961809c2a7f90f20d45d7e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_geofencing_geometry_gist"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "longitude"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "latitude"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "province"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "district"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "ward"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "address"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b44b2384fd44f07294e83b88b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_651a142582b49f73fcaed66772"`);
    await queryRunner.query(`DROP TABLE "geofencing"`);
    await queryRunner.query(`DROP TYPE "public"."geofencing_type_enum"`);
  }
}
