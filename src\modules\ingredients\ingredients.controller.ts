import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateIngredientDto } from './dtos/create-ingredient.dto';
import { ListIngredientDto } from './dtos/list-ingredient.dto';
import { UpdateIngredientDto } from './dtos/update-ingredient.dto';
import { Ingredient } from './entities/ingredient.entity';
import { IngredientsService } from './ingredients.service';

@ApiTags('Ingredients')
@Controller('ingredients')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class IngredientsController {
  constructor(private readonly ingredientsService: IngredientsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if ingredient name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.ingredientsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(
    @Body() createIngredientDto: CreateIngredientDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Ingredient> {
    return this.ingredientsService.create(createIngredientDto, ownerId);
  }

  @Get()
  findAll(
    @Query() listIngredientDto: ListIngredientDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<Ingredient>> {
    return this.ingredientsService.findAll(listIngredientDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Ingredient> {
    return this.ingredientsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateIngredientDto: UpdateIngredientDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Ingredient> {
    return this.ingredientsService.update(id, updateIngredientDto, ownerId);
  }
}
