import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveItemOption1752759834834 implements MigrationInterface {
  name = 'RemoveItemOption1752759834834';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" DROP CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5"`,
    );
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" DROP CONSTRAINT "FK_9be18ee866be61bf8f0af144d8c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" DROP CONSTRAINT "FK_052809b5ea16f8b626e6ac7d40b"`,
    );
    await queryRunner.query(`DROP TABLE "mapping_menu_items_option_ingredients"`);
    await queryRunner.query(`DROP TABLE "menu_item_options"`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "is_alcohol" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" ADD "tax_amount" numeric(10,2) NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" ADD "is_alcohol" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "tax_amount" numeric(10,2) NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "is_alcohol" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "tax_amount" numeric(10,2) NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "order_item_options" ADD "is_alcohol" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "tax_amount" numeric(10,2) NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "order_items" ADD "is_alcohol" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "type"`);
    await queryRunner.query(`CREATE TYPE "public"."menu_items_type_enum" AS ENUM('item', 'option')`);
    await queryRunner.query(
      `ALTER TABLE "menu_items" ADD "type" "public"."menu_items_type_enum" NOT NULL DEFAULT 'item'`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" ADD CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" DROP CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5"`,
    );
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."menu_items_type_enum"`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "type" character varying NOT NULL DEFAULT 'item'`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "is_alcohol"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "tax_amount"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "is_alcohol"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP COLUMN "tax_amount"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "is_alcohol"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "tax_amount"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "is_alcohol"`);
    await queryRunner.query(`ALTER TABLE "order_item_options_original" DROP COLUMN "tax_amount"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "is_alcohol"`);
    await queryRunner.query(
      `CREATE TABLE "menu_item_options" (
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "code" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "internal_name" character varying NOT NULL,
        "published_name" character varying NOT NULL,
        "description" character varying,
        "base_price" numeric(10,2) NOT NULL,
        "type" character varying NOT NULL DEFAULT 'option',
        "restaurant_id" uuid NOT NULL,
        "schedule_active_at" TIMESTAMP WITH TIME ZONE,
        "active_at" TIMESTAMP WITH TIME ZONE,
        CONSTRAINT "PK_5f9cc4a2480757f075354302fdb" PRIMARY KEY ("id")
      )`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_menu_item_options_code" ON "menu_item_options" ("code")`);
    await queryRunner.query(
      `CREATE INDEX "IDX_menu_item_options_restaurant_id" ON "menu_item_options" ("restaurant_id")`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_menu_item_options_active_at" ON "menu_item_options" ("active_at")`);
    await queryRunner.query(
      `ALTER TABLE "menu_item_options" ADD CONSTRAINT "FK_5196746c95ab1710cd5c73c9089" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" ADD CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `CREATE TABLE "mapping_menu_items_option_ingredients" (
        "menu_item_option_id" uuid NOT NULL,
        "ingredient_id" uuid NOT NULL,
        CONSTRAINT "PK_mapping_menu_items_option_ingredients" PRIMARY KEY ("menu_item_option_id", "ingredient_id")
      )`,
    );

    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" ADD CONSTRAINT "FK_052809b5ea16f8b626e6ac7d40b" FOREIGN KEY ("ingredient_id") REFERENCES "ingredients"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" ADD CONSTRAINT "FK_9be18ee866be61bf8f0af144d8c" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
