import { Request, Response } from 'express';

import { User } from '@/common/decorators/user.decorator';
import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { Public } from '@auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { RefreshTokenDto } from '@auth/dtos/refresh-token.dto';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AuthMerchantStaffService } from './auth-merchant-staff.service';
import { LoginMerchantStaffDto } from './dtos/login.dto';

@ApiTags('(Auth) Merchant staff')
@Controller('auth/merchant-staff')
export class AuthMerchantStaffController {
  constructor(
    private readonly authMerchantStaffService: AuthMerchantStaffService,
    private readonly merchantStaffService: MerchantStaffService,
  ) {}

  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginMerchantStaffDto, @Res({ passthrough: true }) response: Response) {
    return this.authMerchantStaffService.login(loginDto, response);
  }

  @Public()
  @Post('refresh')
  async refreshToken(
    @Req() request: Request,
    @Body() refreshTokenDto: RefreshTokenDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    return this.authMerchantStaffService.refreshToken(request, refreshTokenDto, response);
  }

  @Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
  @Post('logout')
  logout(@Res({ passthrough: true }) response: Response) {
    return this.authMerchantStaffService.logout(response);
  }

  @Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
  @Get('me')
  getMe(@User() user) {
    return this.merchantStaffService.getMe(user.id);
  }
}
