import { <PERSON><PERSON><PERSON>y, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>al, <PERSON>String, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CreateIngredientDto {
  @ApiProperty({ description: 'Internal name of the ingredient' })
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the ingredient' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({
    description: 'Array of menu item IDs to add this ingredient to',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuItemIds?: string[];
}
