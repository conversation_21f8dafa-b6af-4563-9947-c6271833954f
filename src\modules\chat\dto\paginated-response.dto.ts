import { ApiProperty } from '@nestjs/swagger';

export class PaginatedConversationsResponseDto {
  @ApiProperty({ description: 'Array of conversations data', type: [Object] })
  data: any[];

  @ApiProperty({ description: 'Next cursor for pagination (timestamp)', required: false })
  cursor?: number; // Next cursor for pagination (timestamp)

  @ApiProperty({ description: 'Whether there are more results available' })
  hasMore: boolean;
}

export class PaginatedMessagesResponseDto {
  @ApiProperty({ description: 'Array of messages data', type: [Object] })
  data: any[];

  @ApiProperty({ description: 'Next cursor for pagination', required: false })
  cursor?: number; // Next cursor for pagination

  @ApiProperty({ description: 'Whether there are more results available' })
  hasMore: boolean;
}
