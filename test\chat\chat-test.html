<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnhBeo Chat Test</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 100vh;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .auth-section {
            grid-column: 1 / -1;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background: #fafafa;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.sent {
            background: #e8f5e8;
            text-align: right;
        }
        .message.received {
            background: #f0f0f0;
        }
        .message.system {
            background: #fff3cd;
            color: #856404;
            font-style: italic;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
        }
        input, button, select, textarea {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .section {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
    </style>
</head>
<body>
    <h1>🚀 AnhBeo Chat System Test</h1>
    
    <div class="auth-section panel">
        <h2>🔐 Authentication</h2>
        <div class="grid-2">
            <div>
                <h3>User Login</h3>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="userEmail" placeholder="<EMAIL>" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="userPassword" placeholder="password" value="123456">
                </div>
                <button onclick="loginUser()">Login as User</button>
                <div id="userToken" style="margin-top: 10px; font-size: 12px; word-break: break-all;"></div>
            </div>
            <div>
                <h3>Restaurant Login</h3>
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" id="restaurantEmail" placeholder="RES5299673332" value="RES5299673332">
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="restaurantPassword" placeholder="password" value="123456">
                </div>
                <button onclick="loginRestaurant()">Login as Restaurant</button>
                <div id="restaurantToken" style="margin-top: 10px; font-size: 12px; word-break: break-all;"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- User Panel -->
        <div class="panel">
            <h2>👤 User Panel</h2>
            
            <div class="section">
                <h3>Socket Connection</h3>
                <div id="userStatus" class="status disconnected">Disconnected</div>
                <button id="userConnectBtn" onclick="connectUserSocket()">Connect</button>
                <button id="userDisconnectBtn" onclick="disconnectUserSocket()" disabled>Disconnect</button>
            </div>

            <div class="section">
                <h3>Chat Actions</h3>
                <div class="form-group">
                    <label>Order ID:</label>
                    <input type="text" id="userOrderId" placeholder="order-uuid-here" value="23474871-ce6d-4b1b-8b1d-c3e1322b38ae">
                </div>
                <button onclick="joinOrderRoom('user')" disabled id="userJoinBtn">Join Order Room</button>
                <button onclick="leaveOrderRoom('user')" disabled id="userLeaveBtn">Leave Order Room</button>
            </div>

            <div class="section">
                <h3>Send Message</h3>
                <div class="form-group">
                    <textarea id="userMessage" placeholder="Type your message..." rows="2" style="width: 100%; box-sizing: border-box;"></textarea>
                </div>
                <button onclick="sendMessage('user')" disabled id="userSendBtn">Send Message</button>
            </div>

            <div class="section">
                <h3>Messages</h3>
                <div id="userMessages" class="messages"></div>
            </div>

            <div class="section">
                <h3>REST API Tests</h3>
                <button onclick="getConversations('user')">Get Conversations</button>
                <button onclick="getMessages('user')">Get Messages</button>
            </div>
        </div>

        <!-- Restaurant Panel -->
        <div class="panel">
            <h2>🏪 Restaurant Panel</h2>
            
            <div class="section">
                <h3>Socket Connection</h3>
                <div id="restaurantStatus" class="status disconnected">Disconnected</div>
                <button id="restaurantConnectBtn" onclick="connectRestaurantSocket()">Connect</button>
                <button id="restaurantDisconnectBtn" onclick="disconnectRestaurantSocket()" disabled>Disconnect</button>
            </div>

            <div class="section">
                <h3>Chat Actions</h3>
                <div class="form-group">
                    <label>Order ID:</label>
                    <input type="text" id="restaurantOrderId" placeholder="order-uuid-here" value="23474871-ce6d-4b1b-8b1d-c3e1322b38ae">
                </div>
                <button onclick="joinOrderRoom('restaurant')" disabled id="restaurantJoinBtn">Join Order Room</button>
                <button onclick="leaveOrderRoom('restaurant')" disabled id="restaurantLeaveBtn">Leave Order Room</button>
            </div>

            <div class="section">
                <h3>Send Message</h3>
                <div class="form-group">
                    <textarea id="restaurantMessage" placeholder="Type your message..." rows="2" style="width: 100%; box-sizing: border-box;"></textarea>
                </div>
                <button onclick="sendMessage('restaurant')" disabled id="restaurantSendBtn">Send Message</button>
            </div>

            <div class="section">
                <h3>Messages</h3>
                <div id="restaurantMessages" class="messages"></div>
            </div>

            <div class="section">
                <h3>REST API Tests</h3>
                <button onclick="getConversations('restaurant')">Get Conversations</button>
                <button onclick="getMessages('restaurant')">Get Messages</button>
            </div>
        </div>
    </div>

    <!-- Debug Panel -->
    <div class="panel" style="margin-top: 100px;">
        <h2>🐛 Debug Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        // Configuration
        const config = {
            backendUrl: 'http://localhost:3002',
            socketUrl: 'http://localhost:3000'
        };

        // Global state
        let userSocket = null;
        let restaurantSocket = null;
        let userToken = null;
        let restaurantToken = null;
        let userInfo = null;
        let restaurantInfo = null;

        // Debug logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        // Add message to UI
        function addMessage(panel, message, type = 'received') {
            const messagesDiv = document.getElementById(`${panel}Messages`);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <strong>${type.toUpperCase()}:</strong> ${JSON.stringify(message, null, 2)}
                <br><small>${new Date().toLocaleTimeString()}</small>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Authentication functions
        async function loginUser() {
            try {
                const email = document.getElementById('userEmail').value;
                const password = document.getElementById('userPassword').value;

                log(`Attempting user login: ${email}`);

                const response = await fetch(`${config.backendUrl}/auth/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    userToken = data.accessToken;
                    userInfo = data.user;
                    document.getElementById('userToken').textContent = `Token: ${userToken}`;
                    log('User login successful', 'success');
                    updateUserButtons();
                } else {
                    log(`User login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`User login error: ${error.message}`, 'error');
            }
        }

        async function loginRestaurant() {
            try {
                const username = document.getElementById('restaurantEmail').value;
                const password = document.getElementById('restaurantPassword').value;

                log(`Attempting restaurant login: ${username}`);

                const response = await fetch(`${config.backendUrl}/auth/merchant-staff/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    restaurantToken = data.accessToken;
                    restaurantInfo = data.user;
                    document.getElementById('restaurantToken').textContent = `Token: ${restaurantToken}`;
                    log('Restaurant login successful', 'success');
                    updateRestaurantButtons();
                } else {
                    log(`Restaurant login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`Restaurant login error: ${error.message}`, 'error');
            }
        }

        // Socket connection functions
        function connectUserSocket() {
            if (!userToken) {
                log('Please login as user first', 'error');
                return;
            }

            userSocket = io(config.socketUrl, {
                extraHeaders: { Authorization: `Bearer ${userToken}` },
            });

            userSocket.on('connect', () => {
                log('User socket connected', 'success');
                document.getElementById('userStatus').textContent = 'Connected';
                document.getElementById('userStatus').className = 'status connected';
                updateUserButtons();
            });

            userSocket.on('disconnect', () => {
                log('User socket disconnected', 'error');
                document.getElementById('userStatus').textContent = 'Disconnected';
                document.getElementById('userStatus').className = 'status disconnected';
                updateUserButtons();
            });

            userSocket.on('connected', (data) => {
                log(`User connected event: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'system');
            });

            userSocket.on('joined_personal_room', (data) => {
                log(`User joined personal room: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'system');
            });

            userSocket.on('joined_order_room', (data) => {
                log(`User joined order room: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'system');
            });

            userSocket.on('left_order_room', (data) => {
                log(`User left order room: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'system');
            });

            userSocket.on('new_message', (data) => {
                log(`User received new message: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'received');
            });

            userSocket.on('message_notification', (data) => {
                log(`User received message notification: ${JSON.stringify(data)}`, 'success');
                addMessage('user', { ...data, type: 'notification' }, 'system');
            });

            userSocket.on('message_status_update', (data) => {
                log(`User received status update: ${JSON.stringify(data)}`, 'success');
                addMessage('user', data, 'system');
            });

            userSocket.on('error', (data) => {
                log(`User socket error: ${JSON.stringify(data)}`, 'error');
                addMessage('user', data, 'error');
            });
        }

        function connectRestaurantSocket() {
            if (!restaurantToken) {
                log('Please login as restaurant first', 'error');
                return;
            }

            restaurantSocket = io(config.socketUrl, {
                extraHeaders: { Authorization: `Bearer ${restaurantToken}` },
            });

            restaurantSocket.on('connect', () => {
                log('Restaurant socket connected', 'success');
                document.getElementById('restaurantStatus').textContent = 'Connected';
                document.getElementById('restaurantStatus').className = 'status connected';
                updateRestaurantButtons();
            });

            restaurantSocket.on('disconnect', () => {
                log('Restaurant socket disconnected', 'error');
                document.getElementById('restaurantStatus').textContent = 'Disconnected';
                document.getElementById('restaurantStatus').className = 'status disconnected';
                updateRestaurantButtons();
            });

            restaurantSocket.on('connected', (data) => {
                log(`Restaurant connected event: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'system');
            });

            restaurantSocket.on('joined_personal_room', (data) => {
                log(`Restaurant joined personal room: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'system');
            });

            restaurantSocket.on('joined_order_room', (data) => {
                log(`Restaurant joined order room: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'system');
            });

            restaurantSocket.on('left_order_room', (data) => {
                log(`Restaurant left order room: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'system');
            });

            restaurantSocket.on('new_message', (data) => {
                log(`Restaurant received new message: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'received');
            });

            restaurantSocket.on('message_notification', (data) => {
                log(`Restaurant received message notification: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', { ...data, type: 'notification' }, 'system');
            });

            restaurantSocket.on('message_status_update', (data) => {
                log(`Restaurant received status update: ${JSON.stringify(data)}`, 'success');
                addMessage('restaurant', data, 'system');
            });

            restaurantSocket.on('error', (data) => {
                log(`Restaurant socket error: ${JSON.stringify(data)}`, 'error');
                addMessage('restaurant', data, 'error');
            });
        }

        function disconnectUserSocket() {
            if (userSocket) {
                userSocket.disconnect();
                userSocket = null;
            }
        }

        function disconnectRestaurantSocket() {
            if (restaurantSocket) {
                restaurantSocket.disconnect();
                restaurantSocket = null;
            }
        }

        // Chat functions
        function joinOrderRoom(type) {
            const socket = type === 'user' ? userSocket : restaurantSocket;
            const orderId = document.getElementById(`${type}OrderId`).value;

            if (!socket) {
                log(`${type} socket not connected`, 'error');
                return;
            }

            if (!orderId) {
                log('Please enter order ID', 'error');
                return;
            }

            log(`${type} joining order room: ${orderId}`);
            socket.emit('join_order_room', { orderId });
        }

        function leaveOrderRoom(type) {
            const socket = type === 'user' ? userSocket : restaurantSocket;
            const orderId = document.getElementById(`${type}OrderId`).value;

            if (!socket) {
                log(`${type} socket not connected`, 'error');
                return;
            }

            if (!orderId) {
                log('Please enter order ID', 'error');
                return;
            }

            log(`${type} leaving order room: ${orderId}`);
            socket.emit('leave_order_room', { orderId });
        }

        async function sendMessage(type) {
            const token = type === 'user' ? userToken : restaurantToken;
            const content = document.getElementById(`${type}Message`).value;
            const orderId = document.getElementById(`${type}OrderId`).value;

            if (!token) {
                log(`Please login as ${type} first`, 'error');
                return;
            }

            if (!content || !orderId) {
                log('Please enter message content and order ID', 'error');
                return;
            }

            try {
                log(`${type} sending message via REST API`);

                const response = await fetch(`${config.backendUrl}/chat/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        orderId,
                        content
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    log(`${type} message sent successfully`, 'success');
                    addMessage(type, { content, status: 'sent' }, 'sent');
                    document.getElementById(`${type}Message`).value = '';
                } else {
                    log(`${type} message send failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`${type} message send error: ${error.message}`, 'error');
            }
        }

        // API functions
        async function getConversations(type) {
            const token = type === 'user' ? userToken : restaurantToken;
            const info = type === 'user' ? userInfo : restaurantInfo;

            if (!token || !info) {
                log(`Please login as ${type} first`, 'error');
                return;
            }

            try {
                const params = new URLSearchParams();
                params.append('userType', type);
                
                if (type === 'user') {
                    params.append('userId', info.id);
                } else {
                    params.append('restaurantId', info.restaurantId);
                }

                const response = await fetch(`${config.backendUrl}/chat/conversations?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include'
                });

                const data = await response.json();

                if (response.ok) {
                    log(`${type} conversations retrieved`, 'success');
                    addMessage(type, { conversations: data }, 'system');
                } else {
                    log(`${type} get conversations failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`${type} get conversations error: ${error.message}`, 'error');
            }
        }

        async function getMessages(type) {
            const token = type === 'user' ? userToken : restaurantToken;
            const conversationId = prompt(`Enter conversation ID for ${type}:`);

            if (!token) {
                log(`Please login as ${type} first`, 'error');
                return;
            }

            if (!conversationId) {
                log('Conversation ID required', 'error');
                return;
            }

            try {
                const response = await fetch(`${config.backendUrl}/chat/conversations/${conversationId}/messages`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include'
                });

                const data = await response.json();

                if (response.ok) {
                    log(`${type} messages retrieved`, 'success');
                    addMessage(type, { messages: data }, 'system');
                } else {
                    log(`${type} get messages failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`${type} get messages error: ${error.message}`, 'error');
            }
        }

        // UI state management
        function updateUserButtons() {
            const connected = userSocket && userSocket.connected;
            const loggedIn = !!userToken;

            document.getElementById('userConnectBtn').disabled = !loggedIn || connected;
            document.getElementById('userDisconnectBtn').disabled = !connected;
            document.getElementById('userJoinBtn').disabled = !connected;
            document.getElementById('userLeaveBtn').disabled = !connected;
            document.getElementById('userSendBtn').disabled = !loggedIn;
        }

        function updateRestaurantButtons() {
            const connected = restaurantSocket && restaurantSocket.connected;
            const loggedIn = !!restaurantToken;

            document.getElementById('restaurantConnectBtn').disabled = !loggedIn || connected;
            document.getElementById('restaurantDisconnectBtn').disabled = !connected;
            document.getElementById('restaurantJoinBtn').disabled = !connected;
            document.getElementById('restaurantLeaveBtn').disabled = !connected;
            document.getElementById('restaurantSendBtn').disabled = !loggedIn;
        }

        // Initialize
        log('Chat test interface loaded');
        updateUserButtons();
        updateRestaurantButtons();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                if (e.target.id === 'userMessage') {
                    sendMessage('user');
                } else if (e.target.id === 'restaurantMessage') {
                    sendMessage('restaurant');
                }
            }
        });
    </script>
</body>
</html> 