import { Exclude } from 'class-transformer';
import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantAccount } from '@/modules/merchant-accounts/entities/merchant-account.entity';

import { MerchantUserRole } from '../enums/merchant-users-role.enum';

@Entity('merchant_users')
export class MerchantUser extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  email: string;

  @Column({ name: 'first_name', type: 'varchar' })
  firstName: string;

  @Column({ name: 'last_name', type: 'varchar' })
  lastName: string;

  @Column({ type: 'varchar' })
  @Exclude()
  password: string;

  @Column({
    type: 'enum',
    enum: MerchantUserRole,
    default: MerchantUserRole.MEMBER,
  })
  role: MerchantUserRole;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @Column({ default: false })
  banned: boolean;

  // One user can own multiple accounts
  @OneToMany(() => MerchantAccount, (merchantAccount) => merchantAccount.ownerMerchantUser)
  merchantAccounts: WrapperType<MerchantAccount>[];
}
