import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLocationAndScheduleOfRestaurant1752221399546 implements MigrationInterface {
  name = 'AddLocationAndScheduleOfRestaurant1752221399546';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_7d88c646d0b073a0f291e9ab34"`);
    await queryRunner.query(
      `CREATE TABLE "restaurant_available_schedules" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "restaurant_id" uuid NOT NULL, "day" integer NOT NULL, "start" TIME NOT NULL DEFAULT '00:00:00', "end" TIME NOT NULL DEFAULT '23:59:59', "is_all_day" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_da601f32188195141530a6064b3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3bd1ca14eecd347cd0ae6ae169" ON "restaurant_available_schedules" ("restaurant_id") `,
    );
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "schedule_active_at" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "phone" character varying`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "latitude" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "longitude" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "location" SET NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f183dc3ad2cc78d2bff53183d4" ON "restaurants" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_75eb8d9c79834a4a34b7fa5eb4" ON "restaurants" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_a98ed620228a82ee3f59d57de4" ON "restaurants" ("schedule_active_at") `);
    await queryRunner.query(
      `ALTER TABLE "restaurant_available_schedules" ADD CONSTRAINT "FK_3bd1ca14eecd347cd0ae6ae1690" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "restaurant_available_schedules" DROP CONSTRAINT "FK_3bd1ca14eecd347cd0ae6ae1690"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_a98ed620228a82ee3f59d57de4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_75eb8d9c79834a4a34b7fa5eb4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f183dc3ad2cc78d2bff53183d4"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "location" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "longitude" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "latitude" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "phone"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "schedule_active_at"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bd1ca14eecd347cd0ae6ae169"`);
    await queryRunner.query(`DROP TABLE "restaurant_available_schedules"`);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7d88c646d0b073a0f291e9ab34" ON "restaurants" ("code") `);
  }
}
