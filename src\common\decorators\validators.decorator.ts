import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ name: 'IsGreaterThanOrEqualTo', async: false })
export class IsGreaterThanOrEqualToValidator implements ValidatorConstraintInterface {
  validate(value: number, args: any) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = args.object[relatedPropertyName];
    return value >= relatedValue;
  }

  defaultMessage(args: any) {
    const [relatedPropertyName] = args.constraints;
    return `${args.property} must be greater than or equal to ${relatedPropertyName}`;
  }
}
