import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

@Entity('onepay_void_requests')
export class OnepayVoidRequest extends BaseEntity {
  @Column({ name: 'org_merch_txn_ref', type: 'varchar' })
  orgMerchTxnRef: string;

  @Index({ unique: true })
  @Column({ name: 'merch_txn_ref', type: 'varchar' })
  merchTxnRef: string;

  @Column({ name: 'amount', type: 'integer' })
  amount: number;

  @Column({ name: 'operator', type: 'varchar' })
  operator: string;

  @Column({ name: 'type', type: 'varchar' })
  type: 'add-card' | 'payment';
}
