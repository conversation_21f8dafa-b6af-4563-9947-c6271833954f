import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { SetFavouriteRestaurantDto } from './dto/set-favourite-restaurant.dto';
import { UserFavouriteRestaurantsService } from './user-favourite-restaurants.service';

@ApiTags('(User) Favourite Restaurants')
@Controller('user/favourite-restaurants')
@Roles({ userType: UserType.USER, role: '*' })
export class UserFavouriteRestaurantsController {
  constructor(private readonly userFavouriteRestaurantsService: UserFavouriteRestaurantsService) {}

  @Post()
  setFavourite(@User('id') userId: string, @Body() setFavouriteRestaurantDto: SetFavouriteRestaurantDto) {
    return this.userFavouriteRestaurantsService.setFavourite(
      userId,
      setFavouriteRestaurantDto.restaurantId,
      setFavouriteRestaurantDto.favourite,
    );
  }
}
