export interface FCMPayload {
  title: string;
  body: string;
  data?: {
    type: FcmUserType | FcmRestaurantType | FcmChatType;
    data: string;
  };
  imageUrl?: string;
}

export enum FcmUserType {
  ORDER_IN_KITCHEN = 'order_in_kitchen',
  IN_KITCHEN_ETA_UPDATED = 'in_kitchen_eta_updated',
  ORDER_IN_DELIVERY = 'order_in_delivery',
  DELIVERY_ETA_UPDATED = 'delivery_eta_updated',
  ORDER_MODIFIED = 'order_modified',
  ORDER_DELIVERED = 'order_delivered',
  ORDER_CANCELLED = 'order_cancelled',
  ORDER_UNFULFILLED = 'order_unfulfilled',
}

export enum FcmRestaurantType {
  NEW_ORDER = 'new_order',
  DONT_MISS_ORDER = 'dont_miss_order',
  ORDER_READY_FOR_DELIVERY = 'order_ready_for_delivery',
  ISSUE_WITH_DELIVERY = 'issue_with_delivery',
  ORDER_MODIFICATION_ACCEPTED = 'order_modification_accepted',
  ORDER_CANCELLED = 'order_cancelled',
}

export enum FcmChatType {
  NEW_MESSAGE_FROM_USER = 'new_message_from_user',
  NEW_MESSAGE_FROM_RESTAURANT = 'new_message_from_restaurant',
}
