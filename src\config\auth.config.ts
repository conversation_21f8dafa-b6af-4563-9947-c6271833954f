import { registerAs } from '@nestjs/config';

export default registerAs('auth', () => ({
  // JWT secrets for different user types
  merchantUserJwtAccessSecret: process.env.MERCHANT_USER_JWT_ACCESS_SECRET || 'merchant-user-access-secret-key',
  merchantUserJwtRefreshSecret: process.env.MERCHANT_USER_JWT_REFRESH_SECRET || 'merchant-user-refresh-secret-key',

  adminJwtAccessSecret: process.env.ADMIN_JWT_ACCESS_SECRET || 'merchant-user-secret-key',
  adminJwtRefreshSecret: process.env.ADMIN_JWT_REFRESH_SECRET || 'merchant-user-secret-key',

  merchantStaffJwtAccessSecret: process.env.MERCHANT_STAFF_JWT_ACCESS_SECRET || 'merchant-staff-access-secret-key',
  merchantStaffJwtRefreshSecret: process.env.MERCHANT_STAFF_JWT_REFRESH_SECRET || 'merchant-staff-refresh-secret-key',

  userJwtAccessSecret: process.env.USER_JWT_ACCESS_SECRET || 'user-access-secret-key',
  userJwtRefreshSecret: process.env.USER_JWT_REFRESH_SECRET || 'user-refresh-secret-key',

  // JWT expiration times
  accessTokenExpiresIn: process.env.ACCESS_TOKEN_EXPIRES_IN || '15m',
  refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',

  // Cookie settings
  cookieSecure: process.env.COOKIE_SECURE === 'true',
  cookieHttpOnly: true,
  cookieSameSite: 'strict' as const,
}));
