import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

import { MenuItemOptionGroupRule } from '../entities/menu-item-option-group.entity';

export class ListMenuItemOptionGroupDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by internal name', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Filter by published name', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({
    description: 'Filter by rule',
    enum: MenuItemOptionGroupRule,
    required: false,
  })
  @IsOptional()
  @IsEnum(MenuItemOptionGroupRule)
  rule?: MenuItemOptionGroupRule;

  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;
}
