import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnablePgTrgm1752254221000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable pg_trgm extension for fuzzy text matching
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS pg_trgm;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove pg_trgm extension
    await queryRunner.query(`DROP EXTENSION IF EXISTS pg_trgm;`);
  }
}
