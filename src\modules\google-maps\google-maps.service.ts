import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { AutoCompleteResponseDto } from './dto/auto-complete-response.dto';

interface AddressComponent {
  streetNumber?: string;
  streetName?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

interface GeocodeResult {
  latitude: number;
  longitude: number;
  fullAddress: string;
  addressComponents: AddressComponent;
  placeId: string;
}

@Injectable()
export class GoogleMapsService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api';

  constructor(private configService: ConfigService) {
    this.apiKey = this.configService.get<string>('GOOGLE_MAPS_API_KEY') || '';
    if (!this.apiKey) {
      throw new Error('GOOGLE_MAPS_API_KEY is required');
    }
  }

  async geocodeAddress(address: string): Promise<GeocodeResult> {
    try {
      const response = await axios.get(`${this.baseUrl}/geocode/json`, {
        params: {
          address,
          key: this.apiKey,
        },
      });

      if (response.data.status !== 'OK') {
        throw new HttpException(`Geocoding failed: ${response.data.status}`, HttpStatus.BAD_REQUEST);
      }

      const result = response.data.results[0];
      return {
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng,
        fullAddress: result.formatted_address,
        addressComponents: this.parseAddressComponents(result.address_components),
        placeId: result.place_id,
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException(`Geocoding error: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getAutoComplete(input: string): Promise<AutoCompleteResponseDto[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/place/autocomplete/json`, {
        params: {
          input,
          key: this.apiKey,
          language: 'en',
          components: 'country:vn',
        },
      });

      if (response.data.status !== 'OK') {
        throw new HttpException(`Auto-complete failed: ${response.data.status}`, HttpStatus.BAD_REQUEST);
      }

      return response.data.predictions.map((p: any) => ({
        placeId: p.place_id,
        description: p.description,
        mainText: p.structured_formatting?.main_text,
        secondaryText: p.structured_formatting?.secondary_text,
      }));
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException(`Autocomplete error: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async reverseGeocode(lat: number, lng: number): Promise<GeocodeResult> {
    try {
      const response = await axios.get(`${this.baseUrl}/geocode/json`, {
        params: {
          latlng: `${lat},${lng}`,
          key: this.apiKey,
        },
      });

      if (response.data.status !== 'OK') {
        throw new HttpException(`Reverse geocoding failed: ${response.data.status}`, HttpStatus.BAD_REQUEST);
      }

      const result = response.data.results[0];
      return {
        latitude: lat,
        longitude: lng,
        fullAddress: result.formatted_address,
        addressComponents: this.parseAddressComponents(result.address_components),
        placeId: result.place_id,
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException(`Reverse geocoding error: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPlaceDetails(placeId: string): Promise<GeocodeResult> {
    try {
      const response = await axios.get(`${this.baseUrl}/place/details/json`, {
        params: {
          place_id: placeId,
          fields: 'geometry,formatted_address,address_components,name',
          key: this.apiKey,
        },
      });

      if (response.data.status !== 'OK') {
        throw new HttpException(`Place details failed: ${response.data.status}`, HttpStatus.BAD_REQUEST);
      }

      const result = response.data.result;
      return {
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng,
        fullAddress: result.formatted_address,
        addressComponents: this.parseAddressComponents(result.address_components),
        placeId,
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException(`Place details error: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private parseAddressComponents(components: any[]): AddressComponent {
    const addressComponents: AddressComponent = {};

    components.forEach((component) => {
      const types = component.types;

      if (types.includes('street_number')) {
        addressComponents.streetNumber = component.long_name;
      } else if (types.includes('route')) {
        addressComponents.streetName = component.long_name;
      } else if (types.includes('locality')) {
        addressComponents.city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        addressComponents.state = component.long_name;
      } else if (types.includes('country')) {
        addressComponents.country = component.long_name;
      } else if (types.includes('postal_code')) {
        addressComponents.postalCode = component.long_name;
      }
    });

    return addressComponents;
  }
}
