import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Put } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { PaymentCardService } from '../payment-card.service';

@Roles({ userType: UserType.USER, role: '*' })
@ApiTags('User Payment Cards')
@Controller('/user/payment-cards')
export class UserPaymentCardController {
  constructor(private readonly paymentCardService: PaymentCardService) {}

  @Get()
  @ApiOperation({ summary: 'Get all payment cards for a user' })
  async getUserPaymentCards(@User('id') userId: string) {
    return this.paymentCardService.findByUserId(userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete payment card' })
  async deletePaymentCard(@Param('id', ParseUUIDPipe) id: string, @User('id') userId: string) {
    await this.paymentCardService.delete(id, userId);
    return { message: 'Payment card deleted successfully' };
  }

  @Put(':id/set-default')
  @ApiOperation({ summary: 'Set default payment card' })
  async setDefaultPaymentCard(@Param('id', ParseUUIDPipe) id: string, @User('id') userId: string) {
    await this.paymentCardService.setDefaultCard(id, userId);
    return { message: 'Payment card set as default successfully' };
  }
}
