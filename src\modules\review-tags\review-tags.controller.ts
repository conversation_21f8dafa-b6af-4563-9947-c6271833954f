import { Public } from '@/modules/auth/decorators/public.decorator';
import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { ReviewTag } from './entities/review-tag.entity';
import { ReviewTagsService } from './review-tags.service';

@ApiTags('Review Tags')
@Controller('review-tags')
export class ReviewTagsController {
  constructor(private readonly reviewTagsService: ReviewTagsService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all review tags' })
  async getReviewTags(): Promise<ReviewTag[]> {
    return this.reviewTagsService.getReviewTags();
  }
}
