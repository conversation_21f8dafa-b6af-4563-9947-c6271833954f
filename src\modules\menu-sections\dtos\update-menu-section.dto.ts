import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PositionItemWithPricePositiveDto } from '@/common/dtos/position-item-with-price.dto';
import { ApiProperty } from '@nestjs/swagger';

import { ViewType } from '../enums/view-type.enum';
import { ScheduleItem } from './create-menu-section.dto';

export class UpdateMenuSectionDto {
  @ApiProperty({ description: 'Internal name of the menu section', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the menu section', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Published name in English', required: false })
  @IsOptional()
  @IsString()
  publishedNameEn?: string;

  @ApiProperty({ description: 'Published name in Vietnamese', required: false })
  @IsOptional()
  @IsString()
  publishedNameVi?: string;

  @ApiProperty({
    description: 'View type of the menu section',
    enum: ViewType,
    example: ViewType.GRID,
    required: false,
  })
  @IsOptional()
  @IsEnum(ViewType)
  viewType?: ViewType;

  @ApiProperty({
    description: 'Available schedule for the menu section',
    required: false,
    type: [ScheduleItem],
    example: [{ day: 1, start: '10:00', end: '14:00', isAllDay: false }],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  availableSchedule?: ScheduleItem[];

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Array of menu item IDs with positions and optional price',
    required: false,
    type: [PositionItemWithPricePositiveDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemWithPricePositiveDto)
  menuItemIds?: PositionItemWithPricePositiveDto[];

  @ApiProperty({ description: 'Array of menu IDs to add this section to', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuIds?: string[];
}
