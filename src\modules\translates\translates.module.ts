import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '../menu-items/entities/menu-item.entity';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { SharedModule } from '../shared/shared.module';
import { TranslatesController } from './translates.controller';
import { TranslatesService } from './translates.service';

@Module({
  imports: [TypeOrmModule.forFeature([MenuSection, MenuItem, MenuItemOptionGroup]), SharedModule],
  controllers: [TranslatesController],
  providers: [TranslatesService],
  exports: [TranslatesService],
})
export class TranslatesModule {}
