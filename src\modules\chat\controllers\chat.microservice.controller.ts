import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { ChatService } from '../chat.service';
import { UserConnectedEventPayload } from '../chat.types';
import { ApiEventPattern } from '@/common/enums/event-pattern.enum';

@Controller()
export class ChatMicroserviceController {
  constructor(private readonly chatService: ChatService) {}

  @MessagePattern(ApiEventPattern.CHAT_USER_CONNECTED)
  async handleUserConnected(@Payload() payload: UserConnectedEventPayload) {
    await this.chatService.markMessagesAsDelivered(payload);
  }

  @MessagePattern(ApiEventPattern.CHAT_USER_JOINED_ORDER)
  async handleUserJoinedOrder(@Payload() payload: UserConnectedEventPayload) {
    await this.chatService.markMessagesAsSeen(payload.orderId, payload.senderType);
  }
}
