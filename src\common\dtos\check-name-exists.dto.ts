import { IsNotEmpty, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, <PERSON>U<PERSON><PERSON>, ValidateI<PERSON> } from 'class-validator';

import { OnlyOneField } from '@/common/validators/single-field.validator';
import { ApiProperty } from '@nestjs/swagger';

export class CheckNameExistsBaseDto {
  @ApiProperty({ description: 'Internal name of the entity', required: false })
  @ValidateIf((o) => o.internalName !== undefined || o.publishedName === undefined)
  @OnlyOneField()
  @IsString()
  @IsNotEmpty()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the entity', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  publishedName?: string;

  @ApiProperty({ description: 'ID of the entity to exclude' })
  @IsOptional()
  @IsUUID()
  excludeId?: string;
}

export class CheckNameExistsDto extends CheckNameExistsBaseDto {
  @ApiProperty({ description: 'ID of the restaurant' })
  @IsUUID()
  restaurantId: string;
}

export class CheckNameExistsBrandDto extends CheckNameExistsBaseDto {
  @ApiProperty({ description: 'ID of the brand' })
  @IsUUID()
  brandId: string;
}

export class NameExistsResponseDto {
  exists: boolean;
}
