import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Controller, ParseFilePipe, Post, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';

import { UploadImageDto } from './dtos/upload.dto';
import { UploadService } from './upload.service';
import { ImageTypeValidator } from './validators/image-type.validator';

@ApiTags('Upload')
@Controller('upload')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  // @Post('presigned-url')
  // async getPresignedUrl(@Body('fileType') fileType: string, @Body('folder') folder?: string) {
  //   return this.uploadService.generatePresignedUrl(fileType, folder);
  // }

  // @Post('menu-item/presigned-url')
  // async getMenuItemPresignedUrl(@Body('fileType') fileType: string) {
  //   return this.uploadService.generatePresignedUrlForMenuItem(fileType);
  // }

  @Post('image')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({ schema: { type: 'object', properties: { file: { type: 'string', format: 'binary' } } } })
  async uploadFile(
    @Query() query: UploadImageDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new ImageTypeValidator()],
      }),
    )
    file: Express.Multer.File,
  ) {
    return this.uploadService.uploadFile(file, query.rule);
  }
}
