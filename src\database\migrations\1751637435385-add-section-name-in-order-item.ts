import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSectionNameInOrderItem1751637435385 implements MigrationInterface {
  name = 'AddSectionNameInOrderItem1751637435385';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items" ADD "menu_section_name" character varying NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items" DROP COLUMN "menu_section_name"`);
  }
}
