import { <PERSON><PERSON>ource, Entity<PERSON>anager, FindOptionsWhere, In, Repository } from 'typeorm';

import { UserType } from '@/modules/auth/enums/user-type.enum';
import { AllUserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { OrdersService } from '@/modules/orders/orders.service';
import { FCMService } from '@/modules/shared/fcm/fcm.service';
import { MicroserviceClientService } from '@/modules/shared/microservice/microservice-client.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ConversationQueryDto } from './dto/conversation-query.dto';
import { MessagesQueryDto } from './dto/messages-query.dto';
import { PaginatedConversationsResponseDto, PaginatedMessagesResponseDto } from './dto/paginated-response.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { ChatConversation } from './entities/chat-conversation.entity';
import { ChatMessage, MessageStatus, SenderType } from './entities/chat-message.entity';

@Injectable()
export class ChatService {
  constructor(
    @InjectRepository(ChatConversation)
    private conversationRepository: Repository<ChatConversation>,
    @InjectRepository(ChatMessage)
    private messageRepository: Repository<ChatMessage>,

    private ordersService: OrdersService,
    private microserviceClient: MicroserviceClientService,
    private fcmService: FCMService,
    private dataSource: DataSource,
  ) {}

  async findOrCreateConversation(
    orderId: string,
    order: { userId: string; restaurantId: string },
    manager: EntityManager,
  ): Promise<ChatConversation> {
    let conversation = await manager.findOne(ChatConversation, {
      where: { orderId: orderId },
    });

    if (!conversation) {
      conversation = manager.create(ChatConversation, {
        userId: order.userId,
        restaurantId: order.restaurantId,
        orderId: orderId,
      });
      conversation = await manager.save(ChatConversation, conversation);
    }

    return conversation;
  }

  verifyAccessOrder(user: AllUserJwtInfo, userId: string, restaurantId: string): void {
    switch (user.userType) {
      case UserType.USER:
        if (user.id !== userId) {
          throw new BadRequestException('You are not the owner of this order');
        }
        break;
      case UserType.MERCHANT_STAFF:
        if (user.restaurantId !== restaurantId) {
          throw new BadRequestException('You are not the staff of this restaurant');
        }
        break;
    }
  }

  async sendMessage(user: AllUserJwtInfo, sendMessageDto: SendMessageDto): Promise<ChatMessage> {
    const { orderId, content } = sendMessageDto;

    const order = await this.ordersService.verifyAccessAndGetOrder(user, orderId);

    if (order.archivedAt) {
      throw new BadRequestException('Session chat is closed');
    }

    const { conversation, savedMessage } = await this.dataSource.transaction(async (manager) => {
      const conversation = await this.findOrCreateConversation(orderId, order, manager);

      const senderType = user.userType === UserType.USER ? SenderType.USER : SenderType.RESTAURANT;

      const message = manager.create(ChatMessage, {
        conversationId: conversation.id,
        senderType,
        userId: conversation.userId,
        restaurantId: conversation.restaurantId,
        merchantUserId: user.userType === UserType.MERCHANT_USER ? user.id : null,
        content,
        status: MessageStatus.SENT,
      });

      const savedMessage = await manager.save(ChatMessage, message);

      await manager.update(ChatConversation, conversation.id, {
        lastMessageAt: new Date(),
        lastMessageContent: savedMessage.content,

        ...(savedMessage.senderType === SenderType.USER
          ? { unreadCountRestaurant: () => 'unread_count_restaurant + 1' }
          : { unreadCountUser: () => 'unread_count_user + 1' }),
      });

      return { conversation, savedMessage };
    });

    await this.microserviceClient.publishNewMessage({
      message: savedMessage,
      conversationId: conversation.id,
      orderId: conversation.orderId,
      userId: conversation.userId,
      restaurantId: conversation.restaurantId,
      senderType: savedMessage.senderType,
    });

    // Send push notification
    try {
      if (savedMessage.senderType === SenderType.USER) {
        // User sent message, notify restaurant staff
        void this.fcmService.sendChatNotificationToRestaurant(
          conversation.restaurantId,
          order.user?.firstName || 'User',
          content,
          orderId,
          order.orderCode,
        );
      } else {
        // Restaurant staff sent message, notify user
        void this.fcmService.sendChatNotificationToUser(
          conversation.userId,
          order.restaurantName || 'Restaurant',
          content,
          orderId,
          order.orderCode,
        );
      }
    } catch (error) {
      // Log error but don't fail the message sending
      console.error('Failed to send push notification:', error);
    }
    return savedMessage;
  }

  async getConversationsByUser(
    userId: string,
    query: ConversationQueryDto,
  ): Promise<PaginatedConversationsResponseDto> {
    const limit = query.limit || 20;
    const queryBuilder = this.conversationRepository
      .createQueryBuilder('conversation')
      .leftJoinAndSelect('conversation.order', 'order')
      .leftJoinAndSelect('conversation.restaurant', 'restaurant')
      .where('conversation.userId = :userId', { userId })
      .orderBy('conversation.lastMessageAt', 'DESC');

    if (query.cursor) {
      queryBuilder.andWhere('conversation.lastMessageAt < :cursor', { cursor: new Date(query.cursor) });
    }

    const conversations = await queryBuilder.limit(limit + 1).getMany();

    const hasMore = conversations.length > limit;
    const data = hasMore ? conversations.slice(0, limit) : conversations;
    const nextCursor = hasMore && data.length > 0 ? data[data.length - 1].lastMessageAt?.getTime() : undefined;

    return {
      data,
      cursor: nextCursor,
      hasMore,
    };
  }

  async getConversationsByRestaurant(
    restaurantId: string,
    query: ConversationQueryDto,
  ): Promise<PaginatedConversationsResponseDto> {
    const limit = query.limit || 20;
    const queryBuilder = this.conversationRepository
      .createQueryBuilder('conversation')
      .leftJoinAndSelect('conversation.order', 'order')
      .leftJoinAndSelect('conversation.user', 'user')
      .where('conversation.restaurantId = :restaurantId', { restaurantId })
      .orderBy('conversation.lastMessageAt', 'DESC');

    if (query.cursor) {
      queryBuilder.andWhere('conversation.lastMessageAt < :cursor', { cursor: new Date(query.cursor) });
    }

    const conversations = await queryBuilder.limit(limit + 1).getMany();

    const hasMore = conversations.length > limit;
    const data = hasMore ? conversations.slice(0, limit) : conversations;
    const nextCursor = hasMore && data.length > 0 ? data[data.length - 1].lastMessageAt?.getTime() : undefined;

    return {
      data,
      cursor: nextCursor,
      hasMore,
    };
  }

  async findOneConversationByOrderId(user: AllUserJwtInfo, orderId: string): Promise<ChatConversation> {
    const codition: FindOptionsWhere<ChatConversation> = { orderId };

    if (user.userType === UserType.USER) {
      codition.userId = user.id;
    } else if (user.userType === UserType.MERCHANT_STAFF) {
      codition.restaurantId = user.restaurantId;
    }

    const conversation = await this.conversationRepository.findOne({
      where: { orderId },
      relations: ['order', 'restaurant', 'user'],
    });
    if (!conversation) {
      throw new NotFoundException('Conversation of orderId: ' + orderId + ' not found');
    }
    return conversation;
  }

  async getMessages(
    user: AllUserJwtInfo,
    orderId: string,
    query: MessagesQueryDto,
  ): Promise<PaginatedMessagesResponseDto> {
    const conversation = await this.findOneConversationByOrderId(user, orderId);

    const limit = query.limit || 50;
    const queryBuilder = this.messageRepository
      .createQueryBuilder('message')
      .where('message.conversationId = :conversationId', { conversationId: conversation.id })
      .orderBy('message.id', 'DESC');

    if (query.cursor) {
      queryBuilder.andWhere('message.id < :cursor', { cursor: query.cursor });
    }

    const messages = await queryBuilder.limit(limit + 1).getMany();

    const hasMore = messages.length > limit;
    const data = hasMore ? messages.slice(0, limit) : messages;
    const nextCursor = hasMore && data.length > 0 ? data[data.length - 1].id : undefined;

    return {
      data,
      cursor: nextCursor,
      hasMore,
    };
  }

  private async markMessagesAsDeliveredByUser(userId: string): Promise<void> {
    let isCompleted = false;
    while (!isCompleted) {
      const queryBuilder = this.messageRepository.createQueryBuilder('message');
      const listChatDistinctRestaurantId = await queryBuilder
        .where({
          userId,
          senderType: SenderType.RESTAURANT,
          status: MessageStatus.SENT,
        })
        .select('DISTINCT message.restaurantId', 'restaurantId')
        .limit(50)
        .getRawMany();
      const listRestaurantIds = listChatDistinctRestaurantId.map((item) => item.restaurantId);

      if (listRestaurantIds.length === 0) {
        isCompleted = true;
        break;
      }

      for (const restaurantId of listRestaurantIds) {
        const now = new Date();
        const updatedMessage = await this.messageRepository.update(
          {
            userId,
            restaurantId,
            senderType: SenderType.RESTAURANT,
            status: MessageStatus.SENT,
          },
          {
            status: MessageStatus.DELIVERED,
            deliveredAt: now,
          },
        );
        if (updatedMessage.affected === 0) {
          continue;
        }
        await this.microserviceClient.publishMessageStatusUpdate({
          userId,
          restaurantId,
          senderType: SenderType.USER,
          status: MessageStatus.DELIVERED,
          updatedAt: now,
        });
      }
    }
  }

  private async markMessagesAsDeliveredByRestaurant(restaurantId: string): Promise<void> {
    let isCompleted = false;
    while (!isCompleted) {
      const queryBuilder = this.messageRepository.createQueryBuilder('message');
      const listChatDistinctRestaurantId = await queryBuilder
        .where({
          restaurantId,
          senderType: SenderType.USER,
          status: MessageStatus.SENT,
        })
        .select('DISTINCT message.userId', 'userId')
        .limit(50)
        .getRawMany();
      const listUserId = listChatDistinctRestaurantId.map((item) => item.userId);

      if (listUserId.length === 0) {
        isCompleted = true;
        break;
      }

      for (const userId of listUserId) {
        const now = new Date();
        const updatedMessage = await this.messageRepository.update(
          {
            userId,
            restaurantId,
            senderType: SenderType.USER,
            status: MessageStatus.SENT,
          },
          {
            status: MessageStatus.DELIVERED,
            deliveredAt: now,
          },
        );
        if (updatedMessage.affected === 0) {
          continue;
        }
        await this.microserviceClient.publishMessageStatusUpdate({
          userId,
          restaurantId,
          senderType: SenderType.RESTAURANT,
          status: MessageStatus.DELIVERED,
          updatedAt: now,
        });
      }
    }
  }

  async markMessagesAsDelivered({
    userId,
    restaurantId,
    senderType,
  }: {
    userId: string;
    restaurantId?: string;
    senderType: SenderType;
  }): Promise<void> {
    if (senderType === SenderType.USER) {
      return this.markMessagesAsDeliveredByUser(userId);
    } else if (restaurantId) {
      return this.markMessagesAsDeliveredByRestaurant(restaurantId);
    }
  }

  async markMessagesAsSeen(orderId: string, senderType: SenderType): Promise<void> {
    const conversation = await this.conversationRepository.findOne({ where: { orderId } });

    if (!conversation) {
      console.info('Conversation of orderId: ', orderId, ' not found');
      return;
    }
    const oppositeSenderType = senderType === 'user' ? SenderType.RESTAURANT : SenderType.USER;
    const now = new Date();
    let isHaveMessage = false;
    await this.dataSource.transaction(async (manager) => {
      const updatedMessage = await manager.update(
        ChatMessage,
        {
          conversationId: conversation.id,
          senderType: oppositeSenderType,
          status: In([MessageStatus.DELIVERED, MessageStatus.SENT]),
        },
        {
          status: MessageStatus.SEEN,
          seenAt: now,
        },
      );
      isHaveMessage = !!updatedMessage.affected && updatedMessage.affected > 0;
      if (!isHaveMessage) return;
      const resetField = oppositeSenderType === SenderType.USER ? 'unreadCountUser' : 'unreadCountRestaurant';
      const lastSeenField = oppositeSenderType === SenderType.USER ? 'userLastSeenAt' : 'restaurantLastSeenAt';

      await manager.update(ChatConversation, conversation.id, {
        [resetField]: 0,
        [lastSeenField]: now,
      });
    });

    if (isHaveMessage) {
      await this.microserviceClient.publishMessageStatusUpdate({
        userId: conversation.userId,
        restaurantId: conversation.restaurantId,
        senderType,
        status: MessageStatus.SEEN,
        updatedAt: now,
      });
    }
  }
}
