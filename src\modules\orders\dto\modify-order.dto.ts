import { Type } from 'class-transformer';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString, IsUUID, ValidateNested } from 'class-validator';

import { CartItemGroupOptionDto } from '@/modules/carts/dto/add-to-cart.dto';
import { ApiProperty, OmitType } from '@nestjs/swagger';

export class CreateOrderItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  menuItemId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  menuSectionId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @IsPositive()
  amount: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    type: [CartItemGroupOptionDto],
    example: [
      {
        optionGroupId: 'UUID',
        options: [{ id: 'UUID', amount: 1 }],
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemGroupOptionDto)
  groupOptions: CartItemGroupOptionDto[];
}

export class EditOrderItemDto extends OmitType(CreateOrderItemDto, ['menuItemId']) {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  orderItemId: string;
}

export class ModifyOrderDto {
  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsOptional()
  removeItemIds?: string[];

  @ApiProperty({ type: [EditOrderItemDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EditOrderItemDto)
  @IsOptional()
  editItems?: EditOrderItemDto[];

  @ApiProperty({ type: [CreateOrderItemDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  @IsOptional()
  createItems?: CreateOrderItemDto[];
}
