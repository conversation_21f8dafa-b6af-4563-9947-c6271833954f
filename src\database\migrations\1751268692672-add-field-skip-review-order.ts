import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldSkipReviewOrder1751268692672 implements MigrationInterface {
  name = 'AddFieldSkipReviewOrder1751268692672';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" ADD "is_skip_review" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "is_skip_review"`);
  }
}
