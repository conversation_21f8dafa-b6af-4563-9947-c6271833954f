import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { FcmRestaurantType, FcmUserType } from '@/modules/shared/fcm/fcm.types';
import { ApiProperty } from '@nestjs/swagger';

export class FakeSendNotificationDto {
  @ApiProperty({
    description: 'Order ID to send notification for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  orderId: string;

  @ApiProperty({
    description: 'Type of notification to send to user',
    enum: FcmUserType,
    required: false,
  })
  @IsOptional()
  @IsEnum(FcmUserType)
  userNotificationType?: FcmUserType;

  @ApiProperty({
    description: 'Type of notification to send to staff',
    enum: FcmRestaurantType,
    required: false,
  })
  @IsOptional()
  @IsEnum(FcmRestaurantType)
  staffNotificationType?: FcmRestaurantType;

  @ApiProperty({
    description: 'Optional note for the fake notification',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}
