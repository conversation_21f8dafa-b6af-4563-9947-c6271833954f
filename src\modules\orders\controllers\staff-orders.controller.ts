import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { ModifyOrderDto } from '../dto/modify-order.dto';
import { RejectOrderDto, SetDeliveryEtaDto, SetPrepTimeDto } from '../dto/order-actions.dto';
import { OrderQueryDto } from '../dto/order-query.dto';
import { OrdersService } from '../orders.service';

@ApiTags('(Ops) Orders')
@Controller('staff/orders')
@Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
export class StaffOrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get()
  async findAll(@Query() query: OrderQueryDto, @User() user: UserMerchantStaffJwtInfo) {
    return this.ordersService.findAll(query, undefined, user?.restaurantId, true);
  }

  @Get(':id')
  async findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantStaffJwtInfo) {
    return this.ordersService.findOne(id, { restaurantId: user?.restaurantId, relations: true });
  }

  @Post(':id/accept')
  @ApiOperation({ summary: 'Accept a new order and move to kitchen' })
  async acceptOrder(
    @Param('id', ParseUUIDPipe) id: string,
    @User() user: UserMerchantStaffJwtInfo,
    @Body() setPrepTimeDto: SetPrepTimeDto,
  ) {
    return this.ordersService.acceptOrder(id, user?.restaurantId, setPrepTimeDto);
  }

  @Post(':id/reject')
  @ApiOperation({ summary: 'Reject a new order' })
  async rejectOrder(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() rejectOrderDto: RejectOrderDto,
    @User() user: UserMerchantStaffJwtInfo,
  ) {
    return this.ordersService.rejectOrder(id, rejectOrderDto, user?.restaurantId);
  }

  @Put(':id/update-preparing-eta')
  @ApiOperation({ summary: 'Update preparation time estimate' })
  async updatePreparingTime(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() SetPrepTimeDto: SetPrepTimeDto,
    @User() user: UserMerchantStaffJwtInfo,
  ) {
    return this.ordersService.updatePreparingTime(id, SetPrepTimeDto, user?.restaurantId);
  }

  @Post(':id/ready-for-delivery')
  @ApiOperation({ summary: 'Mark order as ready for delivery' })
  async markReadyForDelivery(
    @Param('id', ParseUUIDPipe) id: string,
    @User() user: UserMerchantStaffJwtInfo,
    @Body() setDeliveryEtaDto: SetDeliveryEtaDto,
  ) {
    return this.ordersService.markReadyForDelivery(id, setDeliveryEtaDto, user?.restaurantId);
  }

  @Put(':id/update-delivery-eta')
  @ApiOperation({ summary: 'Update delivery ETA' })
  async updateDeliveryEta(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() setDeliveryEtaDto: SetDeliveryEtaDto,
    @User() user: UserMerchantStaffJwtInfo,
  ) {
    return this.ordersService.updateDeliveryEta(id, setDeliveryEtaDto, user?.restaurantId);
  }

  @Post(':id/delivered')
  @ApiOperation({ summary: 'Mark order as delivered' })
  async markAsDelivered(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantStaffJwtInfo) {
    return this.ordersService.markAsDelivered(id, user?.restaurantId);
  }

  @Post(':id/unfulfilled')
  @ApiOperation({ summary: 'Mark order as unfulfilled' })
  async markAsUnfulfilled(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantStaffJwtInfo) {
    return this.ordersService.markAsUnfulfilled(id, user?.restaurantId);
  }

  @Put(':id/modify')
  @ApiOperation({ summary: 'Modify order items of NEW order: remove, edit, or create order items' })
  async modifyOrder(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() modifyOrderDto: ModifyOrderDto,
    @User() user: UserMerchantStaffJwtInfo,
  ) {
    return await this.ordersService.modifyOrder(id, modifyOrderDto, user.id, user.restaurantId);
  }
}
