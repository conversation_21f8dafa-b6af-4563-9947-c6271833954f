import Redis from 'ioredis';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisService.name);
  private readonly client: Redis;

  constructor(private configService: ConfigService) {
    this.client = new Redis({
      host: this.configService.get<string>('redis.host', 'localhost'),
      port: this.configService.get<number>('redis.port', 6379),
      password: this.configService.get<string>('redis.password'),
    });

    this.client.on('connect', () => {
      this.logger.log('Connected to Red<PERSON>');
    });

    this.client.on('error', (error) => {
      this.logger.error('Redis connection error:', error);
    });
  }

  async onModuleDestroy() {
    await this.client.quit();
  }

  async setKey(key: string, value: string, ttlSeconds: number = 300): Promise<void> {
    await this.client.setex(key, ttlSeconds, value);
    this.logger.log(`Key set for ${key} with TTL ${ttlSeconds} seconds`);
  }

  async getKey(key: string): Promise<string | null> {
    return await this.client.get(key);
  }

  async deleteKey(key: string): Promise<void> {
    await this.client.del(key);
    this.logger.log(`Key deleted for ${key}`);
  }

  async incrKey(key: string): Promise<number> {
    return await this.client.incr(key);
  }

  async expireKey(key: string, ttlSeconds: number): Promise<void> {
    await this.client.expire(key, ttlSeconds);
  }
}
