import * as bcrypt from 'bcrypt';
import { Request } from 'express';
import ms, { StringValue } from 'ms';

import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { ACCESS_TOKEN_COOKIE_NAME, REFRESH_TOKEN_COOKIE_NAME } from '@auth/constants/auth.constants';
import { RefreshTokenDto } from '@auth/dtos/refresh-token.dto';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantStaffJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';

import { LoginMerchantStaffDto } from './dtos/login.dto';

@Injectable()
export class AuthMerchantStaffService {
  private acessTokenOptions: JwtSignOptions;
  private refreshTokenOptions: JwtSignOptions;
  private cookieMaxAgeAccessToken: number;
  private cookieMaxAgeRefreshToken: number;

  constructor(
    private merchantStaffService: MerchantStaffService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    this.acessTokenOptions = {
      secret: this.configService.get<string>('auth.merchantStaffJwtAccessSecret'),
      expiresIn: this.configService.get<string>('auth.accessTokenExpiresIn'),
    };

    this.refreshTokenOptions = {
      secret: this.configService.get<string>('auth.merchantStaffJwtRefreshSecret'),
      expiresIn: this.configService.get<string>('auth.refreshTokenExpiresIn'),
    };

    this.cookieMaxAgeAccessToken = ms((this.acessTokenOptions.expiresIn as StringValue) || '1d');
    this.cookieMaxAgeRefreshToken = ms((this.refreshTokenOptions.expiresIn as StringValue) || '1d');
  }

  async validateUser(username: string, password: string) {
    const user = await this.merchantStaffService.findByUsername(username);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Remove password from the returned object
    const { password: _, ...result } = user;
    return result;
  }

  async login(loginDto: LoginMerchantStaffDto, response: any) {
    const user = await this.validateUser(loginDto.username, loginDto.password);

    const payload: UserMerchantStaffJwtPayload = {
      sub: user.id,
      username: user.username,
      restaurantId: user.restaurantId,
      userType: UserType.MERCHANT_STAFF,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload, this.acessTokenOptions);
    const refreshToken = this.jwtService.sign(payload, this.refreshTokenOptions);

    // Set cookies
    this.setCookies(response, accessToken, refreshToken);

    return {
      user: await this.merchantStaffService.getMe(user.id),
      accessToken,
      refreshToken,
    };
  }

  async refreshToken(request: Request, refreshTokenDto: RefreshTokenDto, response: any) {
    try {
      // Try to get refresh token from cookie first, then fall back to body
      let refreshToken = request.cookies?.refresh_token;

      if (!refreshToken) {
        refreshToken = refreshTokenDto?.refreshToken;
      }

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const decoded = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.merchantStaffJwtRefreshSecret'),
      });

      const user = await this.merchantStaffService.findOne(decoded.sub, null);

      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (user.banned) {
        throw new UnauthorizedException('User is banned');
      }

      const payload: UserMerchantStaffJwtPayload = {
        sub: user.id,
        username: user.username,
        restaurantId: user.restaurantId,
        userType: UserType.MERCHANT_STAFF,
        role: user.role,
      };

      const accessToken = this.jwtService.sign(payload, this.acessTokenOptions);
      const newRefreshToken = this.jwtService.sign(payload, this.refreshTokenOptions);

      // Set cookies
      this.setCookies(response, accessToken, newRefreshToken);

      return { accessToken, refreshToken: newRefreshToken };
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  logout(response: any) {
    // Clear cookies
    this.clearCookies(response);

    return { message: 'Logout successful' };
  }

  private setCookies(response: any, accessToken: string, refreshToken: string) {
    const cookieOptions = {
      httpOnly: this.configService.get<boolean>('auth.cookieHttpOnly'),
      secure: this.configService.get<boolean>('auth.cookieSecure'),
      sameSite: this.configService.get<string>('auth.cookieSameSite'),
    };

    response.cookie(ACCESS_TOKEN_COOKIE_NAME, accessToken, {
      maxAge: this.cookieMaxAgeAccessToken,
      ...cookieOptions,
    });
    response.cookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
      maxAge: this.cookieMaxAgeRefreshToken,
      ...cookieOptions,
    });
  }

  private clearCookies(response: any) {
    response.clearCookie(ACCESS_TOKEN_COOKIE_NAME);
    response.clearCookie(REFRESH_TOKEN_COOKIE_NAME);
  }
}
