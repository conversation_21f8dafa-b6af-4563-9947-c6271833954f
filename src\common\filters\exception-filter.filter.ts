import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string | string[] = 'Server Error!';
    let type: string = request.url;

    try {
      if (exception instanceof HttpException) {
        status = exception.getStatus();
        const exceptionResponse = exception.getResponse() as any;
        message = exceptionResponse.message || message;

        if (exceptionResponse.type) {
          type = exceptionResponse.type;
        }
      } else if (exception instanceof Error) {
        this.logger.error(`${exception.message}`, exception.stack);
      } else {
        this.logger.error('Unexpected error', exception);
      }

      response.status(status).json({
        type,
        message,
      });
    } catch (error) {
      this.logger.error('Error in exception filter', error);
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        type: request.url,
        message: 'Server Error!',
      });
    }
  }
}
