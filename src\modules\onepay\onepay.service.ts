import axios from 'axios';
import BigNumber from 'bignumber.js';
import * as crypto from 'crypto';
import { Request, Response } from 'express';
import { isNil } from 'lodash';
import qs from 'qs';
import { DataSource, IsNull, LessThan, Repository } from 'typeorm';

import { getClientIP } from '@/helpers/network';
import { generateRandomString } from '@/helpers/string';
import { Order } from '@/modules/orders/entities/order.entity';
import { OrdersService } from '@/modules/orders/orders.service';
import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { UserJwtInfo } from '../auth/types/jwt-payload.type';
import { PaymentCard } from '../payment-card/entities/payment-card.entity';
import { PaymentCardService } from '../payment-card/payment-card.service';
import { FcmUserType } from '../shared/fcm/fcm.types';
import { CallbackCreateTokenOnepay } from './dto/callback-create-token-onepay.dto';
import { CallbackOnepay } from './dto/callback-onepay.dto';
import { OnepayCreateTokenRequest } from './entities/onepay-create-token-request.entity';
import { OnepayOrderRequest } from './entities/onepay-order-request.entity';
import { OnepayVoidRequest } from './entities/onepay-void-request.entity';
import {
  OnepayCreateTokenPayload,
  OnepayPaymentPayload,
  OnePayTxnResponseCode,
  OnepayVoidPayload,
  PaymentOnepayStatus,
  PaymentResponse,
} from './onepay.constants';

@Injectable()
export class OnePayService {
  private readonly logger = new Logger(OnePayService.name);
  private readonly DEFAULT_IP = '127.0.0.1';

  private readonly config = {
    againLink: process.env.ONEPAY_AGAIN_LINK || '',
    createTokenUrl: process.env.ONEPAY_CREATE_TOKEN_URL || '',
    deleteTokenUrl: process.env.ONEPAY_TOKEN_URL || '',
    paymentUrl: process.env.ONEPAY_PAYMENT_URL || '',
    queryDRUrl: process.env.ONEPAY_QUERY_URL || '',
    voidUrl: process.env.ONEPAY_VOID_URL || '',
    returnUrl: process.env.ONEPAY_RETURNURL || '',
    merchantAddCard: process.env.ONEPAY_MERCHANT_ADD_CARD || '',
    accessCodeAddCard: process.env.ONEPAY_ACCESSCODE_ADD_CARD || '',
    hashCodeAddCard: process.env.ONEPAY_SECURE_HASH_CODE_ADD_CARD || '',
    merchantPayment: process.env.ONEPAY_MERCHANT_PAYMENT || '',
    accessCodePayment: process.env.ONEPAY_ACCESSCODE_PAYMENT || '',
    hashCodePayment: process.env.ONEPAY_SECURE_HASH_CODE_PAYMENT || '',
  };

  constructor(
    @InjectRepository(OnepayCreateTokenRequest)
    private readonly onepayCreateTokenRequestRepository: Repository<OnepayCreateTokenRequest>,
    @InjectRepository(OnepayOrderRequest)
    private readonly onepayOrderRequestRepository: Repository<OnepayOrderRequest>,
    @InjectRepository(OnepayVoidRequest)
    private readonly onepayVoidRequestRepository: Repository<OnepayVoidRequest>,

    @Inject(forwardRef(() => OrdersService))
    private readonly ordersService: WrapperType<OrdersService>,
    @Inject(forwardRef(() => PaymentCardService))
    private readonly paymentCardService: WrapperType<PaymentCardService>,
    private readonly dataSource: DataSource,
  ) {
    if (Object.values(this.config).some((value) => !value)) {
      throw new Error('Missing required OnePay configuration in .env file');
    }
  }

  // =========== Token Creation Methods ===========

  /**
   * Create a new token for payment card
   */
  async createToken(user: UserJwtInfo, request: Request) {
    const ip = getClientIP(request, this.logger);
    const userId = user.id;

    const onepayCreateTokenRequest = await this.createTokenRequest(userId, ip);
    const paymentPayload = this.createTokenPaymentPayload(onepayCreateTokenRequest, user);
    paymentPayload.vpc_SecureHash = this.generateSecureHash(paymentPayload, 'add-card');

    return {
      url: `${this.config.createTokenUrl}?${qs.stringify(paymentPayload, { encode: false })}`,
    };
  }

  /**
   * Delete a token from OnePay
   */
  async deleteToken(tokenNumber: string, tokenExpiry: string): Promise<boolean> {
    try {
      const deleteTokenPayload = this.createDeleteTokenPayload(tokenNumber, tokenExpiry);
      const payloadWithHash = {
        ...deleteTokenPayload,
        vpc_SecureHash: this.generateSecureHash(deleteTokenPayload, 'add-card'),
      };

      const response = await this.makeDeleteTokenRequest(payloadWithHash);
      return this.processDeleteTokenResponse(response);
    } catch (error) {
      this.logger.error('Error deleting token from OnePay:', {
        tokenNumber,
        error: error?.message,
      });
      return false;
    }
  }

  /**
   * Create a new token request record
   */
  private async createTokenRequest(userId: string, ip: string): Promise<OnepayCreateTokenRequest> {
    const onepayCreateTokenRequest = this.onepayCreateTokenRequestRepository.create({
      userId,
      currency: 'VND',
      locale: 'en',
      returnUrl: `${this.config.returnUrl}/onepay/save-token`,
      merchTxnRef: generateRandomString(40),
      orderInfo: 'ADD_CARD',
      amount: 100_000, // 1_000 VND
      ticketNo: ip,
      againLink: this.config.againLink,
      title: 'Gateway',
    });

    return this.onepayCreateTokenRequestRepository.save(onepayCreateTokenRequest);
  }

  /**
   * Create payment payload for token creation
   */
  private createTokenPaymentPayload(request: OnepayCreateTokenRequest, user: UserJwtInfo) {
    const payload: OnepayCreateTokenPayload = {
      vpc_CreateToken: true,
      vpc_Version: '2',
      vpc_Currency: 'VND',
      vpc_CardList: 'INTERCARD',
      vpc_AccessCode: this.config.accessCodeAddCard,
      vpc_Merchant: this.config.merchantAddCard,
      vpc_Locale: 'en',
      vpc_ReturnURL: request.returnUrl,
      vpc_MerchTxnRef: request.merchTxnRef,
      vpc_OrderInfo: request.orderInfo,
      vpc_Amount: request.amount.toString(),
      vpc_TicketNo: request.ticketNo,
      vpc_Command: 'pay',
      AgainLink: request.againLink,
      Title: request.title,

      vpc_Customer_Id: user.id,
      vpc_Customer_Email: user.email,
    };

    return payload;
  }

  /**
   * Create delete token payload
   */
  private createDeleteTokenPayload(tokenNumber: string, tokenExpiry: string) {
    const payload = {
      vpc_Command: 'deleteToken',
      vpc_Merchant: this.config.merchantAddCard,
      vpc_AccessCode: this.config.accessCodeAddCard,
      vpc_MerchTxnRef: generateRandomString(40),
      vpc_TokenNum: tokenNumber,
      vpc_TokenExp: tokenExpiry,
    };

    return payload;
  }

  /**
   * Make delete token request to OnePay API
   */
  private async makeDeleteTokenRequest(payload: any) {
    const queryString = qs.stringify(payload, { encode: false });

    const response = await axios.post(this.config.deleteTokenUrl, queryString, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (response.status !== 200) {
      throw new Error(`Delete token API request failed: ${response.status} ${response.statusText}`);
    }

    return response.data;
  }

  /**
   * Process delete token response from OnePay
   */
  private processDeleteTokenResponse(responseText: string): boolean {
    try {
      const responseData = qs.parse(responseText) as any;
      this.logger.log('Delete token response:', responseData);

      // Verify secure hash
      const isValidResponse = this.verifySecureHash(responseData, 'add-card');
      if (!isValidResponse) {
        this.logger.error('Invalid secure hash in delete token response');
        return false;
      }

      // Check response code
      const responseCode = responseData.vpc_TxnResponseCode;
      return responseCode === OnePayTxnResponseCode.SUCCESS;
    } catch (error) {
      this.logger.error('Error processing delete token response:', error);
      return false;
    }
  }

  // =========== Void/Refund Methods ===========

  /**
   * Void a transaction to refund the amount back to user
   */
  async voidTransaction(request: OnepayCreateTokenRequest, type: 'add-card' | 'payment'): Promise<boolean> {
    try {
      const voidPayload = await this.createVoidPayload(request.merchTxnRef, request.amount, type);
      const payloadWithHash = {
        ...voidPayload,
        vpc_SecureHash: this.generateSecureHash(voidPayload, type),
      };

      this.logger.log('Initiating void transaction:', { merchTxnRef: request.merchTxnRef, type });

      const response = await this.makeVoidRequest(payloadWithHash);
      return this.processVoidResponse(response, request.merchTxnRef);
    } catch (error) {
      this.logger.error('Error voiding transaction:', {
        merchTxnRef: request.merchTxnRef,
        error: error?.message,
      });
      return false;
    }
  }

  /**
   * Create void payload
   */
  private async createVoidPayload(merchTxnRef: string, amount: number | string, type: 'add-card' | 'payment') {
    const voidRequest = this.onepayVoidRequestRepository.create({
      orgMerchTxnRef: merchTxnRef,
      merchTxnRef: generateRandomString(40),
      amount: Number(amount),
      operator: 'system',
      type,
    });
    const voidRequestSaved = await this.onepayVoidRequestRepository.save(voidRequest);
    const payload: OnepayVoidPayload = {
      vpc_Version: '2',
      vpc_Command: 'voidPurchase',
      vpc_Amount: voidRequestSaved.amount.toString(),
      vpc_AccessCode: type === 'add-card' ? this.config.accessCodeAddCard : this.config.accessCodePayment,
      vpc_Merchant: type === 'add-card' ? this.config.merchantAddCard : this.config.merchantPayment,
      vpc_OrgMerchTxnRef: voidRequestSaved.orgMerchTxnRef,
      vpc_MerchTxnRef: voidRequestSaved.merchTxnRef,
      vpc_Operator: voidRequestSaved.operator,
    };

    return payload;
  }

  /**
   * Make void request to OnePay API
   */
  private async makeVoidRequest(payload: OnepayVoidPayload & { vpc_SecureHash: string }) {
    const queryString = qs.stringify(payload, { encode: false });
    const voidUrl = `${this.config.voidUrl}/${payload.vpc_Merchant}/purchases/${payload.vpc_OrgMerchTxnRef}/voids/${payload.vpc_MerchTxnRef}`;
    const response = await axios.put(voidUrl, queryString, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (response.status !== 200) {
      throw new Error(`Void API request failed: ${response.status} ${response.statusText}`);
    }

    return response.data;
  }

  /**
   * Process void response from OnePay
   */
  private processVoidResponse(responseText: string, merchTxnRef: string): boolean {
    try {
      const responseData = qs.parse(responseText) as any;
      this.logger.log('Void transaction response:', {
        merchTxnRef,
        responseCode: responseData.vpc_TxnResponseCode,
        message: responseData.vpc_Message,
      });

      // Verify secure hash
      const isValidResponse = this.verifySecureHash(responseData, 'add-card');
      if (!isValidResponse) {
        this.logger.error('Invalid secure hash in void response:', { merchTxnRef });
        return false;
      }

      // Check response code
      const responseCode = responseData.vpc_TxnResponseCode;
      const isSuccess = responseCode === OnePayTxnResponseCode.SUCCESS;

      if (isSuccess) {
        this.logger.log('Void transaction successful:', { merchTxnRef });
      } else {
        this.logger.error('Void transaction failed:', {
          merchTxnRef,
          responseCode,
          message: responseData.vpc_Message,
        });
      }

      return isSuccess;
    } catch (error) {
      this.logger.error('Error processing void response:', { merchTxnRef, error });
      return false;
    }
  }

  // =========== Payment Methods ===========

  /**
   * Send payment request to OnePay
   */
  async sendPaymentRequest(order: Order, paymentCard: PaymentCard) {
    try {
      const onepayOrderRequest = await this.createOrderRequest(order);
      const paymentPayload = this.createPaymentPayload(onepayOrderRequest, order, paymentCard);
      const secureHash = this.generateSecureHash(paymentPayload, 'payment');
      const body = qs.stringify({ ...paymentPayload, vpc_SecureHash: secureHash });
      let response: any;
      try {
        response = await this.makePaymentRequest(body);
      } catch (err) {
        this.logger.error('Error in makePaymentRequest:', err);
        return PaymentOnepayStatus.PENDING;
      }
      return this.processPaymentResponse(response, onepayOrderRequest);
    } catch (error) {
      this.logger.error('Error in sendPaymentRequest:', error);
      throw error;
    }
  }

  /**
   * Create or get existing order request for payment
   */
  private async createOrderRequest(order: Order): Promise<OnepayOrderRequest> {
    // First check if payment request already exists for this order
    const existingRequest = await this.onepayOrderRequestRepository.findOne({
      where: { orderId: order.id },
    });

    if (existingRequest) {
      this.logger.warn(`Payment request already exists for order ${order.id}`, {
        orderId: order.id,
        existingMerchTxnRef: existingRequest.merchTxnRef,
        existingTxnResponseCode: existingRequest.txnResponseCode,
      });
      throw new BadRequestException('Payment request already exists for order');
    }

    // Create new payment request only if none exists
    const onepayOrderRequest = this.onepayOrderRequestRepository.create({
      userId: order.userId,
      orderId: order.id,
      orderCode: order.orderCode,
      restaurantId: order.restaurantId,
      currency: 'VND',
      locale: 'en',
      merchTxnRef: generateRandomString(40),
      orderInfo: order.orderCode,
      amount: BigNumber(order.total).decimalPlaces(0, BigNumber.ROUND_CEIL).multipliedBy(100).toNumber(),
      ticketNo: this.DEFAULT_IP,
    });

    try {
      return await this.onepayOrderRequestRepository.save(onepayOrderRequest);
    } catch (error) {
      // Handle duplicate key error - might happen in race condition
      if (error.code === '23505' || error.message.includes('duplicate key')) {
        // Try to get the existing record again
        const existingRequest = await this.onepayOrderRequestRepository.findOne({
          where: { orderId: order.id },
        });
        if (existingRequest) {
          this.logger.warn(`Race condition detected - using existing payment request for order ${order.id}`);
          throw new BadRequestException('Payment request already exists for order');
        }
      }
      throw error;
    }
  }

  /**
   * Create payment payload for order payment
   */
  private createPaymentPayload(request: OnepayOrderRequest, order: Order, paymentCard: PaymentCard) {
    const payload: OnepayPaymentPayload = {
      vpc_TokenNum: paymentCard.tokenNumber,
      vpc_TokenExp: paymentCard.tokenExpiry,

      vpc_Version: '2',
      vpc_Currency: 'VND',
      vpc_Command: 'pay',
      vpc_AccessCode: this.config.accessCodePayment,
      vpc_Merchant: this.config.merchantPayment,
      vpc_Locale: 'en',
      vpc_VerType: '2D',
      vpc_MerchTxnRef: request.merchTxnRef,
      vpc_OrderInfo: request.orderInfo,
      vpc_Amount: request.amount.toString(),
      vpc_TicketNo: request.ticketNo,

      vpc_Customer_Id: paymentCard.userId,
    };

    if (order.user?.email) {
      payload.vpc_Customer_Email = order.user.email;
    }

    return payload;
  }

  /**
   * Make payment request to OnePay API
   */
  private async makePaymentRequest(body: string) {
    const response = await axios.post(this.config.paymentUrl, body, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      maxBodyLength: Infinity,
    });

    if (response.status !== 200) {
      throw new Error(`Payment API request failed: ${response.status} ${response.statusText}`);
    }

    return response.data;
  }

  /**
   * Process payment response from OnePay
   */
  private async processPaymentResponse(responseText: string, onepayRequest: OnepayOrderRequest) {
    const responseData = qs.parse(responseText) as unknown as PaymentResponse;
    this.logger.log('payment response:', responseData);

    const isValidResponse = this.verifySecureHash(responseData, 'payment');
    if (!('vpc_TxnResponseCode' in responseData) || !isValidResponse) {
      throw new Error('Invalid secure hash in payment response');
    }

    const vpc_TxnResponseCode = responseData.vpc_TxnResponseCode;
    const isPending = [OnePayTxnResponseCode.PENDING, OnePayTxnResponseCode.PAYING].includes(vpc_TxnResponseCode);

    if (isPending) return PaymentOnepayStatus.PENDING;

    await this.onepayOrderRequestRepository.update({ id: onepayRequest.id }, { txnResponseCode: vpc_TxnResponseCode });

    return vpc_TxnResponseCode === OnePayTxnResponseCode.SUCCESS
      ? PaymentOnepayStatus.SUCCESS
      : PaymentOnepayStatus.FAILED;
  }

  // =========== Callback Methods ===========

  /**
   * Handle add payment card callback from OnePay
   */
  async handleAddPaymentCard(callbackData: CallbackCreateTokenOnepay) {
    try {
      this.validateCallbackData(callbackData);
      const onepayCreateTokenRequest = await this.findOneByMerchTxnRef(callbackData.vpc_MerchTxnRef);
      return this.processAddPaymentCard(callbackData, onepayCreateTokenRequest);
    } catch (error) {
      this.logger.error('Error in handleAddPaymentCard:', error);
      return 'responsecode=0&desc=processing-failed';
    }
  }

  /**
   * Validate callback data from OnePay
   */
  private validateCallbackData(callbackData: CallbackCreateTokenOnepay) {
    const { vpc_MerchTxnRef, vpc_TokenNum, vpc_TokenExp, vpc_CardNum, vpc_Card, vpc_CardUid, vpc_TxnResponseCode } =
      callbackData;

    if (isNil(vpc_MerchTxnRef)) {
      throw new BadRequestException('Missing merchTxnRef');
    }
    if (isNil(vpc_TxnResponseCode)) {
      throw new BadRequestException('Missing txnResponseCode');
    }

    if (vpc_TxnResponseCode === OnePayTxnResponseCode.SUCCESS) {
      if (isNil(vpc_TokenNum) || isNil(vpc_TokenExp) || isNil(vpc_CardNum) || isNil(vpc_Card) || isNil(vpc_CardUid)) {
        throw new BadRequestException('Missing required fields');
      }
    }

    if (!this.verifySecureHash(callbackData, 'add-card')) {
      throw new BadRequestException('Invalid secure hash');
    }
  }

  /**
   * Process add payment card callback
   */
  private async processAddPaymentCard(
    callbackData: CallbackCreateTokenOnepay,
    onepayCreateTokenRequest: OnepayCreateTokenRequest,
  ) {
    if (!isNil(onepayCreateTokenRequest.txnResponseCode)) {
      throw new BadRequestException('OnepayCreateTokenRequest already processed');
    }

    return this.dataSource.transaction(async (manager) => {
      if (callbackData.vpc_TxnResponseCode === OnePayTxnResponseCode.SUCCESS) {
        // Create payment card
        await this.paymentCardService.create(
          {
            userId: onepayCreateTokenRequest.userId,
            tokenNumber: callbackData.vpc_TokenNum,
            tokenExpiry: callbackData.vpc_TokenExp,
            maskedCardNumber: callbackData.vpc_CardNum,
            cardBank: callbackData.vpc_Card,
            cardUid: callbackData.vpc_CardUid,
          },
          manager,
        );

        this.logger.log('Payment card created successfully, refund will be processed by scheduled job:', {
          merchTxnRef: callbackData.vpc_MerchTxnRef,
          transactionNo: callbackData.vpc_TransactionNo,
          amount: callbackData.vpc_Amount,
        });
      } else {
        this.logger.error('Add payment card failed:', {
          transactionId: callbackData.vpc_MerchTxnRef,
          responseCode: callbackData.vpc_TxnResponseCode,
        });
      }

      const updatedRequest = await manager.update(
        OnepayCreateTokenRequest,
        {
          id: onepayCreateTokenRequest.id,
          txnResponseCode: IsNull(),
        },
        {
          txnResponseCode: callbackData.vpc_TxnResponseCode,
        },
      );
      if (updatedRequest.affected === 0) {
        throw new BadRequestException('Failed to update onepayCreateTokenRequest');
      }

      return 'responsecode=1&desc=confirm-success';
    });
  }

  // =========== Schedule Methods ===========

  /**
   * Check and update pending payment card requests
   */
  async checkPendingPaymentCardRequests(): Promise<void> {
    try {
      let hasMoreRequests = true;

      while (hasMoreRequests) {
        const pendingRequests = await this.findPendingCardRequests();
        if (pendingRequests.length === 0) {
          hasMoreRequests = false;
          continue;
        }

        this.logger.log(`Found ${pendingRequests.length} pending payment card requests to check`);
        await this.processPendingCardRequests(pendingRequests);
      }
    } catch (error) {
      this.logger.error('Error in checkPendingPaymentCardRequests:', error);
    }
  }

  /**
   * Process refunds for successful card additions that haven't been refunded
   */
  async processUnrefundedSuccessfulCardAdditions(): Promise<void> {
    try {
      const unrefundedRequests = await this.findUnrefundedSuccessfulCardRequests();
      if (unrefundedRequests.length === 0) {
        this.logger.log('No unrefunded successful card additions found');
        return;
      }

      this.logger.log(`Found ${unrefundedRequests.length} successful card additions to refund`);
      await this.processRefundRequests(unrefundedRequests);
    } catch (error) {
      this.logger.error('Error in processUnrefundedSuccessfulCardAdditions:', error);
    }
  }

  /**
   * Find successful card additions that haven't been refunded yet
   */
  private async findUnrefundedSuccessfulCardRequests() {
    return this.onepayCreateTokenRequestRepository.find({
      where: {
        txnResponseCode: OnePayTxnResponseCode.SUCCESS,
        isRefunded: false,
        retryCount: LessThan(3),
        isInvalid: false, // Exclude invalid requests
      },
      order: { updatedAt: 'ASC' },
      take: 100, // Process in batches
    });
  }

  /**
   * Process refund requests for unrefunded card additions
   */
  private async processRefundRequests(requests: OnepayCreateTokenRequest[]) {
    for (const request of requests) {
      try {
        await this.processIndividualRefundRequest(request);
      } catch (error) {
        this.logger.error('Error processing individual refund request:', {
          requestId: request.id,
          merchTxnRef: request.merchTxnRef,
          error: error?.message,
        });
      }
    }
  }

  /**
   * Process refund for individual card addition request
   */
  private async processIndividualRefundRequest(request: OnepayCreateTokenRequest) {
    this.logger.log('Processing refund for card addition:', {
      requestId: request.id,
      merchTxnRef: request.merchTxnRef,
      userId: request.userId,
    });

    // Query OnePay to get transaction details including TransactionNo
    try {
      // Attempt void transaction
      const isRefunded = await this.voidTransaction(request, 'add-card');

      // Update refund status

      if (isRefunded) {
        this.logger.log('Refund processed successfully:', {
          requestId: request.id,
          merchTxnRef: request.merchTxnRef,
        });
        await this.onepayCreateTokenRequestRepository.update({ id: request.id }, { isRefunded: true });
      } else {
        this.logger.error('Refund failed:', {
          requestId: request.id,
          merchTxnRef: request.merchTxnRef,
        });
        await this.onepayCreateTokenRequestRepository.update(
          { id: request.id },
          { retryCount: () => 'retry_count + 1' },
        );
      }
    } catch (error) {
      this.logger.error('Error during refund processing:', {
        requestId: request.id,
        merchTxnRef: request.merchTxnRef,
        error: error?.message,
      });
    }
  }

  /**
   * Find pending payment card requests
   */
  private async findPendingCardRequests() {
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    return this.onepayCreateTokenRequestRepository.find({
      where: {
        updatedAt: LessThan(fifteenMinutesAgo),
        txnResponseCode: IsNull(),
        drExists: true,
        isInvalid: false, // Exclude invalid requests to avoid infinite loop
      },
      order: { updatedAt: 'ASC' },
      take: 500,
    });
  }

  /**
   * Process pending payment card requests
   */
  private async processPendingCardRequests(requests: OnepayCreateTokenRequest[]) {
    for (const request of requests) {
      try {
        const queryResponse = await this.queryTransactionStatus(request.merchTxnRef, 'add-card');
        await this.processQueryCardDRResponse(queryResponse, request);
      } catch (error) {
        this.logger.error('Error processing pending request:', {
          requestId: request.id,
          merchTxnRef: request.merchTxnRef,
          error: error?.message,
        });
      }
    }
  }

  /**
   * Check and update pending payment order requests
   */
  async checkPendingPaymentOrderRequests(): Promise<void> {
    const threeMinutesAgo = new Date(Date.now() - 3 * 60 * 1000);
    try {
      let hasMoreRequests = true;
      while (hasMoreRequests) {
        const pendingRequests = await this.findPendingOrderRequests(threeMinutesAgo);
        if (pendingRequests.length === 0) {
          hasMoreRequests = false;
          continue;
        }

        this.logger.log(`Found ${pendingRequests.length} pending payment order requests to check`);
        await this.processPendingOrderRequests(pendingRequests);
      }
    } catch (error) {
      this.logger.error('Error in checkPendingPaymentOrderRequests:', error);
    }
  }

  /**
   * Find pending payment order requests
   */
  private async findPendingOrderRequests(threeMinutesAgo: Date) {
    return this.onepayOrderRequestRepository.find({
      where: {
        updatedAt: LessThan(threeMinutesAgo),
        txnResponseCode: IsNull(),
        drExists: true,
      },
      order: { updatedAt: 'ASC' },
      take: 500,
    });
  }

  /**
   * Process pending payment order requests
   */
  private async processPendingOrderRequests(requests: OnepayOrderRequest[]) {
    for (const request of requests) {
      try {
        const queryResponse = await this.queryTransactionStatus(request.merchTxnRef, 'payment');
        await this.processQueryPaymentDRResponse(queryResponse, request);
      } catch (error) {
        this.logger.error('Error processing pending request:', {
          requestId: request.id,
          merchTxnRef: request.merchTxnRef,
          error: error?.message,
        });
      }
    }
  }

  // =========== Helper Methods ===========

  /**
   * Query transaction status using OnePay QueryDR API
   */
  private async queryTransactionStatus(merchTxnRef: string, type: 'add-card' | 'payment'): Promise<any> {
    try {
      const queryDRParams = this.createQueryDRParams(merchTxnRef, type);
      const queryString = new URLSearchParams(queryDRParams).toString();

      const response = await axios.post(this.config.queryDRUrl, queryString, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.status !== 200) {
        throw new Error(`QueryDR API request failed: ${response.status} ${response.statusText}`);
      }

      const responseData = qs.parse(response.data);
      this.validateQueryDRResponse(responseData, type);

      this.logQueryDRResponse(type, merchTxnRef, responseData);
      return responseData;
    } catch (error) {
      this.logger.error('Error querying transaction status:', {
        merchTxnRef,
        error: error?.message,
      });
      throw error;
    }
  }

  /**
   * Create parameters for QueryDR request
   */
  private createQueryDRParams(merchTxnRef: string, type: 'add-card' | 'payment') {
    const params = {
      vpc_Command: 'queryDR',
      vpc_Version: '2',
      vpc_MerchTxnRef: merchTxnRef,
      vpc_Merchant: type === 'add-card' ? this.config.merchantAddCard : this.config.merchantPayment,
      vpc_AccessCode: type === 'add-card' ? this.config.accessCodeAddCard : this.config.accessCodePayment,
      // vpc_User: process.env.VPC_USER || 'op01',
      // vpc_Password: process.env.VPC_PASSWORD || 'op123456',
    };

    params['vpc_SecureHash'] = this.generateSecureHash(params, type);
    return params;
  }

  /**
   * Validate QueryDR response
   */
  private validateQueryDRResponse(responseData: any, type: 'add-card' | 'payment') {
    const isValidResponse = this.verifySecureHash(responseData, type);
    if (!isValidResponse) {
      throw new Error('Invalid secure hash in QueryDR response');
    }
  }

  /**
   * Log QueryDR response
   */
  private logQueryDRResponse(type: 'add-card' | 'payment', merchTxnRef: string, responseData: any) {
    this.logger.log('QueryDR response:', {
      type,
      merchTxnRef,
      drExists: responseData.vpc_DRExists,
      responseCode: responseData.vpc_TxnResponseCode,
      message: responseData.vpc_Message,
    });
  }

  /**
   * Process QueryDR response for payment card
   */
  private async processQueryCardDRResponse(queryResponse: any, onepayRequest: OnepayCreateTokenRequest): Promise<void> {
    const { vpc_DRExists, vpc_TxnResponseCode, vpc_Message } = queryResponse;

    const repository = this.onepayCreateTokenRequestRepository;

    if (vpc_DRExists !== 'Y') {
      await this.handleMissingTransaction(onepayRequest, repository);
      return;
    }

    const shouldContinueQuerying = this.shouldContinueQuerying(vpc_TxnResponseCode);
    if (shouldContinueQuerying) {
      await this.handlePendingTransaction(onepayRequest, vpc_TxnResponseCode, vpc_Message, repository);
      return;
    }

    const isSuccess = vpc_TxnResponseCode === OnePayTxnResponseCode.SUCCESS;
    this.logFinalTransactionStatus(isSuccess, onepayRequest.merchTxnRef, vpc_TxnResponseCode, vpc_Message);

    let isValid = false;
    try {
      this.validateCallbackData(queryResponse);
      isValid = true;
    } catch (e) {
      this.logger.error('Invalid callback data detected, marking request as invalid:', {
        requestId: onepayRequest.id,
        merchTxnRef: onepayRequest.merchTxnRef,
        error: e?.message,
      });
    }

    if (isValid) {
      await this.processAddPaymentCard(queryResponse, onepayRequest);
    } else {
      await this.onepayCreateTokenRequestRepository.update(
        { id: onepayRequest.id, txnResponseCode: IsNull() },
        { isInvalid: true, txnResponseCode: vpc_TxnResponseCode ?? null },
      );
    }
  }

  /**
   * Process QueryDR response for payment order
   */
  private async processQueryPaymentDRResponse(queryResponse: any, onepayRequest: OnepayOrderRequest): Promise<void> {
    const { vpc_DRExists, vpc_TxnResponseCode, vpc_Message } = queryResponse;
    const repository = this.onepayOrderRequestRepository;

    if (vpc_DRExists !== 'Y') {
      await this.dataSource.transaction('SERIALIZABLE', async (manager) => {
        this.logger.warn('Transaction not found in OnePay system:', {
          merchTxnRef: onepayRequest.merchTxnRef,
        });
        await this.ordersService.cancelOrderByJob(manager, onepayRequest.orderId, onepayRequest.restaurantId);
        await manager.update(OnepayOrderRequest, { id: onepayRequest.id }, { drExists: false });
      });
      void this.ordersService.notifyOrderStatusUpdate(onepayRequest.orderId, FcmUserType.ORDER_CANCELLED);
      return;
    }

    const shouldContinueQuerying = this.shouldContinueQuerying(vpc_TxnResponseCode);
    if (shouldContinueQuerying) {
      await this.handlePendingTransaction(onepayRequest, vpc_TxnResponseCode, vpc_Message, repository);
      return;
    }

    const isSuccess = vpc_TxnResponseCode === OnePayTxnResponseCode.SUCCESS;
    this.logFinalTransactionStatus(isSuccess, onepayRequest.merchTxnRef, vpc_TxnResponseCode, vpc_Message);
    const orderId = onepayRequest.orderId;
    const restaurantId = onepayRequest.restaurantId;
    await this.dataSource.transaction('SERIALIZABLE', async (manager) => {
      await this.ordersService.handlePaymentResultByJob(manager, orderId, restaurantId, isSuccess);
      const updatedRequest = await manager.update(
        OnepayOrderRequest,
        {
          id: onepayRequest.id,
          txnResponseCode: IsNull(),
        },
        {
          txnResponseCode: vpc_TxnResponseCode,
        },
      );
      if (updatedRequest.affected === 0) {
        throw new BadRequestException('Failed to update onepayOrderRequest');
      }
    });

    await this.ordersService.findOrderAndUpdateStatus(orderId);
  }

  /**
   * Handle missing transaction in OnePay system
   */
  private async handleMissingTransaction(
    request: OnepayCreateTokenRequest | OnepayOrderRequest,
    repository: Repository<OnepayCreateTokenRequest | OnepayOrderRequest>,
  ) {
    this.logger.warn('Transaction not found in OnePay system:', {
      merchTxnRef: request.merchTxnRef,
    });
    await repository.update({ id: request.id }, { drExists: false });
  }

  /**
   * Handle pending transaction
   */
  private async handlePendingTransaction(
    request: OnepayCreateTokenRequest | OnepayOrderRequest,
    responseCode: string,
    message: string,
    repository: Repository<OnepayCreateTokenRequest | OnepayOrderRequest>,
  ) {
    this.logger.log('Transaction still in progress, will continue querying:', {
      merchTxnRef: request.merchTxnRef,
      responseCode,
      message,
    });

    await repository.update({ id: request.id }, { updatedAt: new Date() });
  }

  /**
   * Handle final transaction status
   */
  private logFinalTransactionStatus(isSuccess: boolean, merchTxnRef: string, responseCode: string, message: string) {
    if (isSuccess) {
      this.logger.log('Transaction successful via QueryDR:', {
        merchTxnRef: merchTxnRef,
      });
    } else {
      this.logger.error('Transaction failed via QueryDR:', {
        merchTxnRef: merchTxnRef,
        responseCode,
        message,
      });
    }
  }

  /**
   * Check if should continue querying based on response code
   */
  private shouldContinueQuerying(responseCode: OnePayTxnResponseCode): boolean {
    return [OnePayTxnResponseCode.PENDING, OnePayTxnResponseCode.PAYING].includes(responseCode);
  }

  /**
   * Generate secure hash for OnePay requests
   */
  private generateSecureHash(callbackData: Record<string, any>, type: 'add-card' | 'payment'): string {
    try {
      const parametersToHash = this.filterParameters(callbackData);
      const stringToSign = this.createStringToSign(parametersToHash);
      return this.calculateHash(stringToSign, type);
    } catch (error) {
      this.logger.error('Generate secure hash error:', error);
      return '';
    }
  }

  /**
   * Filter parameters for hash generation
   */
  private filterParameters(callbackData: Record<string, any>): Record<string, string> {
    const parametersToHash: Record<string, string> = {};

    Object.keys(callbackData).forEach((key) => {
      const value = callbackData[key];
      if (
        (key.startsWith('vpc_') || key.startsWith('user_')) &&
        key !== 'vpc_SecureHash' &&
        key !== 'vpc_SecureHashType' &&
        value !== undefined &&
        value !== null &&
        value !== ''
      ) {
        parametersToHash[key] = value.toString();
      }
    });

    return parametersToHash;
  }

  /**
   * Create string to sign from parameters
   */
  private createStringToSign(parameters: Record<string, string>): string {
    const sortedKeys = Object.keys(parameters).sort();
    return sortedKeys.map((key) => `${key}=${parameters[key]}`).join('&');
  }

  /**
   * Calculate hash from string to sign
   */
  private calculateHash(stringToSign: string, type: 'add-card' | 'payment'): string {
    const keyBuffer = Buffer.from(
      type === 'add-card' ? this.config.hashCodeAddCard : this.config.hashCodePayment,
      'hex',
    );
    return crypto.createHmac('sha256', keyBuffer).update(stringToSign, 'utf8').digest('hex').toUpperCase();
  }

  /**
   * Verify secure hash from OnePay response
   */
  private verifySecureHash(callbackData: Record<string, any>, type: 'add-card' | 'payment'): boolean {
    try {
      const calculatedHash = this.generateSecureHash(callbackData, type);
      return calculatedHash === callbackData.vpc_SecureHash;
    } catch (error) {
      this.logger.error('Hash verification error:', error);
      return false;
    }
  }

  /**
   * Extract custom fields from callback data
   */
  private extractCustomFields(callbackData: CallbackOnepay): Record<string, string> {
    const customFields: Record<string, string> = {};
    Object.keys(callbackData).forEach((key) => {
      if (key.startsWith('user_')) {
        customFields[key] = callbackData[key];
      }
    });
    return customFields;
  }

  async findOneByMerchTxnRef(merchTxnRef: string) {
    const onepayCreateTokenRequest = await this.onepayCreateTokenRequestRepository.findOne({
      where: {
        merchTxnRef,
      },
    });
    if (!onepayCreateTokenRequest) {
      throw new NotFoundException('OnepayCreateTokenRequest not found');
    }
    return onepayCreateTokenRequest;
  }
}
