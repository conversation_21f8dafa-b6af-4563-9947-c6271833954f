import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RestaurantAccessService } from './restaurant-access.service';

@Global() // Make services available globally
@Module({
  imports: [TypeOrmModule.forFeature([Restaurant])],
  providers: [RestaurantAccessService],
  exports: [RestaurantAccessService],
})
export class RestaurantAccessModule {}
