import { <PERSON>, Get, Param, ParseUUIDPipe, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Roles } from '../../auth/decorators/roles.decorator';
import { UserType } from '../../auth/enums/user-type.enum';
import { ListUserDto } from '../dtos/list-user.dto';
import { UsersService } from '../users.service';

@ApiTags('(Admin) Users')
@Controller('admin/users')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class UsersAdminController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  findAll(@Query() listUserDto: ListUserDto) {
    return this.usersService.findAll(listUserDto);
  }

  @Put('ban/:id')
  ban(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.ban(id);
  }

  @Put('unban/:id')
  unban(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.unban(id);
  }
}
