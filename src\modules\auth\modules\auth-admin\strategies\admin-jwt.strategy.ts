import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { AdminsService } from '@/modules/admins/admins.service';
import { AdminJwtInfo, AdminJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

@Injectable()
export class AdminJwtStrategy extends PassportStrategy(Strategy, 'admin-jwt') {
  constructor(
    private configService: ConfigService,
    private adminsService: AdminsService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        (request: Request) => {
          const accessToken = request?.cookies?.access_token;
          if (!accessToken) {
            return null;
          }
          return accessToken;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.adminJwtAccessSecret') as string,
    });
  }

  async validate(payload: AdminJwtPayload) {
    const user = await this.adminsService.findById(payload.sub);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    const userInfo: AdminJwtInfo = {
      id: payload.sub,
      email: user.email,
      userType: payload.userType,
      role: user.role,
    };

    return userInfo;
  }
}
