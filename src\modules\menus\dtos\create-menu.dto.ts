import { Type } from 'class-transformer';
import { Is<PERSON>rray, IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';

import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMenuDto {
  @ApiProperty({ description: 'name of the menu' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Array of menu section IDs with positions', required: false, type: [PositionItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemDto)
  menuSectionIds?: PositionItemDto[];
}
