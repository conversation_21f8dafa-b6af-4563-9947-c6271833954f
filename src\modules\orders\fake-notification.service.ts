import { BadRequestException, Injectable, Logger } from '@nestjs/common';

import { FCMService } from '../shared/fcm/fcm.service';
import { FcmRestaurantType, FcmUserType } from '../shared/fcm/fcm.types';
import { OrdersService } from './orders.service';

@Injectable()
export class FakeNotificationService {
  private readonly logger = new Logger(FakeNotificationService.name);

  constructor(
    private readonly ordersService: OrdersService,
    private readonly fcmService: FCMService,
  ) {}

  /**
   * Send fake notification (with enable check)
   */
  async sendFakeNotification(
    orderId: string,
    userNotificationType?: FcmUserType,
    staffNotificationType?: FcmRestaurantType,
    note?: string,
  ): Promise<{ message: string; details: any }> {
    this.logger.log(`Sending fake notification for order ${orderId}`, {
      orderId,
      userNotificationType,
      staffNotificationType,
      note,
    });

    return this.fakeSendNotification(orderId, userNotificationType, staffNotificationType, note);
  }

  /**
   * Get available notification types
   */
  getAvailableNotificationTypes(): {
    userTypes: typeof FcmUserType;
    staffTypes: typeof FcmRestaurantType;
  } {
    return {
      userTypes: FcmUserType,
      staffTypes: FcmRestaurantType,
    };
  }

  /**
   * Fake send notification for testing purposes
   */
  private async fakeSendNotification(
    orderId: string,
    userNotificationType?: FcmUserType,
    staffNotificationType?: FcmRestaurantType,
    note?: string,
  ): Promise<{ message: string; details: any }> {
    this.logger.log(`Fake sending notification for order ${orderId}`, {
      orderId,
      userNotificationType,
      staffNotificationType,
      note,
    });

    // Get order with relations for notification
    const order = await this.ordersService.findOne(orderId, { relations: true });

    const results: any = {
      orderId,
      sentNotifications: [],
      note,
    };

    // Send user notification if requested
    if (userNotificationType) {
      try {
        await this.fcmService.sendToUser(order, userNotificationType);
        results.sentNotifications.push({
          type: 'user',
          notificationType: userNotificationType,
          status: 'success',
        });
        this.logger.log(`Successfully sent user notification: ${userNotificationType} for order ${orderId}`);
      } catch (error) {
        results.sentNotifications.push({
          type: 'user',
          notificationType: userNotificationType,
          status: 'failed',
          error: error.message,
        });
        this.logger.error(`Failed to send user notification: ${userNotificationType} for order ${orderId}`, error);
      }
    }

    // Send staff notification if requested
    if (staffNotificationType) {
      try {
        await this.fcmService.sendToStaffWithRestaurantType(order, staffNotificationType);
        results.sentNotifications.push({
          type: 'staff',
          notificationType: staffNotificationType,
          status: 'success',
        });
        this.logger.log(`Successfully sent staff notification: ${staffNotificationType} for order ${orderId}`);
      } catch (error) {
        results.sentNotifications.push({
          type: 'staff',
          notificationType: staffNotificationType,
          status: 'failed',
          error: error.message,
        });
        this.logger.error(`Failed to send staff notification: ${staffNotificationType} for order ${orderId}`, error);
      }
    }

    if (!userNotificationType && !staffNotificationType) {
      throw new BadRequestException('At least one notification type (user or staff) must be provided');
    }

    return {
      message: 'Fake notification sending completed',
      details: results,
    };
  }
}
