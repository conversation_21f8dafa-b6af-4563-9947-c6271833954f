import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MappingMenuSectionMenuItem } from './entities/mapping-menu-section-menu-item.entity';
import { MenuSectionAvailableSchedule } from './entities/menu-section-available-schedule.entity';
import { MenuSection } from './entities/menu-section.entity';
import { MenuSectionsController } from './menu-sections.controller';
import { MenuSectionsService } from './menu-sections.service';

@Module({
  imports: [TypeOrmModule.forFeature([MenuSection, MappingMenuSectionMenuItem, MenuSectionAvailableSchedule])],
  controllers: [MenuSectionsController],
  providers: [MenuSectionsService],
  exports: [MenuSectionsService],
})
export class MenuSectionsModule {}
