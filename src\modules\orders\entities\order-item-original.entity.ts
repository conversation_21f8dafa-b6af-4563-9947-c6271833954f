import { Exclude } from 'class-transformer';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';

import { OrderItemOptionOriginal } from './order-item-option-original.entity';
import { Order } from './order.entity';

@Entity('order_items_original')
export class OrderItemOriginal extends BaseEntity {
  @Column({ name: 'order_id', type: 'uuid' })
  orderId: string;

  @Column({ name: 'menu_item_id', type: 'uuid' })
  menuItemId: string;

  @Column({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @Column({ type: 'integer' })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  price: number;

  @Column({
    name: 'tax_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  taxAmount: number;

  @Column({ name: 'note', nullable: true, type: 'text' })
  note?: string | null;

  @Column({ name: 'menu_section_name', type: 'varchar' })
  menuSectionName: string;

  @Exclude()
  @Column({ name: 'menu_section_name_en', type: 'varchar', nullable: true })
  menuSectionNameEn?: string | null;

  @Exclude()
  @Column({ name: 'menu_section_name_vi', type: 'varchar', nullable: true })
  menuSectionNameVi?: string | null;

  @Column({ name: 'menu_item_name', type: 'varchar' })
  menuItemName: string;

  @Exclude()
  @Column({ name: 'menu_item_name_en', type: 'varchar', nullable: true })
  menuItemNameEn?: string | null;

  @Exclude()
  @Column({ name: 'menu_item_name_vi', type: 'varchar', nullable: true })
  menuItemNameVi?: string | null;

  @Column({ name: 'menu_item_description', nullable: true, type: 'text' })
  menuItemDescription?: string | null;

  @Exclude()
  @Column({ name: 'menu_item_description_en', nullable: true, type: 'text' })
  menuItemDescriptionEn?: string | null;

  @Exclude()
  @Column({ name: 'menu_item_description_vi', nullable: true, type: 'text' })
  menuItemDescriptionVi?: string | null;

  @Column({ name: 'menu_item_image_urls', type: 'json', default: [] })
  menuItemImageUrls: string[];

  @Column({ name: 'is_alcohol', type: 'boolean', default: false })
  isAlcohol: boolean;

  @ManyToOne(() => Order, (order) => order.orderItemsOriginal, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: WrapperType<Order>;

  @OneToMany(() => OrderItemOptionOriginal, (orderItemOptionOriginal) => orderItemOptionOriginal.orderItemOriginal)
  orderItemOptionsOriginal: WrapperType<OrderItemOptionOriginal>[];
}
