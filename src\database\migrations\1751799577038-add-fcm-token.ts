import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFcmToken1751799577038 implements MigrationInterface {
  name = 'AddFcmToken1751799577038';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_fcm_tokens" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "token" character varying NOT NULL, "device_id" character varying NOT NULL, "platform" character varying NOT NULL, CONSTRAINT "PK_f8088ed7e1116e01a4033b6ca76" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_9acd2767cf71e4107b4dfd3fcb" ON "user_fcm_tokens" ("token") `);
    await queryRunner.query(`CREATE INDEX "IDX_869ca568c4ec52322f1681b1a3" ON "user_fcm_tokens" ("user_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_26a59cc28bd07ff6b4be48cab0" ON "user_fcm_tokens" ("user_id", "device_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "staff_fcm_tokens" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_staff_id" uuid NOT NULL, "token" character varying NOT NULL, "device_id" character varying NOT NULL, "platform" character varying NOT NULL, CONSTRAINT "PK_378c1adf667b18d724cffa2c518" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a02030a25506b8e2b1d21ad0a2" ON "staff_fcm_tokens" ("token") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_f352983724f663a30afcf73c88" ON "staff_fcm_tokens" ("merchant_staff_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_54ee992f73d8a228210f240a11" ON "staff_fcm_tokens" ("merchant_staff_id", "device_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_fcm_tokens" ADD CONSTRAINT "FK_869ca568c4ec52322f1681b1a3f" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "staff_fcm_tokens" ADD CONSTRAINT "FK_f352983724f663a30afcf73c88e" FOREIGN KEY ("merchant_staff_id") REFERENCES "merchant_staff"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "staff_fcm_tokens" DROP CONSTRAINT "FK_f352983724f663a30afcf73c88e"`);
    await queryRunner.query(`ALTER TABLE "user_fcm_tokens" DROP CONSTRAINT "FK_869ca568c4ec52322f1681b1a3f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_54ee992f73d8a228210f240a11"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f352983724f663a30afcf73c88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a02030a25506b8e2b1d21ad0a2"`);
    await queryRunner.query(`DROP TABLE "staff_fcm_tokens"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_26a59cc28bd07ff6b4be48cab0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_869ca568c4ec52322f1681b1a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9acd2767cf71e4107b4dfd3fcb"`);
    await queryRunner.query(`DROP TABLE "user_fcm_tokens"`);
  }
}
