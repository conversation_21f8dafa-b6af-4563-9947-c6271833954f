import { Type } from 'class-transformer';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString, IsUUID, ValidateNested } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class OptionWithAmountItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  @IsPositive()
  amount?: number;
}
export class CartItemGroupOptionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  optionGroupId: string;

  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OptionWithAmountItemDto)
  options: OptionWithAmountItemDto[];
}

export class AddToCartDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  menuSectionId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  menuItemId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @IsPositive()
  amount: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    example: [
      {
        optionGroupId: 'UUID',
        options: [{ id: 'UUID', amount: 1 }],
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemGroupOptionDto)
  groupOptions: CartItemGroupOptionDto[];
}
