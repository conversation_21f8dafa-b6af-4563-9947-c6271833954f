import { SocketEventPattern } from '@/common/enums/event-pattern.enum';
import { ChatEventNewMessagePayload, ChatEventStatusUpdatePayload } from '@/modules/chat/chat.types';
import { Order } from '@/modules/orders/entities/order.entity';
import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';

@Injectable()
export class MicroserviceClientService implements OnModuleInit, OnModuleDestroy {
  private client: ClientProxy;
  private readonly logger = new Logger(MicroserviceClientService.name);

  constructor(private readonly configService: ConfigService) {
    this.client = ClientProxyFactory.create({
      transport: Transport.REDIS,
      options: {
        host: this.configService.get<string>('redis.host', 'localhost'),
        port: this.configService.get<number>('redis.port', 6379),
        password: this.configService.get<string>('redis.password'),
        retryAttempts: 5,
        retryDelay: 3000,
      },
    });
  }

  async onModuleInit() {
    try {
      await this.client.connect();
      this.logger.log('✅ Microservice client connected to socket server');
    } catch (error) {
      this.logger.error('❌ Failed to connect microservice client to socket server:', error);
    }
  }

  async onModuleDestroy() {
    await this.client.close();
    this.logger.log('🔌 Microservice client disconnected');
  }

  async publishNewMessage(payload: ChatEventNewMessagePayload) {
    try {
      await this.client.emit(SocketEventPattern.CHAT_NEW_MESSAGE, payload);
      this.logger.log(`New message published for ${payload.orderId}`);
    } catch (error) {
      this.logger.error(`Failed to publish new message for ${payload.orderId}:`, error);
    }
  }

  async publishMessageStatusUpdate(payload: ChatEventStatusUpdatePayload) {
    try {
      await this.client.emit(SocketEventPattern.CHAT_MESSAGE_STATUS_UPDATE, payload);
      this.logger.log(`Message status update published for ${payload.userId} - ${payload.restaurantId}`);
    } catch (error) {
      this.logger.error(`Failed to publish message status update for ${payload.userId}:`, error);
    }
  }

  async publishNewOrder(order: Order) {
    try {
      await this.client.emit(SocketEventPattern.ORDER_NEW, order);
      this.logger.log(`New order published for order ${order.id}`);
    } catch (error) {
      this.logger.error(`Failed to publish new order for order ${order.id}:`, error);
    }
  }

  async publishOrderStatusUpdate(order: Order) {
    try {
      await this.client.emit(SocketEventPattern.ORDER_STATUS_UPDATE, order);
      this.logger.log(`Order status update published for order ${order.id}`);
    } catch (error) {
      this.logger.error(`Failed to publish order status update for order ${order.id}:`, error);
    }
  }
}
