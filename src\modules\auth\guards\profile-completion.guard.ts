import { isNil } from 'lodash';

import { UserAddressesService } from '@/modules/user-addresses/user-addresses.service';
import { UserType } from '@auth/enums/user-type.enum';
import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { UserJwtInfo } from '../types/jwt-payload.type';

export const SKIP_PROFILE_CHECK = 'skipProfileCheck';

@Injectable()
export class ProfileCompletionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private userAddressesService: UserAddressesService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If the route is public, allow access without checking roles
    if (isPublic) {
      return true;
    }

    // Check if profile check should be skipped for this route
    const skipProfileCheck = this.reflector.getAllAndOverride<boolean>(SKIP_PROFILE_CHECK, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipProfileCheck) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as UserJwtInfo;

    // Only apply to USER type
    if (!user || user.userType !== UserType.USER) return true;

    let isComplete = false;

    if (!isNil(user.firstName) && !isNil(user.lastName)) {
      if (user.hasAddress) return true;
      isComplete = await this.userAddressesService.hasAnyAddress(user.id);
    }
    if (isComplete) return true;

    throw new ForbiddenException(
      'Profile not complete. Please update your first name, last name, and add at least one address.',
    );
  }
}
