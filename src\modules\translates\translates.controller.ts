import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { GetTranslatesDto } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';
import { TranslateResponseItem, TranslatesService } from './translates.service';

@ApiTags('Translates')
@Controller('translates')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class TranslatesController {
  constructor(private readonly translatesService: TranslatesService) {}

  @Get()
  async getTranslates(
    @Query() dto: GetTranslatesDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<TranslateResponseItem[]> {
    return this.translatesService.getTranslates(dto, ownerId);
  }

  @Put()
  async updateTranslates(
    @Body() dto: UpdateTranslatesDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<{ updated: number }> {
    return this.translatesService.updateTranslates(dto, ownerId);
  }
}
