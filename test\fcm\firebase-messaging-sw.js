// Import Firebase scripts for service worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Initialize Firebase in service worker
// You need to replace this with your actual Firebase config
const firebaseConfig = {
  // Add your Firebase config here
  // This will be set dynamically from the main page
};

// This will be overridden by the main page
let isFirebaseInitialized = false;

// Listen for messages from main thread to initialize Firebase
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'FIREBASE_CONFIG') {
    try {
      if (!isFirebaseInitialized) {
        firebase.initializeApp(event.data.config);
        const messaging = firebase.messaging();

        // Handle background messages
        messaging.onBackgroundMessage((payload) => {
          console.log('Background message received:', payload);

          const notificationTitle = payload.notification?.title || 'New Message';
          const notificationOptions = {
            body: payload.notification?.body || 'You have a new message',
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            data: payload.data || {},
            tag: 'fcm-notification',
            requireInteraction: true,
            actions: [
              {
                action: 'open',
                title: 'Mở',
                icon: '/favicon.ico',
              },
              {
                action: 'close',
                title: 'Đóng',
              },
            ],
          };

          return self.registration.showNotification(notificationTitle, notificationOptions);
        });

        isFirebaseInitialized = true;
        console.log('Firebase initialized in service worker');
      }
    } catch (error) {
      console.error('Error initializing Firebase in service worker:', error);
    }
  }
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);

  event.notification.close();

  if (event.action === 'open') {
    // Open the app
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
        // If app is already open, focus it
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus();
          }
        }
        // If app is not open, open it
        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      }),
    );
  } else if (event.action === 'close') {
    // Just close the notification (already done above)
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus();
          }
        }
        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      }),
    );
  }
});

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('Service worker installing...');
  self.skipWaiting();
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('Service worker activating...');
  event.waitUntil(self.clients.claim());
});

// Handle push events (fallback)
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);

  if (event.data) {
    try {
      const payload = event.data.json();
      const notificationTitle = payload.notification?.title || 'New Message';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new message',
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        data: payload.data || {},
        tag: 'push-notification',
      };

      event.waitUntil(self.registration.showNotification(notificationTitle, notificationOptions));
    } catch (error) {
      console.error('Error handling push event:', error);
    }
  }
});
