import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUniqueNameMenuFlow1751053118802 implements MigrationInterface {
  name = 'UpdateUniqueNameMenuFlow1751053118802';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f5eb402178e3fcc3ea22068227" ON "menu_item_option_groups" ("published_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_eb588e8f968bbcfbaae4b3d63d" ON "menu_item_option_groups" ("internal_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d90994382af186e5937d102895" ON "menu_item_options" ("published_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_64da78d7c52db2437c6eea2cc0" ON "menu_item_options" ("internal_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_2471a73dbf3c3cf33fcd459399" ON "ingredients" ("published_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_9d9cb5f2066f965e3918963e0b" ON "ingredients" ("internal_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_20c5c2f0c5c2607c7ac153360b" ON "menu_items" ("published_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d73cc5700ca606ea3de3ad15a0" ON "menu_items" ("internal_name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d1b81b03438d5b22fb3c63b0b9" ON "menu_sections" ("restaurant_id", "internal_name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_364009fce2f3b1b3a119cb3417" ON "menus" ("name", "restaurant_id") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_364009fce2f3b1b3a119cb3417"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d1b81b03438d5b22fb3c63b0b9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d73cc5700ca606ea3de3ad15a0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_20c5c2f0c5c2607c7ac153360b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9d9cb5f2066f965e3918963e0b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2471a73dbf3c3cf33fcd459399"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_64da78d7c52db2437c6eea2cc0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d90994382af186e5937d102895"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eb588e8f968bbcfbaae4b3d63d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f5eb402178e3fcc3ea22068227"`);
  }
}
