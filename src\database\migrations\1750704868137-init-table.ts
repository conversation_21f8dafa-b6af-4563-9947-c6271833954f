import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitTable1750704868137 implements MigrationInterface {
  name = 'InitTable1750704868137';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "users" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "phone" character varying, "phoneCountryCode" character varying DEFAULT '+84', "firstName" character varying, "lastName" character varying, "banned" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_b383987bfa6e6a8745085621d0" ON "users" ("email") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_05d78f8eb36ab8fe8c1110d6b7" ON "users" ("phone") WHERE deleted_at IS NULL AND phone IS NOT NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_2aac7de7c15294c7f90a51d065" ON "users" ("phoneCountryCode") `);
    await queryRunner.query(
      `CREATE TABLE "user_payment_cards" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "token_number" character varying(16) NOT NULL, "token_expiry" character varying(10) NOT NULL, "masked_card_number" character varying(20), "card_bank" character varying(16), "card_uid" character varying(64) NOT NULL, "is_default" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_e527f1d31ae194f169aeeebaa5c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_064c0b8959badfd4f4a142a6df" ON "user_payment_cards" ("user_id", "card_uid") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "user-addresses" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "latitude" numeric(10,8) NOT NULL, "longitude" numeric(11,8) NOT NULL, "full_address" text NOT NULL, "street_number" character varying, "street_name" character varying, "city" character varying, "state" character varying, "country" character varying, "postal_code" character varying, "address_type" character varying NOT NULL, "address_label" character varying NOT NULL, "is_default" boolean NOT NULL DEFAULT false, "is_temporary" boolean NOT NULL DEFAULT false, "place_id" character varying, CONSTRAINT "PK_4baf15ec3d396aaa05573d74927" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b39d7c4712fd9a0739e034919f" ON "user-addresses" ("user_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_17e9968c3e3863f8308c0cefce" ON "user-addresses" ("user_id", "is_temporary") WHERE is_temporary = true AND deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_61f1d453ca3c6f56322e44104a" ON "user-addresses" ("latitude", "longitude") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a954cc97827e9ec3a22a923169" ON "user-addresses" ("user_id", "is_temporary") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5b1afc35ebbc50959dab3aac6e" ON "user-addresses" ("user_id", "is_default") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."merchant_users_role_enum" AS ENUM('super-admin', 'admin', 'member')`,
    );
    await queryRunner.query(
      `CREATE TABLE "merchant_users" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "password" character varying NOT NULL, "role" "public"."merchant_users_role_enum" NOT NULL DEFAULT 'member', "active_at" TIMESTAMP WITH TIME ZONE, "banned" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_371c70a194efc8b4c1214aff6a8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6bab52b65c68153b846c9e0794" ON "merchant_users" ("email") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_6be527e7fe4a095222ee917cdf" ON "merchant_users" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "merchant_accounts" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "owner_merchant_user_id" uuid, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_27854f4b432bf52511837d67830" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b132a6f2a24dabfce5bd3e8d5d" ON "merchant_accounts" ("owner_merchant_user_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_359b53d73273490ee0f0afda47" ON "merchant_accounts" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "brands" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "merchant_account_id" uuid NOT NULL, "code" character varying NOT NULL, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_b0c437120b624da1034a81fc561" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6cc477dec8a7129e878154ae27" ON "brands" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_51cda18866e732ce9138ee3e01" ON "brands" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "mapping_menu_items_menu_item_option_groups" ("menu_item_id" uuid NOT NULL, "menu_item_option_group_id" uuid NOT NULL, "position" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_1a2f0be0e461d8b5f5587a659c8" PRIMARY KEY ("menu_item_id", "menu_item_option_group_id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."menu_item_option_groups_rule_enum" AS ENUM('option', 'option_max', 'mandatory_fixed', 'mandatory_range')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."menu_item_option_groups_type_enum" AS ENUM('item_customization', 'add_on_item')`,
    );
    await queryRunner.query(
      `CREATE TABLE "menu_item_option_groups" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "internal_name" character varying NOT NULL, "published_name" character varying NOT NULL, "rule" "public"."menu_item_option_groups_rule_enum" NOT NULL DEFAULT 'option', "type" "public"."menu_item_option_groups_type_enum" NOT NULL DEFAULT 'item_customization', "max_amount_of_option" integer NOT NULL DEFAULT '1', "max_amount" integer, "fixed_amount" integer, "from_amount" integer, "to_amount" integer, "brand_id" uuid NOT NULL, CONSTRAINT "PK_9cdaf73bd4c150b053998044c2b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b997d8accd6b14e574ab0319fb" ON "menu_item_option_groups" ("brand_id") `);
    await queryRunner.query(
      `CREATE TABLE "mapping_menu_item_option_groups_menu_item_options" ("menu_item_option_group_id" uuid NOT NULL, "menu_item_option_id" uuid NOT NULL, "position" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_6022cbb5c0346c60c52908f4500" PRIMARY KEY ("menu_item_option_group_id", "menu_item_option_id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "menu_item_options" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "internal_name" character varying NOT NULL, "published_name" character varying NOT NULL, "description" character varying, "base_price" numeric(10,2) NOT NULL, "type" character varying NOT NULL DEFAULT 'option', "brand_id" uuid NOT NULL, "schedule_active_at" TIMESTAMP WITH TIME ZONE, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_5f9cc4a2480757f075354302fdb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_34bd012d009a5a3d880c56c027" ON "menu_item_options" ("brand_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_a694eccaf5bad134221a2a4a42" ON "menu_item_options" ("schedule_active_at") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c7be35c1633414224f218e4bdd" ON "menu_item_options" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "ingredients" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "internal_name" character varying NOT NULL, "published_name" character varying NOT NULL, "brand_id" uuid NOT NULL, CONSTRAINT "PK_9240185c8a5507251c9f15e0649" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_8928c5b2ca8da66d5bcce12eb0" ON "ingredients" ("brand_id") `);
    await queryRunner.query(
      `CREATE TABLE "menu_items" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "internal_name" character varying NOT NULL, "published_name" character varying NOT NULL, "description" character varying, "base_price" numeric(10,2) NOT NULL, "type" character varying NOT NULL DEFAULT 'item', "image_urls" json NOT NULL DEFAULT '[]', "brand_id" uuid NOT NULL, "schedule_active_at" TIMESTAMP WITH TIME ZONE, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_57e6188f929e5dc6919168620c8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b9ec5028095e6f37875372bccd" ON "menu_items" ("published_name") `);
    await queryRunner.query(`CREATE INDEX "IDX_2d2e48df4ccb4860ffbe667807" ON "menu_items" ("brand_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_0e7c37bcce8087e08311f7ffa5" ON "menu_items" ("schedule_active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_ca6f3ba1951e27c19e27c229f8" ON "menu_items" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "mapping_menu_sections_menu_items" ("menu_section_id" uuid NOT NULL, "menu_item_id" uuid NOT NULL, "position" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_d772f51fb2483cf23ce1e8defbc" PRIMARY KEY ("menu_section_id", "menu_item_id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "menu_section_available_schedules" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "menu_section_id" uuid NOT NULL, "day" integer NOT NULL, "start" TIME, "end" TIME, "is_all_day" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_486aeb460e57af2adf15b88f376" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_806e6b8c727b7b60558b966df4" ON "menu_section_available_schedules" ("menu_section_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."menu_sections_view_type_enum" AS ENUM('slide', 'grid', 'spotlight')`,
    );
    await queryRunner.query(
      `CREATE TABLE "menu_sections" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "internal_name" character varying NOT NULL, "published_name" character varying NOT NULL, "view_type" "public"."menu_sections_view_type_enum" NOT NULL DEFAULT 'grid', "schedule_active_at" TIMESTAMP WITH TIME ZONE, "active_at" TIMESTAMP WITH TIME ZONE, "brand_id" uuid NOT NULL, CONSTRAINT "PK_d9f9554edc03170cba756fecdb9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a4b2bf594216fc1e0a570dd02a" ON "menu_sections" ("schedule_active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_216807fa8aeb465c9715e2d1cd" ON "menu_sections" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_acee03cf5662366ec3b3aae98b" ON "menu_sections" ("brand_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_1a5653ff049df3e9cb6c54d06c" ON "menu_sections" ("brand_id", "published_name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "mapping_menus_menu_sections" ("menu_id" uuid NOT NULL, "menu_section_id" uuid NOT NULL, "position" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_4ac87ba59da21fc54f371417784" PRIMARY KEY ("menu_id", "menu_section_id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "menus" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "restaurant_id" uuid NOT NULL, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_3fec3d93327f4538e0cbd4349c4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_bcd4a935c967cc9c20e770d1e6" ON "menus" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_6b878682f9c53cbfe804ae288c" ON "menus" ("active_at") `);
    await queryRunner.query(
      `CREATE TABLE "restaurant_tags" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, CONSTRAINT "PK_b3e871c4b8c697242f0a5a2dd9b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "restaurants" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "code" character varying NOT NULL, "avatar_img" character varying NOT NULL, "background_img" character varying NOT NULL, "price_range" character varying, "star_rated" numeric(3,1) NOT NULL DEFAULT '0', "brand_id" uuid NOT NULL, "active_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_e2133a72eb1cc8f588f7b503e68" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_dfeffbef9c31936dbac54733da" ON "restaurants" ("name") `);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7d88c646d0b073a0f291e9ab34" ON "restaurants" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_5af4db15acb151b8ebc046a6b7" ON "restaurants" ("price_range") `);
    await queryRunner.query(`CREATE INDEX "IDX_f46069b19d64ec43ff6b24d390" ON "restaurants" ("star_rated") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_4ff0af8a480bd8ad020ea03123" ON "restaurants" ("brand_id", "active_at") `,
    );
    await queryRunner.query(
      `CREATE TABLE "order_customers" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "phone" character varying NOT NULL, "email" character varying, "latitude" numeric(10,8) NOT NULL, "longitude" numeric(11,8) NOT NULL, "full_address" text NOT NULL, "street_number" character varying, "street_name" character varying, "city" character varying, "state" character varying, "country" character varying, "postal_code" character varying, "address_type" character varying NOT NULL, "address_label" character varying NOT NULL, "place_id" character varying, CONSTRAINT "PK_4d316591eaccef8eb6cd42c96fc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_2db8ce89554cded69dd8f0fa88" ON "order_customers" ("name", "phone") `);
    await queryRunner.query(
      `CREATE TABLE "order_item_options_original" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_item_original_id" uuid NOT NULL, "menu_item_option_group_id" uuid NOT NULL, "menu_item_option_id" uuid NOT NULL, "amount" integer NOT NULL DEFAULT '1', "price" numeric(10,2) NOT NULL, "option_group_name" character varying NOT NULL, "option_name" character varying NOT NULL, CONSTRAINT "UQ_order_item_option_original" UNIQUE ("order_item_original_id", "menu_item_option_group_id", "menu_item_option_id"), CONSTRAINT "PK_2afa8f3edfd84172547f225a8b7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "order_items_original" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_id" uuid NOT NULL, "menu_item_id" uuid NOT NULL, "amount" integer NOT NULL, "price" numeric(10,2) NOT NULL, "note" text, "menu_item_name" character varying NOT NULL, "menu_item_description" text, "menu_item_image_urls" json NOT NULL DEFAULT '[]', CONSTRAINT "PK_08e013c51d9e5f57a9849fee17b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."merchant_staff_role_enum" AS ENUM('branch-manager', 'staff')`);
    await queryRunner.query(
      `CREATE TABLE "merchant_staff" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "username" character varying NOT NULL, "password" character varying NOT NULL, "role" "public"."merchant_staff_role_enum" NOT NULL DEFAULT 'staff', "banned" boolean NOT NULL DEFAULT false, "restaurant_id" uuid NOT NULL, CONSTRAINT "PK_5bdc684c5552616938a685883e0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ad2ea66f0cc82a2969821a8998" ON "merchant_staff" ("username") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c134d148ae4c3ce4d094c68db3" ON "merchant_staff" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE TABLE "order_item_options" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_item_id" uuid NOT NULL, "menu_item_option_group_id" uuid NOT NULL, "menu_item_option_id" uuid NOT NULL, "amount" integer NOT NULL DEFAULT '1', "price" numeric(10,2) NOT NULL, "option_group_name" character varying NOT NULL, "option_name" character varying NOT NULL, CONSTRAINT "UQ_order_item_option" UNIQUE ("order_item_id", "menu_item_option_group_id", "menu_item_option_id"), CONSTRAINT "PK_8279922d5b99fccbeb617096227" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "order_items" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_id" uuid NOT NULL, "menu_item_id" uuid NOT NULL, "amount" integer NOT NULL, "price" numeric(10,2) NOT NULL, "note" text, "menu_item_name" character varying NOT NULL, "menu_item_description" text, "menu_item_image_urls" json NOT NULL DEFAULT '[]', "modified_at" TIMESTAMP WITH TIME ZONE, "modified_by_id" uuid, CONSTRAINT "PK_005269d8574e6fac0493715c308" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "orders" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, "cart_id" uuid NOT NULL, "order_code" character varying NOT NULL, "order_sequence_number" integer NOT NULL DEFAULT '0', "status" character varying NOT NULL DEFAULT 'new', "payment_method" character varying NOT NULL DEFAULT 'cash', "payment_card_id" uuid, "payment_status" character varying, "include_utensils" boolean NOT NULL DEFAULT false, "subtotal" numeric(12,2) NOT NULL DEFAULT '0', "tax_amount" numeric(10,2) NOT NULL DEFAULT '0', "shipping_fee" numeric(10,2) NOT NULL DEFAULT '0', "order_customer_id" uuid NOT NULL, "discount" numeric(10,2) NOT NULL DEFAULT '0', "total" numeric(12,2) NOT NULL DEFAULT '0', "note" text, "restaurant_name" character varying NOT NULL, "rejection_note" text, "unfulfilled_at" TIMESTAMP WITH TIME ZONE, "modified_at" TIMESTAMP WITH TIME ZONE, "accepted_at" TIMESTAMP WITH TIME ZONE, "in_kitchen_at" TIMESTAMP WITH TIME ZONE, "kitchen_eta" TIMESTAMP WITH TIME ZONE, "completed_at" TIMESTAMP WITH TIME ZONE, "cancelled_at" TIMESTAMP WITH TIME ZONE, "rejection_reason" character varying, "cancel_reason" character varying, "modification_deadline" TIMESTAMP WITH TIME ZONE, "in_delivery_at" TIMESTAMP WITH TIME ZONE, "delivery_from_eta" TIMESTAMP WITH TIME ZONE, "delivery_to_eta" TIMESTAMP WITH TIME ZONE, "archived_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "REL_396137b2e49b4f34ec6b6e791f" UNIQUE ("order_customer_id"), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6390b11fc02ce94c39de62e304" ON "orders" ("cart_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_9c91320bb5a133c981d1cd8471" ON "orders" ("order_code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_892cef7cd3962597f0a1c00679" ON "orders" ("payment_status") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_7adfabf9e92e69bb0e1f83c439" ON "orders" ("restaurant_id", "order_sequence_number") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e9b23b2023b27cbd08de3b706a" ON "orders" ("restaurant_id", "status", "updated_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c67df01f06b0bba0fc0c94560" ON "orders" ("user_id", "status", "updated_at") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_0379c1cb8cd158363b657fd849" ON "orders" ("user_id", "restaurant_id") `);
    await queryRunner.query(
      `CREATE TABLE "onepay_void_requests" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "org_merch_txn_ref" character varying NOT NULL, "merch_txn_ref" character varying NOT NULL, "amount" integer NOT NULL, "operator" character varying NOT NULL, "type" character varying NOT NULL, CONSTRAINT "PK_0d7bcaaa1a738785b415d45b529" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f29cf2ae57bfdb2823174576ed" ON "onepay_void_requests" ("merch_txn_ref") `,
    );
    await queryRunner.query(
      `CREATE TABLE "onepay_create_token_requests" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "currency" character varying NOT NULL, "locale" character varying NOT NULL, "return_url" character varying NOT NULL, "merch_txn_ref" character varying NOT NULL, "order_info" character varying NOT NULL, "amount" integer NOT NULL, "ticket_no" character varying NOT NULL, "again_link" character varying NOT NULL, "title" character varying NOT NULL, "is_refunded" boolean NOT NULL DEFAULT false, "retry_count" integer NOT NULL DEFAULT '0', "dr_exists" boolean NOT NULL DEFAULT true, "txn_response_code" character varying, CONSTRAINT "PK_e629ff61572ec0bd9b87bf021b8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c0cd807cd918bd558cc62e6b23" ON "onepay_create_token_requests" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_4c3a39442f6a176783250ecd1e" ON "onepay_create_token_requests" ("merch_txn_ref") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0285b10a8f28dfca7c151dc560" ON "onepay_create_token_requests" ("dr_exists") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c843fb0876f77a674a6d1b616" ON "onepay_create_token_requests" ("txn_response_code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_29838d741c11c305b7d9a94433" ON "onepay_create_token_requests" ("txn_response_code", "is_refunded", "retry_count") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_09ceef583125688ec741ddcd50" ON "onepay_create_token_requests" ("updated_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e8232648e5a62bb1fcee28cb1a" ON "onepay_create_token_requests" ("updated_at", "txn_response_code", "dr_exists") `,
    );
    await queryRunner.query(
      `CREATE TABLE "onepay_order_requests" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "order_id" uuid NOT NULL, "order_code" character varying NOT NULL, "restaurant_id" uuid NOT NULL, "currency" character varying NOT NULL, "locale" character varying NOT NULL, "merch_txn_ref" character varying NOT NULL, "order_info" character varying NOT NULL, "amount" integer NOT NULL, "ticket_no" character varying NOT NULL, "is_refunded" boolean NOT NULL DEFAULT false, "dr_exists" boolean NOT NULL DEFAULT true, "txn_response_code" character varying, CONSTRAINT "PK_4944aa1e42d4af2871c4305219a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_737f31011da2b353b59c964a6d" ON "onepay_order_requests" ("user_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e8854997a9ca31fed3d2db3c3f" ON "onepay_order_requests" ("order_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c7290a51d662471959fd09a5b0" ON "onepay_order_requests" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6371c678ba78d1240296ed4bd1" ON "onepay_order_requests" ("merch_txn_ref") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d4b322024da0b678ae8ab161a3" ON "onepay_order_requests" ("is_refunded") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_bef84e6cb0e1aaba85edc36896" ON "onepay_order_requests" ("dr_exists") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_6b4075801b9e18b2dc315289f2" ON "onepay_order_requests" ("txn_response_code") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_28a1c8c94031b5f8d01c391eb8" ON "onepay_order_requests" ("updated_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_7833a02c6d70444b115d8d82a9" ON "onepay_order_requests" ("updated_at", "txn_response_code", "dr_exists") `,
    );
    await queryRunner.query(
      `CREATE TABLE "chat_conversations" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, "order_id" uuid NOT NULL, "last_message_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "last_message_content" text, "user_last_seen_at" TIMESTAMP WITH TIME ZONE, "restaurant_last_seen_at" TIMESTAMP WITH TIME ZONE, "unread_count_user" integer NOT NULL DEFAULT '0', "unread_count_restaurant" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_ff117d9f57807c4f2e3034a39f3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_7f9e5689ad3d7f45172fc156fb" ON "chat_conversations" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_2d0e434f89f68688e0845d6639" ON "chat_conversations" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_10a51d9c4befc0a1766e87c9d1" ON "chat_conversations" ("order_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE TYPE "public"."chat_messages_sender_type_enum" AS ENUM('user', 'restaurant')`);
    await queryRunner.query(`CREATE TYPE "public"."chat_messages_status_enum" AS ENUM('sent', 'delivered', 'seen')`);
    await queryRunner.query(
      `CREATE TABLE "chat_messages" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" SERIAL NOT NULL, "conversation_id" uuid NOT NULL, "sender_type" "public"."chat_messages_sender_type_enum" NOT NULL, "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, "merchant_user_id" uuid, "content" text NOT NULL, "message_type" character varying NOT NULL DEFAULT 'text', "status" "public"."chat_messages_status_enum" NOT NULL DEFAULT 'sent', "sent_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "delivered_at" TIMESTAMP WITH TIME ZONE, "seen_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_40c55ee0e571e268b0d3cd37d10" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_3d623662d4ee1219b23cf61e64" ON "chat_messages" ("conversation_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_ae763c9552786328a3d8002c93" ON "chat_messages" ("sender_type") `);
    await queryRunner.query(`CREATE INDEX "IDX_5588b6cea298cedec7063c0d33" ON "chat_messages" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_2b27bb087dddab938b07f09ac3" ON "chat_messages" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_56a790ed62e868f2e0c5f81115" ON "chat_messages" ("status") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_709afc71fa11ad33309070de99" ON "chat_messages" ("restaurant_id", "status") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_25c23b033c23996ea4b09449c5" ON "chat_messages" ("user_id", "status") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_bdfd8165e694fe9d3ce4140c87" ON "chat_messages" ("conversation_id", "created_at") `,
    );
    await queryRunner.query(
      `CREATE TABLE "cart_item_options" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "cart_item_id" uuid NOT NULL, "menu_item_option_group_id" uuid NOT NULL, "menu_item_option_id" uuid NOT NULL, "amount" integer NOT NULL DEFAULT '1', CONSTRAINT "UQ_cart_item_option" UNIQUE ("cart_item_id", "menu_item_option_group_id", "menu_item_option_id"), CONSTRAINT "PK_13cb7db70e5d02463da294726ba" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "cart_items" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "cart_id" uuid NOT NULL, "menu_item_id" uuid NOT NULL, "amount" integer NOT NULL, "note" text, CONSTRAINT "PK_6fccf5ec03c172d27a28a82928b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "carts" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "restaurant_id" uuid NOT NULL, "completed_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_b5f695a59f5ebb50af3c8160816" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_ad50548131c585e38d765bde16" ON "carts" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_2eed9e1a295de7a1b9fbe87ceb" ON "carts" ("user_id", "restaurant_id") WHERE deleted_at IS NULL AND completed_at IS NULL`,
    );
    await queryRunner.query(`CREATE TYPE "public"."admin_role_enum" AS ENUM('super-admin', 'admin', 'member')`);
    await queryRunner.query(
      `CREATE TABLE "admin" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "password" character varying NOT NULL, "name" character varying NOT NULL, "role" "public"."admin_role_enum" NOT NULL DEFAULT 'admin', "banned" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_e032310bcef831fb83101899b10" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ce74d50c917b0ae2b270d14a19" ON "admin" ("email") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "mapping_menu_items_ingredients" ("ingredient_id" uuid NOT NULL, "menu_item_id" uuid NOT NULL, CONSTRAINT "PK_a330af668a5eda46062e5d8bd6d" PRIMARY KEY ("ingredient_id", "menu_item_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_aa134d3ba7f70f2a99bbc69c04" ON "mapping_menu_items_ingredients" ("ingredient_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5712609ed5c959a242159ac1e5" ON "mapping_menu_items_ingredients" ("menu_item_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "mapping_menu_items_option_ingredients" ("ingredient_id" uuid NOT NULL, "menu_item_option_id" uuid NOT NULL, CONSTRAINT "PK_01ab90c0722b85ad476f4ef1274" PRIMARY KEY ("ingredient_id", "menu_item_option_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_052809b5ea16f8b626e6ac7d40" ON "mapping_menu_items_option_ingredients" ("ingredient_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9be18ee866be61bf8f0af144d8" ON "mapping_menu_items_option_ingredients" ("menu_item_option_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "mapping_restaurant_tags_restaurants" ("restaurant_id" uuid NOT NULL, "restaurant_tag_id" uuid NOT NULL, CONSTRAINT "PK_280bc0f3a5175781441b251b40f" PRIMARY KEY ("restaurant_id", "restaurant_tag_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_375355f5ebd3aa8ae3ac88f30e" ON "mapping_restaurant_tags_restaurants" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7ecde827c63244ccc6d7e993b2" ON "mapping_restaurant_tags_restaurants" ("restaurant_tag_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_accounts" ADD CONSTRAINT "FK_b132a6f2a24dabfce5bd3e8d5d5" FOREIGN KEY ("owner_merchant_user_id") REFERENCES "merchant_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "brands" ADD CONSTRAINT "FK_95fdae433a465fd3420d52c0e90" FOREIGN KEY ("merchant_account_id") REFERENCES "merchant_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_menu_item_option_groups" ADD CONSTRAINT "FK_ba1c60c5479cb4e99224d9c51be" FOREIGN KEY ("menu_item_id") REFERENCES "menu_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_menu_item_option_groups" ADD CONSTRAINT "FK_a3e12df0bfd4c9c19c110058c30" FOREIGN KEY ("menu_item_option_group_id") REFERENCES "menu_item_option_groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_option_groups" ADD CONSTRAINT "FK_b997d8accd6b14e574ab0319fb9" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" ADD CONSTRAINT "FK_8aef769c00278d1885c97453946" FOREIGN KEY ("menu_item_option_group_id") REFERENCES "menu_item_option_groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" ADD CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_options" ADD CONSTRAINT "FK_34bd012d009a5a3d880c56c027d" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "ingredients" ADD CONSTRAINT "FK_8928c5b2ca8da66d5bcce12eb09" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_items" ADD CONSTRAINT "FK_2d2e48df4ccb4860ffbe6678076" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_sections_menu_items" ADD CONSTRAINT "FK_2b16234bfb13137f0fe6a22482a" FOREIGN KEY ("menu_section_id") REFERENCES "menu_sections"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_sections_menu_items" ADD CONSTRAINT "FK_6f1feaa66899b1dab4d1f544363" FOREIGN KEY ("menu_item_id") REFERENCES "menu_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_section_available_schedules" ADD CONSTRAINT "FK_806e6b8c727b7b60558b966df42" FOREIGN KEY ("menu_section_id") REFERENCES "menu_sections"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_sections" ADD CONSTRAINT "FK_acee03cf5662366ec3b3aae98bc" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menus_menu_sections" ADD CONSTRAINT "FK_7b48e6f25f9f0fc09eab6ff75c5" FOREIGN KEY ("menu_id") REFERENCES "menus"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menus_menu_sections" ADD CONSTRAINT "FK_f9f651425f175f4a9a4e2bab41d" FOREIGN KEY ("menu_section_id") REFERENCES "menu_sections"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menus" ADD CONSTRAINT "FK_bcd4a935c967cc9c20e770d1e62" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "restaurants" ADD CONSTRAINT "FK_9b14b8271654d38e2bc8064f87d" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" ADD CONSTRAINT "FK_5cea9011c04f04654ee2ee026cd" FOREIGN KEY ("order_item_original_id") REFERENCES "order_items_original"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_items_original" ADD CONSTRAINT "FK_6a9c14e62125afd116066607e4a" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_staff" ADD CONSTRAINT "FK_c134d148ae4c3ce4d094c68db3e" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_item_options" ADD CONSTRAINT "FK_977c146e75da0ec110daed37f81" FOREIGN KEY ("order_item_id") REFERENCES "order_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_items" ADD CONSTRAINT "FK_bb7c33689e79e551096835fdbaa" FOREIGN KEY ("modified_by_id") REFERENCES "merchant_staff"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_items" ADD CONSTRAINT "FK_145532db85752b29c57d2b7b1f1" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD CONSTRAINT "FK_a922b820eeef29ac1c6800e826a" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD CONSTRAINT "FK_85fdda5fcce2f397ef8f117a2c6" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ADD CONSTRAINT "FK_396137b2e49b4f34ec6b6e791f9" FOREIGN KEY ("order_customer_id") REFERENCES "order_customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "onepay_create_token_requests" ADD CONSTRAINT "FK_c0cd807cd918bd558cc62e6b232" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "onepay_order_requests" ADD CONSTRAINT "FK_737f31011da2b353b59c964a6d8" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_conversations" ADD CONSTRAINT "FK_7f9e5689ad3d7f45172fc156fbf" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_conversations" ADD CONSTRAINT "FK_2d0e434f89f68688e0845d6639a" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_conversations" ADD CONSTRAINT "FK_8005fed9d3e452802a1c80ab9b8" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "chat_messages" ADD CONSTRAINT "FK_3d623662d4ee1219b23cf61e649" FOREIGN KEY ("conversation_id") REFERENCES "chat_conversations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "FK_1d43fff33753a7c91679fc2eb29" FOREIGN KEY ("cart_item_id") REFERENCES "cart_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "FK_1b15d23ed42b3b6cf363c833532" FOREIGN KEY ("menu_item_option_group_id") REFERENCES "menu_item_option_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_item_options" ADD CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_items" ADD CONSTRAINT "FK_6385a745d9e12a89b859bb25623" FOREIGN KEY ("cart_id") REFERENCES "carts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cart_items" ADD CONSTRAINT "FK_13e501a1cd1a6b1433ded345689" FOREIGN KEY ("menu_item_id") REFERENCES "menu_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "carts" ADD CONSTRAINT "FK_ad50548131c585e38d765bde166" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_ingredients" ADD CONSTRAINT "FK_aa134d3ba7f70f2a99bbc69c04f" FOREIGN KEY ("ingredient_id") REFERENCES "ingredients"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_ingredients" ADD CONSTRAINT "FK_5712609ed5c959a242159ac1e57" FOREIGN KEY ("menu_item_id") REFERENCES "menu_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" ADD CONSTRAINT "FK_052809b5ea16f8b626e6ac7d40b" FOREIGN KEY ("ingredient_id") REFERENCES "ingredients"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" ADD CONSTRAINT "FK_9be18ee866be61bf8f0af144d8c" FOREIGN KEY ("menu_item_option_id") REFERENCES "menu_item_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_tags_restaurants" ADD CONSTRAINT "FK_375355f5ebd3aa8ae3ac88f30eb" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_tags_restaurants" ADD CONSTRAINT "FK_7ecde827c63244ccc6d7e993b22" FOREIGN KEY ("restaurant_tag_id") REFERENCES "restaurant_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_tags_restaurants" DROP CONSTRAINT "FK_7ecde827c63244ccc6d7e993b22"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_restaurant_tags_restaurants" DROP CONSTRAINT "FK_375355f5ebd3aa8ae3ac88f30eb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" DROP CONSTRAINT "FK_9be18ee866be61bf8f0af144d8c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_option_ingredients" DROP CONSTRAINT "FK_052809b5ea16f8b626e6ac7d40b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_ingredients" DROP CONSTRAINT "FK_5712609ed5c959a242159ac1e57"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_ingredients" DROP CONSTRAINT "FK_aa134d3ba7f70f2a99bbc69c04f"`,
    );
    await queryRunner.query(`ALTER TABLE "carts" DROP CONSTRAINT "FK_ad50548131c585e38d765bde166"`);
    await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_13e501a1cd1a6b1433ded345689"`);
    await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_6385a745d9e12a89b859bb25623"`);
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "FK_0ee18573b466f57da9d6c52b9d0"`);
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "FK_1b15d23ed42b3b6cf363c833532"`);
    await queryRunner.query(`ALTER TABLE "cart_item_options" DROP CONSTRAINT "FK_1d43fff33753a7c91679fc2eb29"`);
    await queryRunner.query(`ALTER TABLE "chat_messages" DROP CONSTRAINT "FK_3d623662d4ee1219b23cf61e649"`);
    await queryRunner.query(`ALTER TABLE "chat_conversations" DROP CONSTRAINT "FK_8005fed9d3e452802a1c80ab9b8"`);
    await queryRunner.query(`ALTER TABLE "chat_conversations" DROP CONSTRAINT "FK_2d0e434f89f68688e0845d6639a"`);
    await queryRunner.query(`ALTER TABLE "chat_conversations" DROP CONSTRAINT "FK_7f9e5689ad3d7f45172fc156fbf"`);
    await queryRunner.query(`ALTER TABLE "onepay_order_requests" DROP CONSTRAINT "FK_737f31011da2b353b59c964a6d8"`);
    await queryRunner.query(
      `ALTER TABLE "onepay_create_token_requests" DROP CONSTRAINT "FK_c0cd807cd918bd558cc62e6b232"`,
    );
    await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_396137b2e49b4f34ec6b6e791f9"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_85fdda5fcce2f397ef8f117a2c6"`);
    await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_a922b820eeef29ac1c6800e826a"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_145532db85752b29c57d2b7b1f1"`);
    await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_bb7c33689e79e551096835fdbaa"`);
    await queryRunner.query(`ALTER TABLE "order_item_options" DROP CONSTRAINT "FK_977c146e75da0ec110daed37f81"`);
    await queryRunner.query(`ALTER TABLE "merchant_staff" DROP CONSTRAINT "FK_c134d148ae4c3ce4d094c68db3e"`);
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP CONSTRAINT "FK_6a9c14e62125afd116066607e4a"`);
    await queryRunner.query(
      `ALTER TABLE "order_item_options_original" DROP CONSTRAINT "FK_5cea9011c04f04654ee2ee026cd"`,
    );
    await queryRunner.query(`ALTER TABLE "restaurants" DROP CONSTRAINT "FK_9b14b8271654d38e2bc8064f87d"`);
    await queryRunner.query(`ALTER TABLE "menus" DROP CONSTRAINT "FK_bcd4a935c967cc9c20e770d1e62"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_menus_menu_sections" DROP CONSTRAINT "FK_f9f651425f175f4a9a4e2bab41d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menus_menu_sections" DROP CONSTRAINT "FK_7b48e6f25f9f0fc09eab6ff75c5"`,
    );
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP CONSTRAINT "FK_acee03cf5662366ec3b3aae98bc"`);
    await queryRunner.query(
      `ALTER TABLE "menu_section_available_schedules" DROP CONSTRAINT "FK_806e6b8c727b7b60558b966df42"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_sections_menu_items" DROP CONSTRAINT "FK_6f1feaa66899b1dab4d1f544363"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_sections_menu_items" DROP CONSTRAINT "FK_2b16234bfb13137f0fe6a22482a"`,
    );
    await queryRunner.query(`ALTER TABLE "menu_items" DROP CONSTRAINT "FK_2d2e48df4ccb4860ffbe6678076"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP CONSTRAINT "FK_8928c5b2ca8da66d5bcce12eb09"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP CONSTRAINT "FK_34bd012d009a5a3d880c56c027d"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" DROP CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" DROP CONSTRAINT "FK_8aef769c00278d1885c97453946"`,
    );
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP CONSTRAINT "FK_b997d8accd6b14e574ab0319fb9"`);
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_menu_item_option_groups" DROP CONSTRAINT "FK_a3e12df0bfd4c9c19c110058c30"`,
    );
    await queryRunner.query(
      `ALTER TABLE "mapping_menu_items_menu_item_option_groups" DROP CONSTRAINT "FK_ba1c60c5479cb4e99224d9c51be"`,
    );
    await queryRunner.query(`ALTER TABLE "brands" DROP CONSTRAINT "FK_95fdae433a465fd3420d52c0e90"`);
    await queryRunner.query(`ALTER TABLE "merchant_accounts" DROP CONSTRAINT "FK_b132a6f2a24dabfce5bd3e8d5d5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7ecde827c63244ccc6d7e993b2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_375355f5ebd3aa8ae3ac88f30e"`);
    await queryRunner.query(`DROP TABLE "mapping_restaurant_tags_restaurants"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9be18ee866be61bf8f0af144d8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_052809b5ea16f8b626e6ac7d40"`);
    await queryRunner.query(`DROP TABLE "mapping_menu_items_option_ingredients"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5712609ed5c959a242159ac1e5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_aa134d3ba7f70f2a99bbc69c04"`);
    await queryRunner.query(`DROP TABLE "mapping_menu_items_ingredients"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ce74d50c917b0ae2b270d14a19"`);
    await queryRunner.query(`DROP TABLE "admin"`);
    await queryRunner.query(`DROP TYPE "public"."admin_role_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2eed9e1a295de7a1b9fbe87ceb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ad50548131c585e38d765bde16"`);
    await queryRunner.query(`DROP TABLE "carts"`);
    await queryRunner.query(`DROP TABLE "cart_items"`);
    await queryRunner.query(`DROP TABLE "cart_item_options"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bdfd8165e694fe9d3ce4140c87"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_25c23b033c23996ea4b09449c5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_709afc71fa11ad33309070de99"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_56a790ed62e868f2e0c5f81115"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2b27bb087dddab938b07f09ac3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5588b6cea298cedec7063c0d33"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ae763c9552786328a3d8002c93"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3d623662d4ee1219b23cf61e64"`);
    await queryRunner.query(`DROP TABLE "chat_messages"`);
    await queryRunner.query(`DROP TYPE "public"."chat_messages_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."chat_messages_sender_type_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_10a51d9c4befc0a1766e87c9d1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d0e434f89f68688e0845d6639"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7f9e5689ad3d7f45172fc156fb"`);
    await queryRunner.query(`DROP TABLE "chat_conversations"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7833a02c6d70444b115d8d82a9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_28a1c8c94031b5f8d01c391eb8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6b4075801b9e18b2dc315289f2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bef84e6cb0e1aaba85edc36896"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d4b322024da0b678ae8ab161a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6371c678ba78d1240296ed4bd1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c7290a51d662471959fd09a5b0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e8854997a9ca31fed3d2db3c3f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_737f31011da2b353b59c964a6d"`);
    await queryRunner.query(`DROP TABLE "onepay_order_requests"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e8232648e5a62bb1fcee28cb1a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_09ceef583125688ec741ddcd50"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_29838d741c11c305b7d9a94433"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c843fb0876f77a674a6d1b616"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0285b10a8f28dfca7c151dc560"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c3a39442f6a176783250ecd1e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c0cd807cd918bd558cc62e6b23"`);
    await queryRunner.query(`DROP TABLE "onepay_create_token_requests"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f29cf2ae57bfdb2823174576ed"`);
    await queryRunner.query(`DROP TABLE "onepay_void_requests"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0379c1cb8cd158363b657fd849"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c67df01f06b0bba0fc0c94560"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e9b23b2023b27cbd08de3b706a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7adfabf9e92e69bb0e1f83c439"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_892cef7cd3962597f0a1c00679"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9c91320bb5a133c981d1cd8471"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6390b11fc02ce94c39de62e304"`);
    await queryRunner.query(`DROP TABLE "orders"`);
    await queryRunner.query(`DROP TABLE "order_items"`);
    await queryRunner.query(`DROP TABLE "order_item_options"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c134d148ae4c3ce4d094c68db3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ad2ea66f0cc82a2969821a8998"`);
    await queryRunner.query(`DROP TABLE "merchant_staff"`);
    await queryRunner.query(`DROP TYPE "public"."merchant_staff_role_enum"`);
    await queryRunner.query(`DROP TABLE "order_items_original"`);
    await queryRunner.query(`DROP TABLE "order_item_options_original"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2db8ce89554cded69dd8f0fa88"`);
    await queryRunner.query(`DROP TABLE "order_customers"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ff0af8a480bd8ad020ea03123"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f46069b19d64ec43ff6b24d390"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5af4db15acb151b8ebc046a6b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7d88c646d0b073a0f291e9ab34"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dfeffbef9c31936dbac54733da"`);
    await queryRunner.query(`DROP TABLE "restaurants"`);
    await queryRunner.query(`DROP TABLE "restaurant_tags"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6b878682f9c53cbfe804ae288c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bcd4a935c967cc9c20e770d1e6"`);
    await queryRunner.query(`DROP TABLE "menus"`);
    await queryRunner.query(`DROP TABLE "mapping_menus_menu_sections"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1a5653ff049df3e9cb6c54d06c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_acee03cf5662366ec3b3aae98b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_216807fa8aeb465c9715e2d1cd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a4b2bf594216fc1e0a570dd02a"`);
    await queryRunner.query(`DROP TABLE "menu_sections"`);
    await queryRunner.query(`DROP TYPE "public"."menu_sections_view_type_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_806e6b8c727b7b60558b966df4"`);
    await queryRunner.query(`DROP TABLE "menu_section_available_schedules"`);
    await queryRunner.query(`DROP TABLE "mapping_menu_sections_menu_items"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ca6f3ba1951e27c19e27c229f8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0e7c37bcce8087e08311f7ffa5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d2e48df4ccb4860ffbe667807"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b9ec5028095e6f37875372bccd"`);
    await queryRunner.query(`DROP TABLE "menu_items"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8928c5b2ca8da66d5bcce12eb0"`);
    await queryRunner.query(`DROP TABLE "ingredients"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c7be35c1633414224f218e4bdd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a694eccaf5bad134221a2a4a42"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_34bd012d009a5a3d880c56c027"`);
    await queryRunner.query(`DROP TABLE "menu_item_options"`);
    await queryRunner.query(`DROP TABLE "mapping_menu_item_option_groups_menu_item_options"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b997d8accd6b14e574ab0319fb"`);
    await queryRunner.query(`DROP TABLE "menu_item_option_groups"`);
    await queryRunner.query(`DROP TYPE "public"."menu_item_option_groups_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."menu_item_option_groups_rule_enum"`);
    await queryRunner.query(`DROP TABLE "mapping_menu_items_menu_item_option_groups"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_51cda18866e732ce9138ee3e01"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6cc477dec8a7129e878154ae27"`);
    await queryRunner.query(`DROP TABLE "brands"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_359b53d73273490ee0f0afda47"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b132a6f2a24dabfce5bd3e8d5d"`);
    await queryRunner.query(`DROP TABLE "merchant_accounts"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6be527e7fe4a095222ee917cdf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6bab52b65c68153b846c9e0794"`);
    await queryRunner.query(`DROP TABLE "merchant_users"`);
    await queryRunner.query(`DROP TYPE "public"."merchant_users_role_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5b1afc35ebbc50959dab3aac6e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a954cc97827e9ec3a22a923169"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_61f1d453ca3c6f56322e44104a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_17e9968c3e3863f8308c0cefce"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b39d7c4712fd9a0739e034919f"`);
    await queryRunner.query(`DROP TABLE "user-addresses"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_064c0b8959badfd4f4a142a6df"`);
    await queryRunner.query(`DROP TABLE "user_payment_cards"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2aac7de7c15294c7f90a51d065"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_05d78f8eb36ab8fe8c1110d6b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b383987bfa6e6a8745085621d0"`);
    await queryRunner.query(`DROP TABLE "users"`);
  }
}
