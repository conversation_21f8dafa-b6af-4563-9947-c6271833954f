import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AssignOwnerDto } from './dtos/assign-owner.dto';
import { CreateMerchantAccountDto } from './dtos/create-merchant-account.dto';
import { ListMerchantAccountDto } from './dtos/list-merchant-account.dto';
import { UpdateMerchantAccountDto } from './dtos/update-merchant-account.dto';
import { MerchantAccount } from './entities/merchant-account.entity';
import { MerchantAccountsService } from './merchant-accounts.service';

@ApiTags('Merchant accounts')
@Controller('merchant-accounts')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class MerchantAccountsController {
  constructor(private readonly merchantAccountsService: MerchantAccountsService) {}

  @Post()
  create(@Body() createMerchantAccountDto: CreateMerchantAccountDto): Promise<MerchantAccount> {
    return this.merchantAccountsService.create(createMerchantAccountDto);
  }

  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  @Get()
  findAll(
    @Query() listMerchantAccountDto: ListMerchantAccountDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<MerchantAccount>> {
    return this.merchantAccountsService.findAll(listMerchantAccountDto, ownerId);
  }

  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<MerchantAccount> {
    return this.merchantAccountsService.findOne(id, ownerId);
  }

  @Roles({ userType: UserType.MERCHANT_USER, role: '*' })
  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMerchantAccountDto: UpdateMerchantAccountDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MerchantAccount> {
    return this.merchantAccountsService.update(id, updateMerchantAccountDto, ownerId);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantAccount> {
    return this.merchantAccountsService.activate(id, null);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantAccount> {
    return this.merchantAccountsService.deactivate(id, null);
  }

  @Post('assign-owner')
  assignOwner(@Body() assignOwnerDto: AssignOwnerDto): Promise<MerchantAccount> {
    return this.merchantAccountsService.assignOwner(assignOwnerDto);
  }
}
