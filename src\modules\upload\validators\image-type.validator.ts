import { FileValidator } from '@nestjs/common';

export class ImageTypeValidator extends FileValidator {
  private allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  private allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];

  constructor() {
    super({});
  }

  isValid(file: Express.Multer.File): boolean {
    console.log('=== IMAGE VALIDATION DEBUG ===');
    console.log('File details:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    });

    // Primary check: MIME type
    if (this.allowedMimeTypes.includes(file.mimetype)) {
      console.log('✅ MIME type validation passed');
      return true;
    }

    // Fallback check: file extension (in case <PERSON><PERSON><PERSON> strips MIME type)
    if (file.originalname) {
      const extension = file.originalname.split('.').pop()?.toLowerCase();
      if (extension && this.allowedExtensions.includes(extension)) {
        console.log('✅ Extension validation passed as fallback');
        return true;
      }
    }

    console.log('❌ Validation failed for both MIME type and extension');
    return false;
  }

  buildErrorMessage(file?: Express.Multer.File): string {
    const receivedMimeType = file?.mimetype || 'unknown';
    const receivedExtension = file?.originalname?.split('.').pop()?.toLowerCase() || 'unknown';

    return `Invalid file type. Received MIME: "${receivedMimeType}", Extension: "${receivedExtension}". Expected MIME types: ${this.allowedMimeTypes.join(', ')} or extensions: ${this.allowedExtensions.join(', ')}`;
  }
}
