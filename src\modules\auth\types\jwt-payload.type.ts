import { Language } from '@/common/enums/language.enum';
import { ABRole } from '@/modules/admins/enums/admin-role.enum';
import { StaffRole } from '@/modules/merchant-staff/enums/staff-role.enum';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';

import { UserType } from '../enums/user-type.enum';

// Base JWT payload and info types
type BaseJwtPayload = { sub: string };
type BaseJwtInfo = { id: string };

// User JWT payload and info types
type UserJwt = {
  email: string;
  userType: UserType.USER;
  firstName: string | undefined;
  lastName: string | undefined;
  hasAddress: boolean;
  language: Language;
};

export type UserJwtPayload = BaseJwtPayload & UserJwt;
export type UserJwtInfo = BaseJwtInfo & UserJwt;

export type UserJwtPayloadToVerifyPhone = BaseJwtPayload & {
  phone: string;
  phoneCountryCode: string;
};

// Merchant user JWT payload and info types
type UserMetchantJwt = {
  email: string;
  userType: UserType.MERCHANT_USER;
  role: MerchantUserRole;
};

export type UserMetchantJwtPayload = BaseJwtPayload & UserMetchantJwt;
export type UserMetchantJwtInfo = BaseJwtInfo & UserMetchantJwt;

// Admin JWT payload and info types
type AdminJwt = {
  email: string;
  userType: UserType.AB_ADMIN;
  role: ABRole;
};

export type AdminJwtPayload = BaseJwtPayload & AdminJwt;
export type AdminJwtInfo = BaseJwtInfo & AdminJwt;

// Merchant staff JWT payload and info types
type UserMerchantStaffJwt = {
  username: string;
  restaurantId: string;
  userType: UserType.MERCHANT_STAFF;
  role: StaffRole;
};

export type UserMerchantStaffJwtPayload = BaseJwtPayload & UserMerchantStaffJwt;
export type UserMerchantStaffJwtInfo = BaseJwtInfo & UserMerchantStaffJwt;

// All user JWT info types
export type AllUserJwtInfo = UserJwtInfo | UserMetchantJwtInfo | UserMerchantStaffJwtInfo | AdminJwtInfo;
