import { Type } from 'class-transformer';
import { IsInt, IsPositive, Min, ValidateIf } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { PositionItemDto } from './position-item.dto';

export class PositionItemWithPriceDto extends PositionItemDto {
  @ApiProperty({
    description: 'Custom price for this item mapping (required, can be null)',
    required: true,
    nullable: true,
  })
  @ValidateIf((o) => o.price !== null)
  @IsInt()
  @Min(0)
  @Type(() => Number)
  price: number | null;
}

export class PositionItemWithPricePositiveDto extends PositionItemDto {
  @ApiProperty({
    description: 'Custom price for this item mapping (required, can be null)',
    required: true,
    nullable: true,
  })
  @ValidateIf((o) => o.price !== null)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  price: number | null;
}
