import { IsOptional, IsString, IsUUID } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListIngredientDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by internal name', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Filter by published name', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;
}
