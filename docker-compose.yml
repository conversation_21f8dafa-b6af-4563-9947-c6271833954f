version: '3.8'

services:
  postgres:
    image: postgis/postgis:16-3.5-alpine
    container_name: anhbeo-postgres-dev
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-anhbeo_dev}
    ports:
      - '${POSTGRES_PORT:-5432}:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-anhbeo_dev}']
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped


  redis:
    image: redis:7.4.3-alpine
    container_name: anhbeo-redis-dev
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
