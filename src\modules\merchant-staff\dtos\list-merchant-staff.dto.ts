import { IsOptional, IsUUID } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListMerchantStaffDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;

  @ApiProperty({ description: 'Filter by brand ID', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;
}
