import { sample, times } from 'lodash';

export function generateRandomString(amount: number): string {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  return times(amount, () => sample(characters)).join('');
}

export function generateCode(amount: number = 3): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  return times(amount, () => sample(characters)).join('');
}
