import { MerchantUsersModule } from '@/modules/merchant-users/merchant-users.module';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AuthMerchantUserController } from './auth-merchant-user.controller';
import { AuthMerchantUserService } from './auth-merchant-user.service';
import { MerchantUserJwtStrategy } from './strategies/merchant-user-jwt.strategy';

@Module({
  imports: [
    MerchantUsersModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('auth.merchantUserJwtAccessSecret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.accessTokenExpiresIn'),
        },
      }),
    }),
  ],
  controllers: [AuthMerchantUserController],
  providers: [AuthMerchantUserService, MerchantUserJwtStrategy],
  exports: [AuthMerchantUserService],
})
export class AuthMerchantUserModule {}
