import * as fs from 'fs';
import * as path from 'path';

/**
 * <PERSON><PERSON><PERSON> to create a new seeder file template
 */
function createSeeder() {
  try {
    const dateNow = Date.now();
    const args = process.argv.slice(2);

    if (args.length === 0) {
      console.error('Please provide a name for the seeder!');
      console.log('Usage: npm run seed:create -- seeder_name');
      process.exit(1);
    }

    // Get seeder name from command line arguments
    let seederName = args[0];

    // Convert name to PascalCase if not already in correct format
    seederName = seederName.charAt(0).toUpperCase() + seederName.slice(1);
    if (!seederName.endsWith('Seeder')) {
      seederName = `${seederName}`;
    }

    const seederNameWithTime = seederName + dateNow;

    // Create file name and path in parent directory

    const parentDir = path.join(__dirname, '..');
    const filePath = path.join(parentDir, `${dateNow}-${seederName}.seeder.ts`);

    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.error(`Seeder ${dateNow}-${seederName}.seeder.ts already exists!`);
      process.exit(1);
    }
    const repositoryName = seederName.charAt(0).toLowerCase() + seederName.slice(1);

    const content = `import { DataSource, Repository } from 'typeorm';
import { Seeder } from './config/base.seeder';
import { ${seederName} } from '@/modules/${seederName.toLowerCase()}s/entities/${seederName.toLowerCase()}.entity';

/**
 * Seeder for ${seederName} entity
 */
export class ${seederNameWithTime} extends Seeder {
  private ${repositoryName}Repository: Repository<${seederName}>;

  constructor(dataSource: DataSource) {
    super(dataSource);
    this.${repositoryName}Repository = dataSource.getRepository(${seederName});
  }

  /**
   * Name of the seeder
   */
  get name(): string {
    return '${seederNameWithTime}';
  }

  /**
   * Execute seeder
   */
  async run(): Promise<void> {
    // TODO: Add your seeding logic here
    
  }
}`;

    // Write the file
    fs.writeFileSync(filePath, content);
    console.log(`Seeder file created successfully: ${filePath}`);
    console.log(`You can edit this file and run 'npm run seed:run' to execute the seeder.`);
  } catch (error) {
    console.error('Error creating seeder file:', error);
    process.exit(1);
  }
}

// Run the function and catch errors
void createSeeder();
