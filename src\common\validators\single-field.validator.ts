import { Validate, ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ name: 'onlyOneField', async: false })
export class OnlyOneFieldConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const object = args.object as any;
    const fields = ['internalName', 'publishedName'];

    // Đếm số lượng field có giá trị
    const providedFields = fields.filter((field: string) => {
      const fieldValue = object[field];
      return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
    });

    // Chỉ cho phép đúng 1 field có giá trị
    return providedFields.length === 1;
  }

  defaultMessage(_args: ValidationArguments) {
    return 'Exactly one of internalName or publishedName must be provided';
  }
}

export function OnlyOneField() {
  return Validate(OnlyOneFieldConstraint);
}
