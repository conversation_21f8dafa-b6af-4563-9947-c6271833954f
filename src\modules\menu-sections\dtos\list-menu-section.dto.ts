import { IsBoolean, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PaginationSortDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export enum MenuSectionSortBy {
  INTERNAL_NAME = 'internalName',
  PUBLISHED_NAME = 'publishedName',
  ACTIVE_AT = 'activeAt',
  UPDATED_AT = 'updatedAt',
  ITEMS_COUNT = 'itemsCount',
}

export class ListMenuSectionDto extends PaginationSortDto {
  @ApiProperty({ description: 'Filter by internal name', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Filter by published name', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({
    description: 'Filter by is active',
    required: false,
    example: true,
  })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Filter by restaurant ID', required: false })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;

  @ApiProperty({
    description: 'Sort by field',
    required: false,
    enum: MenuSectionSortBy,
    default: MenuSectionSortBy.UPDATED_AT,
  })
  @IsOptional()
  @IsEnum(MenuSectionSortBy)
  sortBy: MenuSectionSortBy = MenuSectionSortBy.UPDATED_AT;
}
