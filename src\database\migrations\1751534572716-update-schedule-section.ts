import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateScheduleSection1751534572716 implements MigrationInterface {
  name = 'UpdateScheduleSection1751534572716';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "start" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "start" SET DEFAULT '00:00:00'`,
    );
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "end" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "end" SET DEFAULT '23:59:59'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "end" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "end" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "start" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "menu_section_available_schedules" ALTER COLUMN "start" DROP NOT NULL`);
  }
}
