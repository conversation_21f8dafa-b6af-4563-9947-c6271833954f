import { IsBoolean, IsInt, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON>, <PERSON>, Valida<PERSON> } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { IsGreaterThanOrEqualToValidator } from '@/common/decorators/validators.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class RejectOrderDto {
  @ApiProperty({ description: 'Reason for rejecting the order' })
  @IsNotEmpty()
  reason: string;
}

export class SetPrepTimeDto {
  @ApiProperty({ description: 'Updated preparation time estimate in minutes' })
  @IsInt()
  @Min(1)
  @Max(360)
  prepTimeEstimate: number;
}

export class SetDeliveryEtaDto {
  @ApiProperty({ description: 'Updated delivery time estimate from in minutes' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Max(360)
  deliveryTimeEstimateFrom: number;

  @ApiProperty({ description: 'Updated delivery time estimate to in minutes' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Max(360)
  @Validate(IsGreaterThanOrEqualToValidator, ['deliveryTimeEstimateFrom'])
  deliveryTimeEstimateTo: number;
}

export class ApproveModificationDto {
  @ApiProperty({ description: 'Whether to approve the modification' })
  @IsNotEmpty()
  approved: boolean;
}

export class ConfirmOrderReceivedDto {
  @ApiProperty({ description: 'Whether the user has received the order' })
  @ToBoolean()
  @IsBoolean()
  received: boolean;
}
