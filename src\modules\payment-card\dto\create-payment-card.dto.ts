import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

// payment-card.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class CreatePaymentCardDto {
  @ApiProperty({ description: 'User ID' })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Token number from OnePay' })
  @IsNotEmpty()
  @IsString()
  tokenNumber: string;

  @ApiProperty({ description: 'Token expiry date' })
  @IsNotEmpty()
  @IsString()
  tokenExpiry: string;

  @ApiProperty({ description: 'Masked card number', required: false })
  @IsOptional()
  @IsString()
  maskedCardNumber?: string;

  @ApiProperty({ description: 'Card type', required: false })
  @IsOptional()
  @IsString()
  cardBank?: string;

  @ApiProperty({ description: 'Card UID', required: false })
  @IsNotEmpty()
  @IsString()
  cardUid: string;
}
