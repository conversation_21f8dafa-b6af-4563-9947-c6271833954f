import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator';

import { ToBoolean, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListMerchantUserDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by email', required: false })
  @IsOptional()
  @IsEmail()
  @ToLowerCase()
  email?: string;

  @ApiProperty({ description: 'Filter by first name', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: 'Filter by last name', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: 'Filter by ban status', required: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  banned?: boolean;
}
