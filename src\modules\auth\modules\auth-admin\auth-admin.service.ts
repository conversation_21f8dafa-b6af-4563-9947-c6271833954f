import * as bcrypt from 'bcrypt';
import { Request } from 'express';
import ms, { StringValue } from 'ms';

import { AdminsService } from '@/modules/admins/admins.service';
import { ACCESS_TOKEN_COOKIE_NAME, REFRESH_TOKEN_COOKIE_NAME } from '@auth/constants/auth.constants';
import { UserType } from '@auth/enums/user-type.enum';
import { AdminJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';

import { LoginAdminDto } from './dtos/login.dto';

@Injectable()
export class AuthAdminService {
  private acessTokenOptions: JwtSignOptions;
  private refreshTokenOptions: JwtSignOptions;
  private cookieMaxAgeAccessToken: number;
  private cookieMaxAgeRefreshToken: number;
  constructor(
    private adminsService: AdminsService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    this.acessTokenOptions = {
      secret: this.configService.get<string>('auth.adminJwtAccessSecret'),
      expiresIn: this.configService.get<string>('auth.accessTokenExpiresIn'),
    };

    this.refreshTokenOptions = {
      secret: this.configService.get<string>('auth.adminJwtRefreshSecret'),
      expiresIn: this.configService.get<string>('auth.refreshTokenExpiresIn'),
    };

    this.cookieMaxAgeAccessToken = ms((this.acessTokenOptions.expiresIn as StringValue) || '1d');
    this.cookieMaxAgeRefreshToken = ms((this.refreshTokenOptions.expiresIn as StringValue) || '1d');
  }

  async validateUser(email: string, password: string) {
    const user = await this.adminsService.findOneByEmail(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    // In a real application, you would use bcrypt.compare
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Remove password from the returned object
    const { password: _, ...result } = user;
    return result;
  }

  async login(loginDto: LoginAdminDto, response: any) {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    const payload: AdminJwtPayload = {
      sub: user.id,
      email: user.email,
      userType: UserType.AB_ADMIN,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload, this.acessTokenOptions);

    const refreshToken = this.jwtService.sign(payload, this.refreshTokenOptions);

    // Set cookies
    this.setCookies(response, accessToken, refreshToken);

    return {
      user: await this.adminsService.getMe(user.id),
      accessToken,
      refreshToken,
    };
  }

  async refreshToken(request: Request, response: any) {
    try {
      // Get refresh token from cookies
      const refreshToken = request.cookies?.refresh_token;

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const decoded = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.adminJwtRefreshSecret'),
      });

      const user = await this.adminsService.findById(decoded.sub);

      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (user.banned) {
        throw new UnauthorizedException('User is banned');
      }

      const payload: AdminJwtPayload = {
        sub: user.id,
        email: user.email,
        userType: UserType.AB_ADMIN,
        role: user.role,
      };

      const accessToken = this.jwtService.sign(payload, this.acessTokenOptions);

      const newRefreshToken = this.jwtService.sign(payload, this.refreshTokenOptions);

      // Set cookies
      this.setCookies(response, accessToken, newRefreshToken);

      return { accessToken, refreshToken: newRefreshToken };
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  logout(response: any) {
    // Clear cookies
    this.clearCookies(response);

    return { message: 'Logout successful' };
  }

  private setCookies(response: any, accessToken: string, refreshToken: string) {
    const cookieOptions = {
      httpOnly: this.configService.get<boolean>('auth.cookieHttpOnly'),
      secure: this.configService.get<boolean>('auth.cookieSecure'),
      sameSite: this.configService.get<string>('auth.cookieSameSite'),
    };

    response.cookie(ACCESS_TOKEN_COOKIE_NAME, accessToken, {
      maxAge: this.cookieMaxAgeAccessToken,
      ...cookieOptions,
    });
    response.cookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
      maxAge: this.cookieMaxAgeRefreshToken,
      ...cookieOptions,
    });
  }

  private clearCookies(response: any) {
    response.clearCookie(ACCESS_TOKEN_COOKIE_NAME);
    response.clearCookie(REFRESH_TOKEN_COOKIE_NAME);
  }
}
