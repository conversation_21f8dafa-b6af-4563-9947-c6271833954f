import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { Language } from '@/common/enums/language.enum';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { IsTimeWithoutTz } from '@/common/validators/timetz.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class ScheduleItem {
  @ApiProperty({ description: 'Day of the week (0-6, where 0 is Sunday)', example: 0 })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  @Max(6)
  day: number;

  @ApiProperty({ description: 'Start time (HH:MM format)', example: '10:00', required: false })
  @IsString()
  @IsTimeWithoutTz()
  start: string;

  @ApiProperty({ description: 'End time (HH:MM format)', example: '14:00', required: false })
  @IsString()
  @IsTimeWithoutTz()
  end: string;

  @ApiProperty({ description: 'Is open all day', example: false, default: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isAllDay?: boolean;
}

export class CreateRestaurantDto {
  @ApiProperty({ description: 'Internal name of the restaurant' })
  @IsNotEmpty()
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the restaurant' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'Avatar image URL of the restaurant' })
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_AVATAR)
  avatarImg: string;

  @ApiProperty({ description: 'Background image URL of the restaurant' })
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_BANNER)
  backgroundImg: string;

  @ApiProperty({ description: 'Price range of the restaurant', required: false })
  @IsOptional()
  @IsString()
  priceRange?: string;

  @ApiProperty({ description: 'Address of the restaurant', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Ward of the restaurant', required: false })
  @IsOptional()
  @IsString()
  ward?: string;

  @ApiProperty({ description: 'District of the restaurant', required: false })
  @IsOptional()
  @IsString()
  district?: string;

  @ApiProperty({ description: 'Province of the restaurant', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  @ApiProperty({ description: 'Phone number of the restaurant', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Latitude of the restaurant location',
    example: 10.7769,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Type(() => Number)
  latitude: number;

  @ApiProperty({
    description: 'Longitude of the restaurant location',
    example: 106.7009,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Type(() => Number)
  longitude: number;

  @ApiProperty({
    description: 'Available schedule for the restaurant',
    required: false,
    type: [ScheduleItem],
    example: [{ day: 1, start: '10:00', end: '14:00', isAllDay: false }],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  availableSchedule?: ScheduleItem[];

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Tags of the restaurant', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  tagIds?: string[];

  @ApiProperty({ description: 'ID of the brand' })
  @IsNotEmpty()
  @IsUUID()
  brandId: string;

  @ApiProperty({ description: 'ID of the menu to activate', required: false })
  @IsOptional()
  @IsUUID()
  activeMenuId?: string;

  @ApiProperty({
    description: 'Default language of the restaurant',
    enum: Language,
    default: Language.EN,
    required: false,
  })
  @IsOptional()
  @IsEnum(Language)
  defaultLanguage?: Language = Language.EN;
}
