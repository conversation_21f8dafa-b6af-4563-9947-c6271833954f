import { Column, Entity, Index, ManyToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('restaurant_tags')
@Index('idx_restaurant_tags_name_trigram', { synchronize: false }) // GIN index for fuzzy search, sync=false vì tạo qua migration
export class RestaurantTag extends BaseEntity {
  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @ManyToMany(() => Restaurant, (restaurant) => restaurant.tags)
  restaurants: Restaurant[];
}
