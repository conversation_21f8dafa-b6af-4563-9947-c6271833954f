import { Exclude } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { GeofencingType } from '../geofencing.types';

@Entity('geofencing')
export class Geofencing extends BaseEntity {
  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string | null;

  @Column({ type: 'enum', enum: GeofencingType })
  type: GeofencingType;

  @Index()
  @Column({
    name: 'shipping_fee',
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  shippingFee: number;

  // For PostGIS geometry storage
  @Exclude()
  @Index('IDX_geofencing_geometry_gist', { spatial: true })
  @Column({
    type: 'geometry',
    spatialFeatureType: 'Geometry',
    srid: 4326, // WGS84
  })
  geometry: string;

  // Store original data for API responses
  @Column({ name: 'geometry_data', type: 'jsonb' })
  geometryData: {
    snapshot: {
      path?: Array<{ lat: number; lng: number }>; // for polygon
      center?: { lat: number; lng: number }; // for circle
      radius?: number; // for circle
      bounds?: {
        // for rectangle
        south: number;
        west: number;
        north: number;
        east: number;
      };
    };
    type: string;
    fillColor: string;
  };

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.geofencing)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;
}
