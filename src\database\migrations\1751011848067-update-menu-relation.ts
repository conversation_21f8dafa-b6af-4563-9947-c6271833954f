import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMenuRelation1751011848067 implements MigrationInterface {
  name = 'UpdateMenuRelation1751011848067';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP CONSTRAINT "FK_b997d8accd6b14e574ab0319fb9"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP CONSTRAINT "FK_34bd012d009a5a3d880c56c027d"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP CONSTRAINT "FK_8928c5b2ca8da66d5bcce12eb09"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP CONSTRAINT "FK_2d2e48df4ccb4860ffbe6678076"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP CONSTRAINT "FK_acee03cf5662366ec3b3aae98bc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b997d8accd6b14e574ab0319fb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_34bd012d009a5a3d880c56c027"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8928c5b2ca8da66d5bcce12eb0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d2e48df4ccb4860ffbe667807"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_acee03cf5662366ec3b3aae98b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1a5653ff049df3e9cb6c54d06c"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "brand_id"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP COLUMN "brand_id"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP COLUMN "brand_id"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "brand_id"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "brand_id"`);
    await queryRunner.query(
      `ALTER TABLE "menu_item_option_groups" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`,
    );
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "restaurant_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" ADD "restaurant_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "ingredients" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(`ALTER TABLE "ingredients" ADD "restaurant_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "restaurant_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "restaurant_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menus" ADD "code" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_2988f46a1a98b221a5ffcc641b" ON "menu_item_option_groups" ("code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4c32753d45094d0f2f7bf05bef" ON "menu_item_option_groups" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_5e4b1b61ec860d7cb50648e259" ON "menu_item_option_groups" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8431dcda04bee7d0265acc6bdf" ON "menu_item_options" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_5196746c95ab1710cd5c73c908" ON "menu_item_options" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_bffed124d5fffa79d4cc5f945e" ON "menu_item_options" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_291910d4875ae08b2202f08eff" ON "ingredients" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_3f3748c03ffce9383bc4760e1f" ON "ingredients" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f65641c6855ac01088bab92524" ON "ingredients" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_42b75f4146dd6c5facd0369007" ON "menu_items" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_8d1ee4780bf64ae94cbf3e5370" ON "menu_items" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_02969eec5a444af1f326ec6059" ON "menu_items" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_3bbc3e40de7b87601f907cf196" ON "menu_sections" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_ec73ac3da075000d0b7cbfafe4" ON "menu_sections" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_7bb0257e08249d7c3d7948ec1f" ON "menu_sections" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_561ca9e52e041a95d0c7b86b86" ON "menu_sections" ("restaurant_id", "published_name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_9b9626d392ff967d6715178a3e" ON "menus" ("code") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_979fbb4aced0f9996aa6442482" ON "menus" ("code", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_option_groups" ADD CONSTRAINT "FK_4c32753d45094d0f2f7bf05bef3" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_options" ADD CONSTRAINT "FK_5196746c95ab1710cd5c73c9089" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "ingredients" ADD CONSTRAINT "FK_3f3748c03ffce9383bc4760e1fa" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_items" ADD CONSTRAINT "FK_8d1ee4780bf64ae94cbf3e53705" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_sections" ADD CONSTRAINT "FK_ec73ac3da075000d0b7cbfafe45" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP CONSTRAINT "FK_ec73ac3da075000d0b7cbfafe45"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP CONSTRAINT "FK_8d1ee4780bf64ae94cbf3e53705"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP CONSTRAINT "FK_3f3748c03ffce9383bc4760e1fa"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP CONSTRAINT "FK_5196746c95ab1710cd5c73c9089"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP CONSTRAINT "FK_4c32753d45094d0f2f7bf05bef3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_979fbb4aced0f9996aa6442482"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9b9626d392ff967d6715178a3e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_561ca9e52e041a95d0c7b86b86"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7bb0257e08249d7c3d7948ec1f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ec73ac3da075000d0b7cbfafe4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bbc3e40de7b87601f907cf196"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_02969eec5a444af1f326ec6059"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8d1ee4780bf64ae94cbf3e5370"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_42b75f4146dd6c5facd0369007"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f65641c6855ac01088bab92524"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3f3748c03ffce9383bc4760e1f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_291910d4875ae08b2202f08eff"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bffed124d5fffa79d4cc5f945e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5196746c95ab1710cd5c73c908"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8431dcda04bee7d0265acc6bdf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5e4b1b61ec860d7cb50648e259"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c32753d45094d0f2f7bf05bef"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2988f46a1a98b221a5ffcc641b"`);
    await queryRunner.query(`ALTER TABLE "menus" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "restaurant_id"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "restaurant_id"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP COLUMN "restaurant_id"`);
    await queryRunner.query(`ALTER TABLE "ingredients" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP COLUMN "restaurant_id"`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "restaurant_id"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "brand_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "brand_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "ingredients" ADD "brand_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_item_options" ADD "brand_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "brand_id" uuid NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_1a5653ff049df3e9cb6c54d06c" ON "menu_sections" ("published_name", "brand_id") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_acee03cf5662366ec3b3aae98b" ON "menu_sections" ("brand_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_2d2e48df4ccb4860ffbe667807" ON "menu_items" ("brand_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_8928c5b2ca8da66d5bcce12eb0" ON "ingredients" ("brand_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_34bd012d009a5a3d880c56c027" ON "menu_item_options" ("brand_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_b997d8accd6b14e574ab0319fb" ON "menu_item_option_groups" ("brand_id") `);
    await queryRunner.query(
      `ALTER TABLE "menu_sections" ADD CONSTRAINT "FK_acee03cf5662366ec3b3aae98bc" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_items" ADD CONSTRAINT "FK_2d2e48df4ccb4860ffbe6678076" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "ingredients" ADD CONSTRAINT "FK_8928c5b2ca8da66d5bcce12eb09" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_options" ADD CONSTRAINT "FK_34bd012d009a5a3d880c56c027d" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "menu_item_option_groups" ADD CONSTRAINT "FK_b997d8accd6b14e574ab0319fb9" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
