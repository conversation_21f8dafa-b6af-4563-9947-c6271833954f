import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CallbackOnepay {
  @ApiProperty({ description: 'Giá trị vpc_Command đã g<PERSON>i sang cổng thanh toán', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_Command: string;

  @ApiProperty({ description: 'Mã giao dịch đã gửi sang cổng', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_MerchTxnRef: string;

  @ApiProperty({ description: 'Merchant ID đã gửi sang cổng.', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_Merchant: string;

  @ApiProperty({ description: 'Mã đơn hàng đã gửi sang cổng.', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_OrderInfo: string;

  @ApiProperty({ description: 'Số tiền g<PERSON>i đã sang cổng (đã nhân 100).', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_Amount: string;

  @ApiProperty({
    description:
      'Mã kết quả được sinh ra bởi cổng thanh toán để chỉ trạng thái giao dịch. Giá trị là "0" (zero) cho biết giao dịch đã được xử lý thành công. Tất cả các giá trị khác cho biết giao dịch đã bị từ chối.',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  vpc_TxnResponseCode: string;

  @ApiProperty({ description: 'Mô tả lỗi giao dịch khi thanh toán', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_Message: string;

  @ApiProperty({ description: 'Chuỗi chữ ký bảo mật toàn vẹn dữ liệu.', required: true })
  @IsNotEmpty()
  @IsString()
  vpc_SecureHash: string;

  // not required

  @ApiProperty({ description: 'Phiên bản thanh toán của cổng.', required: false })
  @IsOptional()
  @IsString()
  vpc_Version?: string;

  @ApiProperty({
    description: 'Mã token trả về nếu giao dịch chọn tạo token, format giống 6 số đầu và 3 số cuối của thẻ',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_TokenNum?: string;

  @ApiProperty({ description: 'Thời gian hết hạn token, ví dụ OCT-2019: 1019', required: false })
  @IsOptional()
  @IsString()
  vpc_TokenExp?: string;

  @ApiProperty({
    description: 'Là một chuỗi duy nhất được sinh ra từ cổng thanh toán cho mỗi giao dịch.',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_TransactionNo?: string;

  @ApiProperty({
    description:
      'Kênh thanh toán: - WEB: Thanh toán qua website.- APP: thanh toán qua ứng dụng MobileBanking hoặc Ví điện tử.',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_PayChannel?: string;

  @ApiProperty({
    description: 'Loại thẻ đã thanh toán: - Quốc tế: VC, MC, JC, AE, CUP.- Nội địa: 6 số đầu định danh thẻ',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_Card?: string;

  @ApiProperty({
    description:
      'Mã duy nhất cho mỗi thẻ, ví dụ: ABE4864268F53BD5D531D8845FAAF5F1 Không trả về cho các ngân hàng sau TCB, VIB và DongABank.',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_CardUid?: string;

  @ApiProperty({ description: 'Card đã mã hóa, 6 số đầu/ 4 số cuối, vd: 412345xxxxxx1234.', required: false })
  @IsOptional()
  @IsString()
  vpc_CardNum?: string;

  @ApiProperty({ description: 'Ngày hết hạn của thẻ thanh toán (MMYY).', required: false })
  @IsOptional()
  @IsString()
  vpc_CardExp?: string;

  @ApiProperty({ description: 'Mã ủy quyền giao dịch từ ngân hàng.', required: false })
  @IsOptional()
  @IsString()
  vpc_AuthorizeId?: string;

  @ApiProperty({ description: 'Mã phản hồi từ ngân hàng phát hành thẻ.', required: false })
  @IsOptional()
  @IsString()
  vpc_AcqResponseCode?: string;

  // trả góp
  @ApiProperty({ description: 'Với giao dịch trả góp: Tên chủ thẻ.', required: false })
  @IsOptional()
  @IsString()
  vpc_CardHolderName?: string;

  @ApiProperty({
    description: 'Với giao dịch trả góp: Ngân hàng thanh toán trả góp (swift code của bank).',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_ItaBank?: string;

  @ApiProperty({ description: 'Với giao dịch trả góp: Phí trả góp.', required: false })
  @IsOptional()
  @IsString()
  vpc_ItaFeeAmount?: string;

  @ApiProperty({ description: 'Với giao dịch trả góp: Kỳ hạn trả góp.', required: false })
  @IsOptional()
  @IsString()
  vpc_ItaTime?: string;

  @ApiProperty({ description: 'Với giao dịch trả góp: Số điện thoại của khách hàng.', required: false })
  @IsOptional()
  @IsString()
  vpc_ItaMobile?: string;

  @ApiProperty({ description: 'Với giao dịch trả góp: Email của khách hàng.', required: false })
  @IsOptional()
  @IsString()
  vpc_ItaEmail?: string;

  @ApiProperty({
    description:
      'Với giao dịch trả góp: Số tiền gốc của đơn vị truyền sang OnePay khi chưa có phí trả góp. Nếu giao dịch không có phí trả góp thì vpc_Amount = vpc_OrderAmount.',
    required: false,
  })
  @IsOptional()
  @IsString()
  vpc_OrderAmount?: string;

  @ApiProperty({ description: 'Tham số trả về mà đơn vị tự định nghĩa ở Request. Vd: User_NhieuTien', required: false })
  @IsOptional()
  @IsString()
  user_Xxxxxx?: string;

  @ApiProperty({ description: 'Thời gian thực hiện thanh toán', required: false })
  @IsOptional()
  @IsString()
  vpc_PaymentTime?: string;

  @ApiProperty({ description: 'Quốc gia của thẻ thanh toán', required: false })
  @IsOptional()
  @IsString()
  vpc_BinCountry?: string;

  @ApiProperty({ description: 'Mã khách hàng', required: false })
  @IsOptional()
  @IsString()
  user_CustomerId?: string;
}
