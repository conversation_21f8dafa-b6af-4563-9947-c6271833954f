import { Module } from '@nestjs/common';

import { EmailModule } from './email/email.module';
import { FCMModule } from './fcm/fcm.module';
import { MicroserviceModule } from './microservice/microservice.module';
import { RedisModule } from './redis/redis.module';
import { RestaurantAccessModule } from './restaurant-access/restaurant-access.module';

@Module({
  imports: [EmailModule, FCMModule, MicroserviceModule, RedisModule, RestaurantAccessModule],
  exports: [EmailModule, FCMModule, MicroserviceModule, RedisModule, RestaurantAccessModule],
})
export class SharedModule {}
