import { OrderStatus, OrderStatusGroup } from './order.enums';

const { NEW, MODIFIED, MODIFIED_ACCEPTED, IN_KITCHEN, IN_KITCHEN_OVERDUE, PAYING } = OrderStatus;
const { IN_DELIVERY, IN_DELIVERY_OVERDUE, DELIVERED, NOT_DELIVERED, CANCELLED, UNFULFILLED } = OrderStatus;

// Mapping from individual statuses to status groups
export const STATUS_GROUP_MAPPING: Record<OrderStatus, OrderStatusGroup[]> = {
  [NEW]: [OrderStatusGroup.NEEDS_ACTION],
  [MODIFIED]: [OrderStatusGroup.IN_PROCESS],
  [MODIFIED_ACCEPTED]: [OrderStatusGroup.NEEDS_ACTION],
  [IN_KITCHEN]: [OrderStatusGroup.IN_PROCESS],
  [IN_KITCHEN_OVERDUE]: [OrderStatusGroup.IN_PROCESS, OrderStatusGroup.NEEDS_ACTION],
  [PAYING]: [OrderStatusGroup.IN_PROCESS],
  [IN_DELIVERY]: [OrderStatusGroup.IN_PROCESS],
  [IN_DELIVERY_OVERDUE]: [OrderStatusGroup.IN_PROCESS, OrderStatusGroup.NEEDS_ACTION],
  [DELIVERED]: [OrderStatusGroup.COMPLETED],
  [NOT_DELIVERED]: [OrderStatusGroup.NEEDS_ACTION],
  [CANCELLED]: [OrderStatusGroup.COMPLETED],
  [UNFULFILLED]: [OrderStatusGroup.COMPLETED],
};

// Helper function to get statuses by group
export const getStatusesByGroup = (group: OrderStatusGroup): OrderStatus[] => {
  return Object.entries(STATUS_GROUP_MAPPING)
    .filter(([_, statusGroup]) => statusGroup.includes(group))
    .map(([status, _]) => status as OrderStatus);
};

// Helper function to get all statuses for multiple groups
export const getStatusesByGroups = (groups: OrderStatusGroup[]): OrderStatus[] => {
  return Array.from(new Set(groups.flatMap((group) => getStatusesByGroup(group))));
};

const VALID_TRANSITIONS_MAP: Record<OrderStatus, OrderStatus[]> = {
  [NEW]: [IN_KITCHEN, CANCELLED, MODIFIED],
  [MODIFIED]: [MODIFIED_ACCEPTED, CANCELLED, MODIFIED],
  [MODIFIED_ACCEPTED]: [IN_KITCHEN],
  [IN_KITCHEN]: [IN_DELIVERY, IN_KITCHEN_OVERDUE],
  [IN_KITCHEN_OVERDUE]: [IN_DELIVERY],
  [PAYING]: [IN_KITCHEN],
  [IN_DELIVERY]: [DELIVERED, UNFULFILLED, IN_DELIVERY_OVERDUE, NOT_DELIVERED],
  [IN_DELIVERY_OVERDUE]: [DELIVERED, UNFULFILLED, NOT_DELIVERED],
  [NOT_DELIVERED]: [DELIVERED, UNFULFILLED],
  [DELIVERED]: [],
  [CANCELLED]: [],
  [UNFULFILLED]: [],
};

export const validateTransitionsStatus = (currentStatus: OrderStatus, newStatus: OrderStatus) => {
  const validTransitions = VALID_TRANSITIONS_MAP[currentStatus];
  return validTransitions?.includes(newStatus) || false;
};
