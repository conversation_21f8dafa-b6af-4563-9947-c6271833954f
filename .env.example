# .env.example
# ---------------------------------------------------------------------------
# Environment variables for anh-beo-backend
#
# Copy this file to .env and replace placeholder values with your actual configuration.
# Do NOT commit the .env file to version control.
# ---------------------------------------------------------------------------

# --- Application Configuration ---
APP_MODE=general # Mode (api, job, general)
NODE_ENV=development # Environment (development, production, test)
PORT=3000            # Port the NestJS application will listen on

ENABLE_FAKE_USER=false
ENABLE_FAKE_NOTIFICATION=false
ENABLE_PUBLIC_FOLDER=false
ENABLE_SWAGGER=false
SWAGGER_USER=
SWAGGER_PASSWORD=

# --- PostgreSQL Database (TypeORM & Docker) ---
POSTGRES_HOST=localhost     # Database host (use 'postgres' if running inside Docker network without host mapping)
POSTGRES_PORT=5432          # Port exposed by the PostgreSQL container
POSTGRES_USER=postgres      # Database username
POSTGRES_PASSWORD=password  # Database password
POSTGRES_DB=anhbeo_dev      # Database name
POSTGRES_SSL=false          # Set to true if your DB requires SSL (e.g., managed cloud DB)
POSTGRES_LOGGING=false
POSTGRES_SYNCHRONIZE=false

# --- Redis Cache & BullMQ (Docker) ---
REDIS_HOST=localhost        # Redis host (use 'redis' if running inside Docker network without host mapping)
REDIS_PORT=6379             # Port exposed by the Redis container
REDIS_PASSWORD=             # Redis password (if any, leave blank if none)
# REDIS_DB=0                # Optional: Specify Redis DB index if needed

# --- Other Potential Variables (Add as needed) ---
# Example: Email service (Resend/SES)
RESEND_API_KEY=
DEFAULT_FROM_EMAIL=

# --- Auth Configuration ---
USER_JWT_ACCESS_SECRET=user-access-secret-key
USER_JWT_REFRESH_SECRET=user-refresh-secret-key
MERCHANT_USER_JWT_ACCESS_SECRET=merchant-user-access-secret-key
MERCHANT_USER_JWT_REFRESH_SECRET=merchant-user-refresh-secret-key
MERCHANT_STAFF_JWT_ACCESS_SECRET=merchant-staff-access-secret-key
MERCHANT_STAFF_JWT_REFRESH_SECRET=merchant-staff-refresh-secret-key
ADMIN_JWT_ACCESS_SECRET=admin-access-secret-key
ADMIN_JWT_REFRESH_SECRET=admin-refresh-secret-key

ACCESS_TOKEN_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=30d
COOKIE_SECURE=false      # Set to true in production

ROOT_ANHBEO_ADMIN_EMAIL=<EMAIL>
ROOT_ANHBEO_ADMIN_PASSWORD=ChangeMe123!
ROOT_ANHBEO_ADMIN_NAME=John

AWS_REGION=example
AWS_ACCESS_KEY_ID=example
AWS_SECRET_ACCESS_KEY=example
AWS_S3_BUCKET=example

# Onepay
ONEPAY_VOID_URL=https://mtf.onepay.vn/paygate/api/v1/vpc/merchants
ONEPAY_TOKEN_URL=https://mtf.onepay.vn/msp/api/v1/vpc/tokens
ONEPAY_CREATE_TOKEN_URL=https://mtf.onepay.vn/paygate/vpcpay.op
ONEPAY_PAYMENT_URL=https://mtf.onepay.vn/msp/api/v1/vpc/invoices
ONEPAY_QUERY_URL=https://mtf.onepay.vn/msp/api/v1/vpc/invoices/queries
ONEPAY_AGAIN_LINK=http://api-dev.anhbeo.com
ONEPAY_RETURNURL=https://api-dev.anhbeo.com
ONEPAY_MERCHANT_ADD_CARD=secret
ONEPAY_ACCESSCODE_ADD_CARD=secret
ONEPAY_SECURE_HASH_CODE_ADD_CARD=secret
ONEPAY_MERCHANT_PAYMENT=secret
ONEPAY_ACCESSCODE_PAYMENT=secret
ONEPAY_SECURE_HASH_CODE_PAYMENT=secret

OTP_EXPIRY_MINUTES=5

GOOGLE_MAPS_API_KEY=example

# FCM Configuration
FCM_SERVICE_ACCOUNT_PATH=./firebase-service-account.json

TAX_RATE=8
TAX_RATE_ALCOHOL=10