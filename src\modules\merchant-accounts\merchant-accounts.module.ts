import { MerchantUsersModule } from '@/modules/merchant-users/merchant-users.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantAccount } from './entities/merchant-account.entity';
import { MerchantAccountsController } from './merchant-accounts.controller';
import { MerchantAccountsService } from './merchant-accounts.service';

@Module({
  imports: [TypeOrmModule.forFeature([MerchantAccount]), MerchantUsersModule],
  controllers: [MerchantAccountsController],
  providers: [MerchantAccountsService],
  exports: [MerchantAccountsService],
})
export class MerchantAccountsModule {}
