import { IsOptional, IsString, IsUUID } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListMerchantAccountDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Filter by owner merchant user ID', required: false })
  @IsOptional()
  @IsUUID()
  ownerMerchantUserId?: string;
}
