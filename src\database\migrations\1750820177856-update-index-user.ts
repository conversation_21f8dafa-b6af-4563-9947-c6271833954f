import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateIndexUser1750820177856 implements MigrationInterface {
  name = 'UpdateIndexUser1750820177856';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_05d78f8eb36ab8fe8c1110d6b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2db8ce89554cded69dd8f0fa88"`);
    await queryRunner.query(`ALTER TABLE "order_customers" ADD "phoneCountryCode" character varying`);
    await queryRunner.query(`ALTER TABLE "order_customers" ALTER COLUMN "phone" DROP NOT NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_8c68be683a00c8412d591e329f" ON "users" ("phone", "phoneCountryCode") WHERE deleted_at IS NULL AND phone IS NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_8c68be683a00c8412d591e329f"`);
    await queryRunner.query(`ALTER TABLE "order_customers" ALTER COLUMN "phone" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "order_customers" DROP COLUMN "phoneCountryCode"`);
    await queryRunner.query(`CREATE INDEX "IDX_2db8ce89554cded69dd8f0fa88" ON "order_customers" ("name", "phone") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_05d78f8eb36ab8fe8c1110d6b7" ON "users" ("phone") WHERE ((deleted_at IS NULL) AND (phone IS NOT NULL))`,
    );
  }
}
