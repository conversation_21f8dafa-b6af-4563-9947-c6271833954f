import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { FOLDER_DIMENSIONS, FolderType } from './upload.constants';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private s3Client: S3Client;
  private bucket: string;
  private region: string;

  constructor(private configService: ConfigService) {
    const region = this.configService.get<string>('upload.aws.region');
    const accessKeyId = this.configService.get<string>('upload.aws.accessKeyId');
    const secretAccessKey = this.configService.get<string>('upload.aws.secretAccessKey');
    const bucket = this.configService.get<string>('upload.aws.bucket');

    if (!region || !accessKeyId || !secretAccessKey || !bucket) {
      this.logger.warn('Missing required AWS configuration');
    } else {
      this.s3Client = new S3Client({
        region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
      this.bucket = bucket;
      this.region = region;
    }
  }

  private validateConfig(): void {
    if (!this.region || !this.s3Client || !this.bucket) {
      throw new BadRequestException(`Missing required AWS configuration`);
    }
  }

  async generatePresignedUrl(fileType: string, folder = 'menu-items'): Promise<{ url: string; key: string }> {
    this.validateConfig();
    const key = `${folder}/${uuidv4()}-${Date.now()}`;
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      ContentType: fileType,
    });

    const url = await getSignedUrl(this.s3Client, command, { expiresIn: 3600 });
    return { url, key };
  }

  async generatePresignedUrlForMenuItem(fileType: string): Promise<{ url: string; key: string }> {
    this.validateConfig();
    return this.generatePresignedUrl(fileType, 'menu-items');
  }

  async uploadFile(file: Express.Multer.File, folderType: FolderType): Promise<{ key: string; url: string }> {
    this.validateConfig();
    // await this.validateImageDimensions(file, folderType);
    const key = `${folderType}/${Date.now()}-${uuidv4()}.${file.mimetype.split('/')[1]}`;

    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    await this.s3Client.send(command);
    const url = this.getFileUrl(key);

    return { key, url };
  }

  getFileUrl(key: string): string {
    return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
  }

  private async validateImageDimensions(file: Express.Multer.File, folderType: FolderType) {
    const dimensions = FOLDER_DIMENSIONS[folderType];
    if (!dimensions) return;

    const imageDimensions = await this.getImageDimensions(file);

    // Check minimum dimensions
    if (imageDimensions.width < dimensions.minWidth || imageDimensions.height < dimensions.minHeight) {
      throw new BadRequestException(`Image must be at least ${dimensions.minWidth}x${dimensions.minHeight} pixels`);
    }

    // Check aspect ratio if specified
    if (dimensions.aspectRatio) {
      const expectedRatio = dimensions.aspectRatio.width / dimensions.aspectRatio.height;
      const actualRatio = imageDimensions.width / imageDimensions.height;

      // Allow for small floating point differences
      const ratioTolerance = 0.01;
      if (Math.abs(actualRatio - expectedRatio) > ratioTolerance) {
        throw new BadRequestException(
          `Image must have aspect ratio ${dimensions.aspectRatio.width}:${dimensions.aspectRatio.height}`,
        );
      }
    }
  }

  private async getImageDimensions(file: Express.Multer.File): Promise<{ width: number; height: number }> {
    try {
      const metadata = await sharp(file.buffer).metadata();
      if (!metadata.width || !metadata.height) {
        throw new Error('Could not determine image dimensions');
      }
      return {
        width: metadata.width,
        height: metadata.height,
      };
    } catch (_error) {
      throw new BadRequestException('Invalid image file');
    }
  }
}
