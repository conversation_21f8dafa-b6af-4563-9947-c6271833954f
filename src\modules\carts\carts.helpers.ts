import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import { localizeMenuItem } from '../menu-items/menu-items.helpers';
import { Cart } from './entities/cart.entity';

export const localizeCart = (cart: Cart, userLanguage: Language) => {
  const restaurantLanguage = cart.restaurant?.defaultLanguage;

  if (!cart.cartItems) return;

  for (const { menuItem, cartItemOptions } of cart.cartItems) {
    if (menuItem) localizeMenuItem(menuItem, userLanguage, restaurantLanguage);

    if (!cartItemOptions) continue;

    for (const { menuItemOption, menuItemOptionGroup } of cartItemOptions) {
      LocalizationHelper.localizeMenuItemEntity(menuItemOption, userLanguage, restaurantLanguage);
      LocalizationHelper.localizeMenuItemEntity(menuItemOptionGroup, userLanguage, restaurantLanguage);
    }
  }
};
