import { Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '../menu-items/entities/menu-item.entity';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { RestaurantAccessService } from '../shared/restaurant-access/restaurant-access.service';
import { GetTranslatesDto, TranslateType } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';

export interface TranslateResponseItem {
  id: string;
  type: TranslateType;
  publishedName: string;
  publishedNameEn?: string | null;
  publishedNameVi?: string | null;
  description?: string | null;
  descriptionEn?: string | null;
  descriptionVi?: string | null;
}

@Injectable()
export class TranslatesService {
  constructor(
    @InjectRepository(MenuSection)
    private readonly menuSectionRepository: Repository<MenuSection>,
    @InjectRepository(MenuItem)
    private readonly menuItemRepository: Repository<MenuItem>,
    @InjectRepository(MenuItemOptionGroup)
    private readonly menuItemOptionGroupRepository: Repository<MenuItemOptionGroup>,
    private readonly restaurantAccessService: RestaurantAccessService,
  ) {}

  async getTranslates(dto: GetTranslatesDto, ownerId: string | null): Promise<TranslateResponseItem[]> {
    // Verify restaurant access
    await this.restaurantAccessService.verifyAccessRestaurant(dto.restaurantId, ownerId);

    const results: TranslateResponseItem[] = [];

    // Get menu sections
    if (!dto.type || dto.type === TranslateType.MENU_SECTION) {
      const menuSections = await this.menuSectionRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: ['id', 'publishedName', 'publishedNameEn', 'publishedNameVi'],
      });

      results.push(
        ...menuSections.map((section) => ({
          id: section.id,
          type: TranslateType.MENU_SECTION,
          publishedName: section.publishedName,
          publishedNameEn: section.publishedNameEn,
          publishedNameVi: section.publishedNameVi,
        })),
      );
    }

    // Get menu items
    if (!dto.type || dto.type === TranslateType.MENU_ITEM) {
      const menuItems = await this.menuItemRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: [
          'id',
          'publishedName',
          'publishedNameEn',
          'publishedNameVi',
          'description',
          'descriptionEn',
          'descriptionVi',
        ],
      });

      results.push(
        ...menuItems.map((item) => ({
          id: item.id,
          type: TranslateType.MENU_ITEM,
          publishedName: item.publishedName,
          publishedNameEn: item.publishedNameEn,
          publishedNameVi: item.publishedNameVi,
          description: item.description,
          descriptionEn: item.descriptionEn,
          descriptionVi: item.descriptionVi,
        })),
      );
    }

    // Get menu item option groups
    if (!dto.type || dto.type === TranslateType.MENU_ITEM_OPTION_GROUP) {
      const optionGroups = await this.menuItemOptionGroupRepository.find({
        where: { restaurantId: dto.restaurantId },
        select: ['id', 'publishedName', 'publishedNameEn', 'publishedNameVi'],
      });

      results.push(
        ...optionGroups.map((group) => ({
          id: group.id,
          type: TranslateType.MENU_ITEM_OPTION_GROUP,
          publishedName: group.publishedName,
          publishedNameEn: group.publishedNameEn,
          publishedNameVi: group.publishedNameVi,
        })),
      );
    }

    return results;
  }

  async updateTranslates(dto: UpdateTranslatesDto, ownerId: string | null): Promise<{ updated: number }> {
    let updatedCount = 0;

    for (const item of dto.items) {
      let repository: Repository<any>;
      let entity: any;
      let updateData: any = {};

      switch (item.type) {
        case TranslateType.MENU_SECTION:
          repository = this.menuSectionRepository;
          entity = await repository.findOne({ where: { id: item.id } });
          // Menu sections only have publishedName fields
          updateData = {
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          };
          break;
        case TranslateType.MENU_ITEM:
          repository = this.menuItemRepository;
          entity = await repository.findOne({ where: { id: item.id } });
          // Menu items have both publishedName and description fields
          updateData = {
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
            description: item.description,
            descriptionEn: item.descriptionEn,
            descriptionVi: item.descriptionVi,
          };
          break;
        case TranslateType.MENU_ITEM_OPTION_GROUP:
          repository = this.menuItemOptionGroupRepository;
          entity = await repository.findOne({ where: { id: item.id } });
          // Menu item option groups only have publishedName fields
          updateData = {
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          };
          break;
        default:
          continue;
      }

      if (!entity) {
        throw new NotFoundException(`Entity with id ${item.id} and type ${item.type} not found`);
      }

      // Verify restaurant access
      await this.restaurantAccessService.verifyAccessRestaurant(entity.restaurantId, ownerId);

      // Remove undefined values
      Object.keys(updateData).forEach((key) => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        await repository.update(item.id, updateData);
        updatedCount++;
      }
    }

    return { updated: updatedCount };
  }
}
