import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { GeofencingType } from '../../modules/geofencing/geofencing.types';

export function IsValidGeofencingSnapshot(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidGeofencingSnapshot',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const object = args.object as any;
          const type = object.type;
          const snapshot = value;

          if (!snapshot || typeof snapshot !== 'object') {
            return false;
          }

          switch (type) {
            case GeofencingType.POLYGON:
              return validatePolygonSnapshot(snapshot);

            case GeofencingType.CIRCLE:
              return validateCircleSnapshot(snapshot);

            case GeofencingType.RECTANGLE:
              return validateRectangleSnapshot(snapshot);

            default:
              return false;
          }
        },
        defaultMessage(args: ValidationArguments) {
          const object = args.object as any;
          const type = object.type;

          switch (type) {
            case GeofencingType.POLYGON:
              return 'Polygon snapshot must have a valid path array with at least 3 points';

            case GeofencingType.CIRCLE:
              return 'Circle snapshot must have center (lat, lng) and radius (number > 0)';

            case GeofencingType.RECTANGLE:
              return 'Rectangle snapshot must have bounds with south, west, north, east coordinates';

            default:
              return 'Invalid geofencing type or snapshot data';
          }
        },
      },
    });
  };
}

function validatePolygonSnapshot(snapshot: any): boolean {
  if (!snapshot.path || !Array.isArray(snapshot.path)) {
    return false;
  }

  if (snapshot.path.length < 3) {
    return false;
  }

  return snapshot.path.every(
    (point: any) =>
      typeof point === 'object' &&
      typeof point.lat === 'number' &&
      typeof point.lng === 'number' &&
      point.lat >= -90 &&
      point.lat <= 90 &&
      point.lng >= -180 &&
      point.lng <= 180,
  );
}

function validateCircleSnapshot(snapshot: any): boolean {
  if (!snapshot.center || typeof snapshot.center !== 'object') {
    return false;
  }

  if (typeof snapshot.center.lat !== 'number' || typeof snapshot.center.lng !== 'number') {
    return false;
  }

  if (snapshot.center.lat < -90 || snapshot.center.lat > 90) {
    return false;
  }

  if (snapshot.center.lng < -180 || snapshot.center.lng > 180) {
    return false;
  }

  if (typeof snapshot.radius !== 'number' || snapshot.radius <= 0) {
    return false;
  }

  return true;
}

function validateRectangleSnapshot(snapshot: any): boolean {
  if (!snapshot.bounds || typeof snapshot.bounds !== 'object') {
    return false;
  }

  const { south, west, north, east } = snapshot.bounds;

  if (typeof south !== 'number' || typeof west !== 'number' || typeof north !== 'number' || typeof east !== 'number') {
    return false;
  }

  if (south < -90 || south > 90 || north < -90 || north > 90) {
    return false;
  }

  if (west < -180 || west > 180 || east < -180 || east > 180) {
    return false;
  }

  if (south >= north || west >= east) {
    return false;
  }

  return true;
}
