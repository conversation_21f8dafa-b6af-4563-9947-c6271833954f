import { BrandsModule } from '@/modules/brands/brands.module';
import { MenuItemsModule } from '@/modules/menu-items/menu-items.module';
import { MerchantStaffModule } from '@/modules/merchant-staff/merchant-staff.module';
import { RestaurantTagsModule } from '@/modules/restaurant-tags/restaurant-tags.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UserAddressesModule } from '../user-addresses/user-addresses.module';
import { RestaurantsController } from './controllers/restaurants.controller';
import { StaffRestaurantsController } from './controllers/staff-restaurants.controller';
import { UserRestaurantsController } from './controllers/user-restaurants.controller';
import { RestaurantAvailableSchedule } from './entities/restaurant-available-schedule.entity';
import { Restaurant } from './entities/restaurant.entity';
import { RestaurantsService } from './restaurants.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Restaurant, RestaurantAvailableSchedule]),
    BrandsModule,
    RestaurantTagsModule,
    MerchantStaffModule,
    UserAddressesModule,
    MenuItemsModule,
  ],
  controllers: [RestaurantsController, UserRestaurantsController, StaffRestaurantsController],
  providers: [RestaurantsService],
  exports: [RestaurantsService],
})
export class RestaurantsModule {}
