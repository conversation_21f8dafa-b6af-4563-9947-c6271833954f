import { Exclude, Expose } from 'class-transformer';
import { isNil } from 'lodash';
import { Column, Entity, Generated, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MappingMenuMenuSection } from '@/modules/menus/entities/mapping-menu-menu-section.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { ViewType } from '../enums/view-type.enum';
import { MappingMenuSectionMenuItem } from './mapping-menu-section-menu-item.entity';
import { MenuSectionAvailableSchedule } from './menu-section-available-schedule.entity';

@Entity('menu_sections')
@Index(['restaurantId', 'publishedName'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['restaurantId', 'internalName'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['code', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class MenuSection extends BaseEntity {
  @Generated('uuid')
  @Index({ unique: true })
  @Column({ name: 'code', type: 'uuid' })
  code: string;

  @Column({ name: 'internal_name', type: 'varchar' })
  internalName: string;

  @Column({ name: 'published_name', type: 'varchar' })
  publishedName: string;

  @Exclude()
  @Column({ name: 'published_name_en', type: 'varchar', nullable: true })
  publishedNameEn?: string | null;

  @Exclude()
  @Column({ name: 'published_name_vi', type: 'varchar', nullable: true })
  publishedNameVi?: string | null;

  @Column({ name: 'view_type', type: 'enum', enum: ViewType, default: ViewType.GRID })
  viewType: ViewType;

  @OneToMany(() => MenuSectionAvailableSchedule, (schedule) => schedule.menuSection, {
    cascade: true,
  })
  availableSchedule: WrapperType<MenuSectionAvailableSchedule>[];

  @Index()
  @Column({ name: 'schedule_active_at', nullable: true, type: 'timestamptz' })
  scheduleActiveAt?: Date | null;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @Expose()
  get menus() {
    return this.mappingMenus?.map((m) => m.menu).filter((m) => !isNil(m));
  }

  @Exclude()
  @OneToMany(() => MappingMenuMenuSection, (mappingMenu) => mappingMenu.menuSection)
  mappingMenus?: WrapperType<MappingMenuMenuSection>[];

  @Expose()
  get menuItems() {
    return this.mappingMenuItems
      ?.filter((m) => !isNil(m.menuItem))
      ?.map((m) => {
        m.menuItem.position = m.position;
        // Use mapping price if available, otherwise use basePrice
        m.menuItem.price = m.price;
        return m.menuItem;
      })
      .sort((a, b) => a.position - b.position);
  }

  @Exclude()
  @OneToMany(() => MappingMenuSectionMenuItem, (mappingMenuItem) => mappingMenuItem.menuSection)
  mappingMenuItems?: WrapperType<MappingMenuSectionMenuItem>[];

  // field not in database
  position: number;
  // Virtual fields for counts
  itemsCount?: number;
  itemOutOfStockCount?: number;
  itemPrice?: number | null;
}
