import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { GeofencingController } from './controllers/geofencing.controller';
import { Geofencing } from './entities/geofencing.entity';
import { GeofencingService } from './geofencing.service';

@Module({
  imports: [TypeOrmModule.forFeature([Geofencing])],
  controllers: [GeofencingController],
  providers: [GeofencingService],
  exports: [GeofencingService],
})
export class GeofencingModule {}
