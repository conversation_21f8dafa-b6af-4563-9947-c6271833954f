# Cập nhật hệ thống giá (Pricing System Update)

## Tổng quan

Cập nhật này bổ sung khả năng set gi<PERSON> riêng (custom pricing) cho các item và option trong menu system, cho phép flexibility cao hơn trong việc quản lý giá.

## 🔧 Thay đổi API

### 1. Menu Sections API

#### `POST/PUT /menu-sections` - Create/Update Menu Section

**Thay đổi trong request body:**

```typescript
// BEFORE
menuItemIds: {
  id: string;
  position: number;
}
[];

// AFTER
menuItemIds: {
  id: string;
  position: number;
  price: number | null; // null = sử dụng basePrice của item
}
[];
```

**Ví dụ:**

```json
{
  "menuItemIds": [
    {
      "id": "item-123",
      "position": 1,
      "price": 50000 // Giá custom cho item này trong section
    },
    {
      "id": "item-456",
      "position": 2,
      "price": null // Sử dụng basePrice của item
    }
  ]
}
```

### 2. Menu Item Option Groups API

#### `POST/PUT /menu-item-option-groups` - Create/Update Menu Item Option Group

**Thay đổi trong request body:**

```typescript
// BEFORE
menuItemOptionIds: {
  id: string;
  position: number;
}
[];

// AFTER
menuItemOptionIds: {
  id: string;
  position: number;
  price: number | null; // null = sử dụng basePrice của option
}
[];
```

**Ví dụ:**

```json
{
  "menuItemOptionIds": [
    {
      "id": "option-123",
      "position": 1,
      "price": 15000 // Giá custom cho option này trong group
    },
    {
      "id": "option-456",
      "position": 2,
      "price": null // Sử dụng basePrice của option
    }
  ]
}
```

### 3. Menu Items and Options API

#### `POST/PUT /menu-items-and-options` - Create/Update Menu Item and Options

**Thay đổi major trong request body:**

```typescript
// BEFORE
menuSectionIds: string[]
menuItemOptionGroupIds: string[]

// AFTER - Tùy thuộc vào type
```

**Khi `type = "item"`:**

```json
{
  "menuSectionIds": [
    {
      "sectionId": "section-123",
      "price": 45000 // Giá custom cho item này trong section
    },
    {
      "sectionId": "section-456",
      "price": null // Sử dụng basePrice của item
    }
  ],
  "menuItemOptionGroupsOfItem": [
    {
      "id": "group-123",
      "position": 1
    },
    {
      "id": "group-456",
      "position": 2
    }
  ]
  // Bỏ trường: menuItemOptionGroupIds
}
```

**Khi `type = "option"`:**

```json
{
  "menuItemOptionGroupsOfOption": [
    {
      "groupId": "group-123",
      "price": 12000 // Giá custom cho option này trong group
    },
    {
      "groupId": "group-456",
      "price": null // Sử dụng basePrice của option
    }
  ]
  // Bỏ trường: menuItemOptionGroupIds
  // Bỏ trường: menuSectionIds (option không thuộc trực tiếp vào section)
}
```

### 4. Cart API

#### `POST /user/carts/add` - Add Item to Cart

**Thay đổi trong request body:**

```typescript
// BEFORE
{
  restaurantId: string;
  menuItemId: string;
  amount: number;
  note?: string;
  groupOptions: GroupOption[];
}

// AFTER
{
  restaurantId: string;
  menuSectionId: string;  // REQUIRED: Section mà item thuộc về
  menuItemId: string;
  amount: number;
  note?: string;
  groupOptions: GroupOption[];
}
```

**Ví dụ:**

```json
{
  "restaurantId": "restaurant-123",
  "menuSectionId": "section-456",  // Bắt buộc để xác định giá đúng
  "menuItemId": "item-123",
  "amount": 2,
  "note": "Không cay",
  "groupOptions": [...]
}
```

## 📊 Thay đổi Data Structure

### 1. MenuItem & MenuItemOption Response

```typescript
// MenuItem
{
  id: string;
  name: string;
  basePrice: number; // Giá gốc
  price: number | null; // Giá custom trong section này (ưu tiên)
  // ... other fields
}

// MenuItemOption
{
  id: string;
  name: string;
  basePrice: number; // Giá gốc
  price: number | null; // Giá custom trong group này (ưu tiên)
  // ... other fields
}
```

**Logic tính giá:**

- Nếu `price !== null` → sử dụng `price`
- Nếu `price === null` → sử dụng `basePrice`

### 2. (khi join với Section/Group)

**MenuItem với MenuSections:**

```typescript
// MenuItem với array menuSections
{
  id: string;
  name: string;
  basePrice: number;
  menuSections: [
    {
      id: string;
      name: string;
      itemPrice: number | null;  // Giá custom của item trong section này
      // ... other fields
    }
  ]
  // ... other fields
}
```

**MenuItemOption với MenuItemOptionGroups:**

```typescript
// MenuItemOption với array menuItemOptionGroups
{
  id: string;
  name: string;
  basePrice: number;
  menuItemOptionGroups: [
    {
      id: string;
      name: string;
      optionPrice: number | null;  // Giá custom của option trong group này
      // ... other fields
    }
  ]
  // ... other fields
}
```

**Logic tính giá:**

- Giá item trong section = `section.itemPrice` (nếu !== null) hoặc `item.basePrice`
- Giá option trong group = `group.optionPrice` (nếu !== null) hoặc `option.basePrice`

## ⚡ Impact & Breaking Changes

### Breaking Changes

1. **Menu Sections & Option Groups API:** Thêm field `price: number | null` vào object structure của `menuItemIds` và `menuItemOptionIds`
2. **Menu Items and Options API:**
   - `menuSectionIds` từ `string[]` → `{sectionId: string, price: number | null}[]`
   - Bỏ field `menuItemOptionGroupIds`
   - Thêm field `menuItemOptionGroupsOfItem` (cho type = "item") và `menuItemOptionGroupsOfOption` (cho type = "option")
3. **Cart API:** Thêm field bắt buộc `menuSectionId` khi add item
4. **Response Structure:** Bổ sung các field mới trong response (`price`, `itemPrice`, `optionPrice`)

## 🔍 Use Cases

### 1. Giá theo Section

```
Item "Phở Bò" có basePrice = 50,000đ
- Trong "Section A": price = 45,000đ (giảm giá)
- Trong "Section B": price = null (dùng basePrice = 50,000đ)
```

### 2. Giá Option theo Group

```
Option "Thêm thịt" có basePrice = 15,000đ
- Trong "Group Phở": price = 12,000đ (giảm giá cho combo)
- Trong "Group Bún": price = null (dùng basePrice = 15,000đ)
```

### 3. Cart Context

```
User add "Phở Bò" với:
- menuItemId: "item-123"
- menuSectionId: "section-456"
→ Hệ thống sẽ lấy giá của item trong section đó (custom price nếu có, ngược lại basePrice)
```
