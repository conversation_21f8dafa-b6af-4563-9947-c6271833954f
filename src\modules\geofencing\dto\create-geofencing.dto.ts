import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsU<PERSON>D,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';

import { IsValidGeofencingSnapshot } from '@/common/validators/geofencing-snapshot.validator';
import { ApiProperty } from '@nestjs/swagger';

import { GeofencingType } from '../geofencing.types';

class PointDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 40.7128,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Type(() => Number)
  lat: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: -74.006,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Type(() => Number)
  lng: number;
}

class CenterDto extends PointDto {}

class BoundsDto {
  @ApiProperty({
    description: 'Southern boundary latitude',
    example: 40.7128,
  })
  @IsNumber()
  @Type(() => Number)
  south: number;

  @ApiProperty({
    description: 'Western boundary longitude',
    example: -74.006,
  })
  @IsNumber()
  @Type(() => Number)
  west: number;

  @ApiProperty({
    description: 'Northern boundary latitude',
    example: 40.7128,
  })
  @IsNumber()
  @Type(() => Number)
  north: number;

  @ApiProperty({
    description: 'Eastern boundary longitude',
    example: -74.006,
  })
  @IsNumber()
  @Type(() => Number)
  east: number;
}

class SnapshotDto {
  @ApiProperty({
    description: 'Array of points defining the path (for polygon)',
    type: [PointDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PointDto)
  path?: PointDto[];

  @ApiProperty({
    description: 'Center point (for circle)',
    type: CenterDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CenterDto)
  center?: CenterDto;

  @ApiProperty({
    description: 'Radius in meters (for circle)',
    example: 1000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  radius?: number;

  @ApiProperty({
    description: 'Bounding box (for rectangle)',
    type: BoundsDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BoundsDto)
  bounds?: BoundsDto;
}

export class GeometryDataDto {
  @ApiProperty({
    description: 'Geometric data snapshot containing path, center, radius, or bounds',
    type: SnapshotDto,
  })
  @IsValidGeofencingSnapshot({
    message: 'Snapshot data must match the geometry type requirements',
  })
  @ValidateNested()
  @Type(() => SnapshotDto)
  snapshot: SnapshotDto;

  @ApiProperty({
    description: 'Type of geofencing geometry',
    enum: GeofencingType,
    example: GeofencingType.POLYGON,
  })
  @IsEnum(GeofencingType)
  type: GeofencingType;

  @ApiProperty({
    description: 'Fill color for the geofencing area',
    example: '#FF0000',
  })
  @IsString()
  @IsNotEmpty()
  fillColor: string;
}

export class GeofencingItemDto {
  @ApiProperty({
    description: 'Unique identifier for the geofencing area',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Name of the geofencing area',
    example: 'Downtown Delivery Zone',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of the geofencing area',
    example: 'Delivery zone covering downtown area',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Shipping fee for this area in VND',
    example: 15000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  shippingFee: number;

  @ApiProperty({
    description: 'Geometric data for the geofencing area',
    type: GeometryDataDto,
  })
  @ValidateNested()
  @Type(() => GeometryDataDto)
  geometryData: GeometryDataDto;
}

export class CreateBulkGeofencingDto {
  @ApiProperty({
    description: 'Restaurant ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  restaurantId: string;

  @ApiProperty({
    description: 'Array of geofencing areas to create',
    type: [GeofencingItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GeofencingItemDto)
  geofencingAreas: GeofencingItemDto[];
}

export class GetCheapestGeofencingDto {
  @ApiProperty({
    description: 'User address ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  addressId: string;

  @ApiProperty({
    description: 'Restaurant ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  restaurantId: string;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  userId?: string;
}
