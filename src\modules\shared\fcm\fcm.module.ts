import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';
import { User } from '@/modules/users/entities/user.entity';
import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { StaffFcmToken } from './entities/staff-fcm-token.entity';
import { UserFcmToken } from './entities/user-fcm-token.entity';
import { FCMService } from './fcm.service';

@Global()
@Module({
  imports: [ConfigModule, TypeOrmModule.forFeature([UserFcmToken, StaffFcmToken, User, MerchantStaff])],
  providers: [FCMService],
  exports: [FCMService],
})
export class FCMModule {}
