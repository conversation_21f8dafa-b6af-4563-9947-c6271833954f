import { OtpModule } from '@/modules/otp/otp.module';
import { UserAddressesModule } from '@/modules/user-addresses/user-addresses.module';
import { UsersModule } from '@/modules/users/users.module';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AuthUserFakeController } from './auth-user-fake.controller';
import { AuthUserController } from './auth-user.controller';
import { AuthUserService } from './auth-user.service';
import { OnboardingService } from './onboarding.service';
import { ProfileUpdateService } from './profile-update.service';
import { UserJwtStrategy } from './strategies/user-jwt.strategy';

@Module({
  imports: [
    UsersModule,
    UserAddressesModule,
    OtpModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('auth.userJwtAccessSecret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.accessTokenExpiresIn'),
        },
      }),
    }),
  ],
  controllers: [AuthUserController, ...(process.env.ENABLE_FAKE_USER === 'true' ? [AuthUserFakeController] : [])],
  providers: [AuthUserService, OnboardingService, ProfileUpdateService, UserJwtStrategy],
  exports: [AuthUserService, OnboardingService, ProfileUpdateService],
})
export class AuthUserModule {}
