import cookieParser from 'cookie-parser';
import express, { json, urlencoded } from 'express';
import morgan from 'morgan';
import { join } from 'path';

import { AppModule } from '@/app.module';
import { AppMode } from '@/common/enums/event-pattern.enum';
import { HttpExceptionFilter } from '@/common/filters/exception-filter.filter';
import { setupSwagger } from '@/setup-swagger';
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory, Reflector } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ExpressAdapter } from '@nestjs/platform-express';

async function bootstrap() {
  const server = express();
  const app = await NestFactory.create(AppModule, new ExpressAdapter(server));
  server.set('trust proxy', true);

  app.use(morgan(':method :url :status :res[content-length] - :response-time ms'));

  const configService = app.get(ConfigService);
  const appMode = configService.get<string>('app.appMode');

  // Conditionally create Redis microservice
  if (appMode !== AppMode.JOB) {
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.REDIS,
      options: {
        host: configService.get<string>('redis.host', 'localhost'),
        port: configService.get<number>('redis.port', 6379),
        password: configService.get<string>('redis.password'),
        retryAttempts: 5,
        retryDelay: 3000,
      },
    });
  }

  app.useGlobalFilters(new HttpExceptionFilter());
  app.enableCors({
    origin: [/^http:\/\/localhost(:\d+)?$/, /^https?:\/\/.*\.anhbeo\.com$/],
    credentials: true, // Required to allow cookies or authorization headers
  });

  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ limit: '50mb', extended: true }));
  app.use(cookieParser());

  // Serve static files for geofencing test nếu bật env
  if (configService.get<boolean>('app.enablePublicFolder')) {
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const publicPath = isDevelopment ? join(process.cwd(), 'src/public') : join(process.cwd(), 'dist/public');
    app.use('/public', express.static(publicPath));
  }

  // Enable shutdown hooks
  app.enableShutdownHooks();

  // Global Validation Pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that do not have any decorators
      forbidNonWhitelisted: true, // Throw errors if non-whitelisted values are provided
      transform: true, // Automatically transform payloads to DTO instances
      transformOptions: {
        enableImplicitConversion: true, // Convert primitive types automatically
      },
    }),
  );

  // Global Serialization Interceptor
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  setupSwagger(app, configService);

  // Get port from ConfigService, fallback to 3000
  const port = configService.get<number>('app.port', 3000);
  await app.listen(port);
  console.log(`Application with Mode "${appMode}" is running on: ${await app.getUrl()}`);
  console.log(`Swagger documentation available at: ${await app.getUrl()}/docs`); // Log Swagger URL
  // Start microservice
  if (appMode !== AppMode.JOB) {
    await app.startAllMicroservices();
    console.log('🚀 Redis microservice is listening...');
  }
}

bootstrap().catch((err) => {
  console.error('❌ Failed to start application:', err);
  process.exit(1);
});
