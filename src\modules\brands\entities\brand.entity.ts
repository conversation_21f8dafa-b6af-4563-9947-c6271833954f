import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantAccount } from '@/modules/merchant-accounts/entities/merchant-account.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('brands')
export class Brand extends BaseEntity {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ name: 'merchant_account_id', type: 'uuid' })
  merchantAccountId: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'code', type: 'varchar' })
  code: string;

  @Index()
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @ManyToOne(() => MerchantAccount, (merchantAccount) => merchantAccount.brands)
  @JoinColumn({ name: 'merchant_account_id' })
  merchantAccount: WrapperType<MerchantAccount>;

  @OneToMany(() => Restaurant, (restaurant) => restaurant.brand)
  restaurants: Restaurant[];
}
