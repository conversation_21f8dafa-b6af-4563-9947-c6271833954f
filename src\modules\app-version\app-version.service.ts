import { Repository } from 'typeorm';

import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { GetAppVersionDto } from './dto/get-app-version.dto';
import { UpdateAppVersionDto } from './dto/update-app-version.dto';
import { AppVersion } from './entities/app-version.entity';

@Injectable()
export class AppVersionService {
  constructor(
    @InjectRepository(AppVersion)
    private readonly appVersionRepository: Repository<AppVersion>,
  ) {}

  async getCurrentVersion(query: GetAppVersionDto): Promise<AppVersion | null> {
    const queryBuilder = this.appVersionRepository
      .createQueryBuilder('appVersion')
      .where('appVersion.isActive = :isActive', { isActive: true });

    if (query.platform) {
      queryBuilder.andWhere('appVersion.platform = :platform', { platform: query.platform });
    }

    queryBuilder.orderBy('appVersion.releasedAt', 'DESC').addOrderBy('appVersion.createdAt', 'DESC');

    return queryBuilder.getOne();
  }

  async getAllVersions(platform?: 'ios' | 'android'): Promise<AppVersion[]> {
    const queryBuilder = this.appVersionRepository.createQueryBuilder('appVersion');

    if (platform) {
      queryBuilder.where('appVersion.platform = :platform', { platform });
    }

    queryBuilder
      .orderBy('appVersion.platform', 'ASC')
      .addOrderBy('appVersion.releasedAt', 'DESC')
      .addOrderBy('appVersion.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  async createVersion(dto: UpdateAppVersionDto): Promise<AppVersion> {
    // Check if version already exists for this platform
    const existingVersion = await this.appVersionRepository.findOne({
      where: {
        version: dto.version,
        platform: dto.platform,
      },
    });

    if (existingVersion) {
      throw new ConflictException(`Version ${dto.version} already exists for platform ${dto.platform}`);
    }

    const appVersion = this.appVersionRepository.create({
      ...dto,
      releasedAt: new Date(),
    });

    return this.appVersionRepository.save(appVersion);
  }

  async updateVersion(id: string, dto: Partial<UpdateAppVersionDto>): Promise<AppVersion> {
    const appVersion = await this.appVersionRepository.findOne({
      where: { id },
    });

    if (!appVersion) {
      throw new NotFoundException(`App version with ID ${id} not found`);
    }

    // If updating version or platform, check for conflicts
    if (dto.version || dto.platform) {
      const version = dto.version || appVersion.version;
      const platform = dto.platform || appVersion.platform;

      const existingVersion = await this.appVersionRepository.findOne({
        where: {
          version,
          platform,
        },
      });

      if (existingVersion && existingVersion.id !== id) {
        throw new ConflictException(`Version ${version} already exists for platform ${platform}`);
      }
    }

    Object.assign(appVersion, dto);
    return this.appVersionRepository.save(appVersion);
  }

  async deleteVersion(id: string): Promise<void> {
    const result = await this.appVersionRepository.softDelete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`App version with ID ${id} not found`);
    }
  }

  async getVersionById(id: string): Promise<AppVersion> {
    const appVersion = await this.appVersionRepository.findOne({
      where: { id },
    });

    if (!appVersion) {
      throw new NotFoundException(`App version with ID ${id} not found`);
    }

    return appVersion;
  }
}
