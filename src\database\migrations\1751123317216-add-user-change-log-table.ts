import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserChangeLogTable1751123317216 implements MigrationInterface {
  name = 'AddUserChangeLogTable1751123317216';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_change_logs" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "change_type" character varying NOT NULL, "before_data" jsonb NOT NULL, "after_data" jsonb NOT NULL, "changed_by_user_id" uuid, "user_agent" character varying, "ip_address" inet, CONSTRAINT "PK_3dca0574708c1a352d9ed487678" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b32ebc7389b31249cec583b622" ON "user_change_logs" ("user_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_09df5343f9d0f4e24c320f4e47" ON "user_change_logs" ("user_id", "change_type") `,
    );
    await queryRunner.query(
      `ALTER TABLE "user_change_logs" ADD CONSTRAINT "FK_b32ebc7389b31249cec583b6229" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_change_logs" DROP CONSTRAINT "FK_b32ebc7389b31249cec583b6229"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_09df5343f9d0f4e24c320f4e47"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b32ebc7389b31249cec583b622"`);
    await queryRunner.query(`DROP TABLE "user_change_logs"`);
  }
}
