import { Exclude } from 'class-transformer';
import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { ABRole } from '../enums/admin-role.enum';

@Entity()
export class Admin extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  email: string;

  @Exclude()
  @Column({ type: 'varchar' })
  password: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({
    type: 'enum',
    enum: ABRole,
    default: ABRole.ADMIN,
  })
  role: ABRole;

  @Column({ default: false })
  banned: boolean;
}
