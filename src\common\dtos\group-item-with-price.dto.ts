import { Type } from 'class-transformer';
import { IsInt, IsUUID, Min, ValidateIf } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class GroupItemWithPriceDto {
  @ApiProperty({ description: 'Group ID' })
  @IsUUID()
  groupId: string;

  @ApiProperty({
    description: 'Custom price for this item in this group (required, can be null)',
    required: true,
    nullable: true,
  })
  @ValidateIf((o) => o.price !== null)
  @IsInt()
  @Min(0)
  @Type(() => Number)
  price: number | null;
}
