import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTotalOrdersSoldToRestaurant1752264125753 implements MigrationInterface {
  name = 'AddTotalOrdersSoldToRestaurant1752264125753';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "total_orders_sold" integer NOT NULL DEFAULT '0'`);
    await queryRunner.query(`CREATE INDEX "IDX_93448093be47625ee44fef5240" ON "restaurants" ("total_orders_sold") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_93448093be47625ee44fef5240"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "total_orders_sold"`);
  }
}
