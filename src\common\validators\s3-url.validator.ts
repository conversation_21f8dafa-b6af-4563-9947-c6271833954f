import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { FolderType } from '@/modules/upload/upload.constants';

const domain = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com`;

/**
 * Validates that the URL is a valid S3 URL and matches the expected folder structure
 * @param folderType The expected folder type that the URL should belong to
 * @param validationOptions Additional validation options
 * @returns PropertyDecorator
 */
export function IsValidS3Url(folderType?: FolderType | FolderType[], validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidS3Url',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [folderType],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Allow empty values (use @IsNotEmpty if required)

          if (Array.isArray(value)) {
            return value.every((url) => validateSingleUrl(url, args));
          }

          return validateSingleUrl(value, args);
        },
        defaultMessage(args: ValidationArguments) {
          const [expectedFolders] = args.constraints;
          const folderText = Array.isArray(expectedFolders)
            ? expectedFolders.join(', ')
            : expectedFolders || 'any valid folder';
          return `${args.property} must be a valid S3 URL from folder(s): ${folderText}`;
        },
      },
    });
  };
}

function validateSingleUrl(url: string, args: ValidationArguments): boolean {
  const [expectedFolders] = args.constraints;

  // Basic S3 URL validation
  const s3UrlPattern = new RegExp(`^${domain}/(.+)$`);
  const match = url.match(s3UrlPattern);

  if (!match) {
    return false;
  }

  const path = match[1];

  // If no folder constraint is specified, any valid S3 URL is acceptable
  if (!expectedFolders) {
    return true;
  }

  // Extract folder from path (first part before first slash)
  const folder = path.split('/')[0];

  // Check if folder matches expected folder(s)
  if (Array.isArray(expectedFolders)) {
    return expectedFolders.includes(folder as FolderType);
  }

  return folder === expectedFolders;
}

/**
 * Validates that the URL array contains valid S3 URLs and matches the expected folder structure
 * @param folderType The expected folder type that URLs should belong to
 * @param validationOptions Additional validation options
 * @returns PropertyDecorator
 */
export function IsValidS3UrlArray(folderType?: FolderType | FolderType[], validationOptions?: ValidationOptions) {
  return IsValidS3Url(folderType, {
    ...validationOptions,
    message:
      validationOptions?.message ||
      `Each URL must be a valid S3 URL from folder(s): ${Array.isArray(folderType) ? folderType.join(', ') : folderType || 'any valid folder'}`,
  });
}
