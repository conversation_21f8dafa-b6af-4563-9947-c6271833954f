import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateRestaurantReviewReplyDto } from '../dto/create-restaurant-review-reply.dto';
import { ListStaffRestaurantReviewDto } from '../dto/list-staff-restaurant-review.dto';
import { RestaurantReviewReply } from '../entities/restaurant-review-reply.entity';
import { RestaurantReview } from '../entities/restaurant-review.entity';
import { RestaurantReviewsService } from '../restaurant-reviews.service';

@ApiTags('(Merchant User) Restaurant Reviews')
@Controller('merchant-user/restaurants/reviews')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MerchantUserRestaurantReviewsController {
  constructor(private readonly restaurantReviewsService: RestaurantReviewsService) {}

  @Post('reply')
  @ApiOperation({ summary: 'Reply to a restaurant review' })
  async createRestaurantReviewReplyByMerchantUser(
    @Body() createReplyDto: CreateRestaurantReviewReplyDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<RestaurantReviewReply> {
    return this.restaurantReviewsService.createRestaurantReviewReplyByMerchantUser(createReplyDto, ownerId);
  }

  @Get()
  @ApiOperation({ summary: 'Get restaurant reviews for staff' })
  async findRestaurantReviewsForMerchantUser(
    @Query() query: ListStaffRestaurantReviewDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<RestaurantReview>> {
    return this.restaurantReviewsService.findRestaurantReviewsForMerchantUser(query, ownerId);
  }
}
