import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSectionIdToOriginalOrderItem1751601236408 implements MigrationInterface {
  name = 'AddSectionIdToOriginalOrderItem1751601236408';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items_original" ADD "menu_section_id" uuid NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_items_original" DROP COLUMN "menu_section_id"`);
  }
}
