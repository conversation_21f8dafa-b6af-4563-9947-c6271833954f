import { IsBoolean, IsEnum, IsInt, IsOptional, IsString, IsUrl, Min } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class UpdateAppVersionDto {
  @ApiProperty({ description: 'Version string (e.g., 1.0.0)', example: '1.0.0' })
  @IsString()
  version: string;

  @ApiProperty({ description: 'Build number', example: 1 })
  @IsInt()
  @Min(1)
  buildNumber: number;

  @ApiProperty({ description: 'Platform', enum: ['ios', 'android'], example: 'android' })
  @IsEnum(['ios', 'android'])
  platform: 'ios' | 'android';

  @ApiProperty({ description: 'Whether this version requires force update', example: false, required: false })
  @IsOptional()
  @IsBoolean()
  isForceUpdate?: boolean;

  @ApiProperty({ description: 'Whether this version is active', example: true, required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Release notes', example: 'Bug fixes and improvements', required: false })
  @IsOptional()
  @IsString()
  releaseNotes?: string;

  @ApiProperty({
    description: 'Download URL',
    example: 'https://play.google.com/store/apps/details?id=com.example.app',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  downloadUrl?: string;
}
