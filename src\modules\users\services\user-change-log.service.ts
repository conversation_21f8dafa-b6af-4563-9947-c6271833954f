import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ChangeType, UserChangeLog } from '../entities/user-change-log.entity';

export interface CreateChangeLogData {
  userId: string;
  changeType: ChangeType;
  beforeData: Record<string, any>;
  afterData: Record<string, any>;
  changedByUserId?: string;
  userAgent?: string;
  ipAddress?: string;
}

@Injectable()
export class UserChangeLogService {
  constructor(
    @InjectRepository(UserChangeLog)
    private userChangeLogRepository: Repository<UserChangeLog>,
  ) {}

  async createChangeLog(data: CreateChangeLogData): Promise<UserChangeLog> {
    const changeLog = this.userChangeLogRepository.create({
      userId: data.userId,
      changeType: data.changeType,
      beforeData: data.beforeData,
      afterData: data.afterData,
      changedByUserId: data.changedByUserId || data.userId,
      userAgent: data.userAgent,
      ipAddress: data.ipAddress,
    });

    return this.userChangeLogRepository.save(changeLog);
  }

  async getUserChangeLogs(userId: string, limit = 50): Promise<UserChangeLog[]> {
    return this.userChangeLogRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getChangeLogsByType(userId: string, changeType: ChangeType): Promise<UserChangeLog[]> {
    return this.userChangeLogRepository.find({
      where: { userId, changeType },
      order: { createdAt: 'DESC' },
    });
  }
}
