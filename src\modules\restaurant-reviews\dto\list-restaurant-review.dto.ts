import { Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, <PERSON>, Min } from 'class-validator';

import { PaginationSortDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListRestaurantReviewDto extends PaginationSortDto {
  @ApiProperty({ required: false, default: 'createdAt', enum: ['createdAt', 'rating'] })
  @IsOptional()
  @IsIn(['createdAt', 'rating'])
  sortBy: string = 'createdAt';

  @ApiProperty({ description: 'Filter by rating', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  @Type(() => Number)
  rating?: number;
}
