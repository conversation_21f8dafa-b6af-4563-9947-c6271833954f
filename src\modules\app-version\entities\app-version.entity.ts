import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

@Entity('app_versions')
@Index(['platform', 'version'], { unique: true, where: 'deleted_at IS NULL' })
export class AppVersion extends BaseEntity {
  @Column({ name: 'version', type: 'varchar' })
  version: string;

  @Column({ name: 'build_number', type: 'integer' })
  buildNumber: number;

  @Index()
  @Column({ name: 'platform', type: 'enum', enum: ['ios', 'android'], default: 'android' })
  platform: 'ios' | 'android';

  @Column({ name: 'is_force_update', type: 'boolean', default: false })
  isForceUpdate: boolean;

  @Index()
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'release_notes', type: 'text', nullable: true })
  releaseNotes?: string | null;

  @Column({ name: 'download_url', type: 'varchar', nullable: true })
  downloadUrl?: string | null;

  @Index()
  @Column({ name: 'released_at', type: 'timestamptz', nullable: true })
  releasedAt?: Date | null;
}
