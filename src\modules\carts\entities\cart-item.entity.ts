import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuSection } from '@/modules/menu-sections/entities/menu-section.entity';

import { CartItemOption } from './cart-item-option.entity';
import { Cart } from './cart.entity';

@Entity('cart_items')
export class CartItem extends BaseEntity {
  @Column({ name: 'cart_id', type: 'uuid' })
  cartId: string;

  @Column({ name: 'menu_item_id', type: 'uuid' })
  menuItemId: string;

  @Column({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @Column({ type: 'integer' })
  amount: number;

  @Column({ name: 'note', nullable: true, type: 'text' })
  note?: string | null;

  @ManyToOne(() => Cart, (cart) => cart.cartItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'cart_id' })
  cart: WrapperType<Cart>;

  @ManyToOne(() => MenuItem)
  @JoinColumn({ name: 'menu_item_id' })
  menuItem?: WrapperType<MenuItem>;

  @ManyToOne(() => MenuSection)
  @JoinColumn({ name: 'menu_section_id' })
  menuSection?: WrapperType<MenuSection>;

  @OneToMany(() => CartItemOption, (cartItemOption) => cartItemOption.cartItem)
  cartItemOptions: WrapperType<CartItemOption>[];
}
