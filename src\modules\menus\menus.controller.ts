import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckMenuNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-menu-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuDto } from './dtos/create-menu.dto';
import { DuplicateMenuDto } from './dtos/duplicate-menu.dto';
import { ListMenuDto } from './dtos/list-menu.dto';
import { UpdateMenuDto } from './dtos/update-menu.dto';
import { Menu } from './entities/menu.entity';
import { MenusService } from './menus.service';

@ApiTags('Menus')
@Controller('menus')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenusController {
  constructor(private readonly menusService: MenusService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu name exists for restaurant' })
  async checkExists(@Body() dto: CheckMenuNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menusService.checkNameExists(dto.restaurantId, dto.name, dto.excludeId);
    return { exists };
  }

  @Post()
  create(@Body() createMenuDto: CreateMenuDto): Promise<Menu> {
    return this.menusService.create(createMenuDto, null);
  }

  @Get()
  findAll(@Query() listMenuDto: ListMenuDto, @UserMerchantId() ownerId: string | null): Promise<Pagination<Menu>> {
    return this.menusService.findAll(listMenuDto, ownerId);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Menu> {
    return this.menusService.activate(id, ownerId);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Menu> {
    return this.menusService.deactivate(id, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Menu> {
    return this.menusService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuDto: UpdateMenuDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Menu> {
    return this.menusService.update(id, updateMenuDto, ownerId);
  }

  @Delete(':id')
  delete(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null) {
    return this.menusService.delete(id, ownerId);
  }

  @Post('duplicate')
  duplicate(@Body() duplicateMenuDto: DuplicateMenuDto, @UserMerchantId() ownerId: string | null) {
    return this.menusService.duplicate(duplicateMenuDto, ownerId);
  }
}
